<!-- 风研团队管理首页 -->
<!--#include virtual ="include/header.html"-->
<link href="resource/css/audit-welcome.css?v=6.5" rel="stylesheet"/>
<link href="resource/css/ordinary-welcome.css?v=6.5" rel="stylesheet"/>
<link href="resource/css/lead.css?v=6.5" rel="stylesheet"/>
<link href="resource/css/style-red.css?v=6.5" id="linkid" rel="stylesheet" type="text/css"/>
<link href="resource/css/new-style.css?v=6.5" media="all" rel="stylesheet" type="text/css"/>
<link href="resource/css/jacobforwecomejob.css?v=6.5" rel="stylesheet" type="text/css"/>
<style>
    p,span{
        text-wrap-mode: wrap !important;
    }
    .layui-layer-content-my{
        background: none;
        padding: 0;
        max-width: 60%;
        box-shadow: 0 1px 6px rgba(0,0,0,.1);
        position: relative;
        line-height: 22px;
        min-width: 12px;
        font-size: 12px;
        _float: left;
        border-radius: 4px;
        color: #fff;
        /*max-width: 60%;*/
        position: absolute;
    }
    .layui-table-tips-main-my{
        max-height: 150px;
        padding: 8px 15px;
        padding-right: 18px;
        font-size: 14px;
        overflow-y: auto;
        background-color: #fff;
        color: #333;
        border-color: #dcdcdc;
        border-width: 1px;
        border-style: solid;
        margin-top: -10px;
    }
    .layui-table-tips-main-my .icon-guanbishixin{
        position: absolute;
        right: -6px;
        top: -14px;
        color:#666;
        font-size: 24px;
        cursor: pointer
    }
    .btn-no-drop{
        cursor: no-drop !important;
        background: #ddd !important;
        border-color: #ddd !important;
        color: #fff !important;
    }
    .layui-table-view .layui-table th {
        border: 0px !important;
    }

    .layui-table-header {
        border-color: #E6E8EA !important;
    }

    .layui-table thead tr {
        background: #F4F8FC !important;
    }

    .layui-table-cell {
        height: 40px;
        line-height: 40px;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333333;
        padding:0 5px;
    }

    .layui-border-box.layui-table-view {
        border: 0px;
    }

    .layui-table-view .layui-table td {
        border: 0px;
        border-bottom: 1px solid #E6E8EA;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
    }

    .layui-table tr td .layui-table-cell {
        height: 40px;
        line-height: 40px;
    }

    .top-change-lists {
        background: #ffffff;
        border-radius: 4px;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        margin: 8px 0px 0px 0px;
        padding: 0px 20px;
    }

    .top-change-lists .top-change-list-one {
        margin-top: 4px;
        height: 32px;
        line-height: 32px;
        background: #fff;
        border: 1px solid #c20000;
        border-radius: 2px;
        padding: 0px 14px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #c20000;
        margin-right: 14px;
        cursor: pointer;
    }

    .top-change-lists .top-change-list-one.actived {
        background: #c20000;
        color: #fff;
    }

    .layui-card-content-box {
        height: calc(100% - 46px);
        padding: 0px 8px;
        overflow-y: auto;
        overflow-x: hidden;
    }

    .serach-table .search-table-contions {
        display: flex;
        align-items: center;
        margin: 20px 0px;
    }

    .serach-table .search-table-contions .search-table-contion-one {
        display: flex;
        align-items: center;
        margin-right: 60px;
    }

    .serach-table .search-table-contions .search-table-contion-one:last-child {
        margin-right: 0px;
    }

    .serach-table .search-table-contions .search-table-contion-one .search-table-contion-one-left {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
        padding-right: 12px;
    }

    .serach-table .search-table-contions .search-table-contion-one .search-table-contion-one-right input {
        height: 32px;
        line-height: 32px;
        background: #ffffff;
        border-radius: 2px;
        border: 1px solid #dcdee0;
        width: 200px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333;
        padding-left: 12px;
        padding-right: 5px;
        box-sizing: border-box;
    }

    .search-table-contion-btns {
        flex: 1;
        display: flex;
    }

    .flex-1 {
        flex: 1;
    }

    .search-table-contion-btns .search-table-contion-btns-search {
        width: 54px;
        height: 26px;
        line-height: 26px;
        background: #c20000;
        border-radius: 2px;
        border: 1px solid #c20000;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        cursor: pointer;
    }

    .search-table-contion-btns .search-table-contion-btns-search:last-child {
        margin-right: 0px;
    }

    .search-table-contion-btns .search-table-contion-btns-search.add {
        background: #fff;
        color: #c20000;
    }

    .bottom-box {
        display: flex;
        align-items: center;
    }

    .one-click-box {
        display: flex;
        align-items: center;
        flex-direction: column;
        margin-top: 14px;
        cursor: pointer;
    }

    .one-click-box .one-click-box-top-img {
        width: 70px;
        height: 70px;
        background: #F5F5F5;
        border-radius: 50%;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }

    .one-click-box .one-click-box-top-img img { /* width:55px;*/
    }
    .one-click-box.opacity .one-click-box-top-img{
        cursor: no-drop;
    }
    .one-click-box.opacity  {
        opacity: 0.8;
        cursor: no-drop;
    }

    .peoples-box {
        padding: 12px;
    }

    .peoples-box .peoples-box-title {
        display: flex;
        align-items: center;
    }

    .peoples-box .peoples-box-title .people-box-point {
        width: 7px;
        height: 7px;
        background: #c20000;
        border-radius: 50%;
        margin-right: 8px;
    }

    .peoples-box .peoples-box-title {
        font-size: 15px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333333;
        margin-bottom: 10px;
    }

    .peoples-box .people-contents {
        min-height: 194px;
        border: 1px solid #E4E4E4;
        width: 100%;
        padding: 12px;
    }

    .peoples-box .people-contents .people-contents-one {
        display: flex;
        margin-bottom: 10px;
    }

    .peoples-box .people-contents .people-contents-one-left {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #919495;
        width: 100px;
        padding-top: 5px;
    }

    .peoples-box .people-contents .people-contents-one-rights {
        display: flex;
        flex-wrap: wrap;
        flex: 1;
    }

    .peoples-box .people-contents .people-contents-one-rights .people-contents-one-rights-one {
        height: 32px;
        background: #F6F6F6;
        border: 1px solid #E8E8E8;
        margin-right: 10px;
        display: flex;
        align-items: center;
        width: calc((100% - 10px) / 2);
        padding: 0px 6px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
        box-sizing: border-box;
        margin-bottom: 8px;
    }

    .peoples-box .people-contents .people-contents-one-rights .people-contents-one-rights-one:nth-child(2n) {
        margin-right: 0px;
    }

    .peoples-box .people-contents .people-contents-one-rights .people-contents-one-rights-one .name-lan {
        height: 21px;
        background: #64AAE8;
        border-radius: 1px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FFFFFF;
        margin-right: 10px;
        padding: 0px 5px;
        width: 50px;
        line-height: 21px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-align: center;
    }

    .people-contents-one-zerk {
        width: 26px;
        height: 26px;
        background: #FFAC3E;
        border-radius: 4px;
        font-size: 15px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FFFFFF;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px;
        position: relative;
        top: 4px;
    }

    .people-contents-one-zerk.zerk-2 {
        background-color: #5AA9FA;
    }

    .no_datas {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        width: 100%;
        height: 100%;
    }

    .no_datas img {
        width: 140px;
        height: 140px;
    }

    .no_datas .no_datas_text {
        font-size: 13px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #A2A6A7;
        margin-top: 13px;
    }

    .layui-card {
        margin-bottom: 8px;
    }

    .name-black {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    @media only screen and (max-width: 1680px) {
        .serach-table .search-table-contions .search-table-contion-one {
            margin-right: 20px;
        }

        .no_datas img {
            width: 120px;
            height: 120px;
        }

        .peoples-box .people-contents .people-contents-one-left {
            font-size: 13px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #919495;
            width: 80px;
            padding-top: 5px;
        }

        .peoples-box .people-contents .people-contents-one-rights .people-contents-one-rights-one {
            height: 32px;
            background: #F6F6F6;
            border: 1px solid #E8E8E8;
            margin-right: 5px;
            display: flex;
            align-items: center;
            width: calc((100% - 5px) / 2);
            padding: 0px 5px;
            font-size: 13px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #333333;
            box-sizing: border-box;
            margin-bottom: 5px;
        }

        .peoples-box .people-contents .people-contents-one-rights .people-contents-one-rights-one .name-lan {
            height: 21px;
            background: #64AAE8;
            border-radius: 1px;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #FFFFFF;
            margin-right: 10px;
            padding: 0px 5px;
            width: 40px;
            line-height: 21px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            text-align: center;
        }
    }

    @media only screen and (max-width: 1440px) {
        .serach-table .search-table-contions .search-table-contion-one .search-table-contion-one-left {
            font-size: 12px;
            padding-right: 6px;
        }

        .serach-table .search-table-contions .search-table-contion-one {
            margin-right: 10px;
        }

        .no_datas img {
            width: 115px;
            height: 115px;
        }

        .no_datas .no_datas_text {
            font-size: 12px;
        }

        .serach-table .search-table-contions {
            margin: 10px 0px;
        }

        .main-list-header:before {
            top: 9px;
        }

        .serach-table .search-table-contions .search-table-contion-one .search-table-contion-one-right input {
            width: 158px;
        }

        .search-table-contion-btns .search-table-contion-btns-search {
            width: 60px;
            height: 28px;
            line-height: 28px;
            font-size: 12px;
        }

        .peoples-box .people-contents .people-contents-one-left {
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #919495;
            width: 65px;
            padding-top: 5px;
        }

        .peoples-box .people-contents .people-contents-one-rights .people-contents-one-rights-one {
            height: 32px;
            background: #F6F6F6;
            border: 1px solid #E8E8E8;
            margin-right: 4px;
            display: flex;
            align-items: center;
            width: calc((100% - 4px) / 2);
            padding: 0px 5px;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #333333;
            box-sizing: border-box;
            margin-bottom: 5px;
        }

        .peoples-box .people-contents .people-contents-one-rights .people-contents-one-rights-one .name-lan {
            height: 21px;
            background: #64AAE8;
            border-radius: 1px;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #FFFFFF;
            margin-right: 5px;
            padding: 0px 5px;
            width: 30px;
            line-height: 21px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            text-align: center;
        }

        .peoples-box {
            padding: 8px 5px;
        }
    }

    .order-4-table {
        width: 100%;
        padding: 8px 0;
        box-sizing: border-box;
    }

    .order-4-table .order-4-table-header {
        width: 100%;
        height: 44px;
        background: #F4F8FC;
        border-bottom: 1px solid #E6EAEE;
    }

    .order-4-table .order-4-table-th {
        float: left;
        width: 50%;
        height: 44px;
        line-height: 44px;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: bold;
        box-sizing: border-box;
        color: #333333;
    }

    .order-4-table-th1 {
        padding-left: 38px;
        width: 50% !important;
    }

    .order-4-table-th2 {
        padding-left: 50px;
    }

    .order-4-table .order-4-table-body {
        height: 240px;
        overflow-y: auto;
        width: 100%;
    }
    .order-4-table .order-4-table-tr{border-bottom:1px solid #E6EAEE;width:100%;}
    .order-4-table .order-4-table-td{float:left;height:44px;line-height:44px;font-size:14px;font-family:PingFangSC-Medium,PingFang SC;font-weight:500;box-sizing:border-box;color:#333333;}
    .task-title-style .order-button-update{width:74px;text-align:right;}
    .layui-table tr td .layui-table-cell > div {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .teamEdit {
        float: right;
        background: #c20000;
        padding: 0 10px;
        height: 28px;
        margin-top: 4px;
        margin-right: 4px;
        line-height: 28px;
        color: #fff;
        cursor: pointer;
        font-size: 12px;
        border-radius: 2px;
        font-weight: inherit;
    }

    .list-form {
        margin-top: 10px;
    }

    .list-form .layui-input-block {
        margin-left: 70px;
        min-height: 36px;
    }

    .list-form .layui-form-label {
        width: 70px;
        padding-right: 8px !important;
    }

    .width20 {
        width: 20%;
        float: left;
        box-sizing: border-box;
    }
    .layui-form-select dl {
        min-width: 197px;
    }

</style>

<body class="new-audit-fluid">
<div class="layui-fluid larry-wrapper">
    <div class="tab-menu">
        <a data-parent="true" href="javascript:" id="tableMenu" style="color: #c00; text-decoration: underline">
            <i class="iconfont" data-icon=""></i>
        </a>
    </div>
    <div class="layui-row">
        <div class="top-change-lists" id="changeLists">

        </div>

        <div class="layui-row layui-col-space10" style="margin-top: 8px">
            <div style="float: left; width: 75%; box-sizing: border-box;padding-top: 0px;">
                <div class="layui-card" style="height: 705px;">
                    <div class="layui-card-header main-list-header task-title-style">
                        研究专题
                    </div>
                    <div class="layui-card-content-box">
                        <div class="layui-col-md12 layui-col-sm12">
                            <div class="serach-table">
                                <div class="layui-row list-form">
                                    <form class="float-left layui-form" style="width: calc(100% - 220px);">
                                        <div class="width20">
                                            <div class="layui-form-item layui-form-item-sm">
                                                <label class="layui-form-label">专题</label>
                                                <div class="layui-input-block">
                                                    <input class="layui-input" id="specialSubject" name="specialSubject" placeholder="请输入" type="text"/>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="width20">
                                            <div class="layui-form-item layui-form-item-sm">
                                                <label class="layui-form-label">子专题</label>
                                                <div class="layui-input-block">
                                                    <input class="layui-input" id="subtopic" name="subtopic" placeholder="请输入" type="text"/>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="width20">
                                            <div class="layui-form-item layui-form-item-sm">
                                                <label class="layui-form-label">分析人员</label>
                                                <div class="layui-input-block">
                                                    <input class="layui-input" id="analysts" name="analysts" placeholder="请输入" type="text"/>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="width20">
                                            <div class="layui-form-item layui-form-item-sm">
                                                <label class="layui-form-label">聚焦风险</label>
                                                <div class="layui-input-block">
                                                    <input class="layui-input" id="initialFocusRisk" name="initialFocusRisk" placeholder="请输入" type="text"/>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="width20">
                                            <div class="layui-form-item layui-form-item-sm">
                                                <label class="layui-form-label">成果状态</label>
                                                    <div class="layui-input-block new-unselect" id="relateDept" lay-filter="relateDept">
                                                    </div>
<!--                                                    <input class="layui-input" id="initialFocusRisk" name="initialFocusRisk" placeholder="请输入" type="text"/>-->

                                            </div>
                                        </div>
                                    </form>
                                    <div class="search-table-contion-btns float-right" style="width: 220px;">
                                        <div class="flex-1"></div>
                                        <div class="search-table-contion-btns-search" onclick="initAchieveList()">
                                            查询
                                        </div>
                                        <div class="search-table-contion-btns-search add btn-no-drop"  id="addResearch" onclick="addLeaderManager(1)">
                                            新增
                                        </div>
                                        <div class="search-table-contion-btns-search add btn-no-drop" id="windResearch" onclick="addLeaderManager(2)" style="width: 78px;">
                                            远程风研
                                        </div>
                                    </div>
                                </div>
                                <table class="layui-table jq-even" id="table_1" lay-filter="table_1"></table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bottom-box">
                    <div class="layui-col-md4 layui-col-sm4" style="padding-right:4px">
                        <div class="layui-card" style="height: 390px;">
                            <div class="layui-card-header main-list-header task-title-style">
                                <span class="width-span ie-header">风研会议</span>

                                <!--更多-->
                                <button class="order-button order-button-more" style="margin-right: 10px" onclick="meetingQuery()">
                                    <i class="iconfont index-icon">&#xe689;</i>
                                </button>
                                <button class="order-button order-button-update"  layui-type="1" >
                                    <i class="iconfont index-icon">&#xe631;</i>
                                </button>
                            </div>
                            <div class="layui-card-content-box">
                                <div class="layui-col-md12 layui-col-sm12" style="width:100%;height:100%">
                                    <div class="order-4-table">
                                    <div class="order-4-table-header layui-row">
                                        <div class="order-4-table-th order-4-table-th1">会议标题</div>
                                        <div class="order-4-table-th order-4-table-th2">会议日期</div>
                                    </div>
                                    <div class="order-4-table-body" id="group_table">

                                    </div>

                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md4 layui-col-sm4" style="padding-left:4px;padding-right:4px">
                        <div class="layui-card" style="height: 386px;">
                            <div class="layui-card-header main-list-header task-title-style">
                                案例分析
                            </div>
                            <div class="layui-card-content-box">
                                <div class="layui-col-md12 layui-col-sm12" style="width:100%;height:100%">
                                    <div class="no_datas">
                                        <img src="resource/images/bigdata/nodata.png" alt="">
                                        <div class="no_datas_text">暂无数据</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md4 layui-col-sm4" style="padding-left:4px;">
                        <div class="layui-card" style="height: 386px">
                            <div class="layui-card-header main-list-header task-title-style">
                                制度解读
                            </div>
                            <div class="layui-card-content-box">
                                <div class="layui-col-md12 layui-col-sm12" style="width:100%;height:100%">
                                    <div class="no_datas">
                                        <img src="resource/images/bigdata/nodata.png" alt="">
                                        <div class="no_datas_text">暂无数据</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div style="float: left; width: 25%; box-sizing: border-box;padding-top: 0px;">
                <div class="layui-card" style="background: transparent;padding: 0px;">
                    <div class="rightone pl20" style="height:141px">
                        <div class="trtTitle">选择账期区间</div>
                        <div class="trm layui-form radioTime">
                            <span class="trmTitle">快速选择</span>
                            <input lay-filter="quickchoose" name="quickchoose" title="去年" type="radio" value="2">
                            <input lay-filter="quickchoose" name="quickchoose" title="本年" type="radio" value="1">
                            <input lay-filter="quickchoose" name="quickchoose" title="自定义" type="radio" value="0">
                        </div>
                        <div class="trb ">
                            <span class="trbTitle">自定义</span>
                            <i class="iconfont">&#xe719;</i>
                            <input class="layui-input" disabled="disabled" id="payment" lay-filter="payment"
                                   name="payment"
                                   placeholder="">
                        </div>
                    </div>
                </div>
                <div class="layui-card" style="height: 292px;">
                    <div class="layui-card-header main-list-header task-title-style">
                        中控
                    </div>
                    <div class="layui-card-content-box">
                        <div class="layui-col-md12 layui-col-sm12">
                            <div class="layui-col-md4 layui-col-sm4" onclick="riskOpen()">
                                <div class="one-click-box ">
                                    <div class="one-click-box-top-img">
                                        <img src="resource/images/bigdata/yjhy.png" alt="">
                                    </div>
                                    <div class="one-click-box-top-text">风研会议</div>
                                </div>
                            </div>
                            <div class="layui-col-md4 layui-col-sm4">
                                <div class="one-click-box opacity">
                                    <div class="one-click-box-top-img">
                                        <img src="resource/images/bigdata/sjdt.png" alt="">
                                    </div>
                                    <div class="one-click-box-top-text">数据打通</div>
                                </div>
                            </div>
                            <div class="layui-col-md4 layui-col-sm4">

                                <div class="one-click-box opacity">
                                    <div class="one-click-box-top-img">
                                        <img src="resource/images/bigdata/fycg.png" alt="">
                                    </div>
                                    <div class="one-click-box-top-text">风研成果</div>
                                </div>
                            </div>
                        </div>

                        <div class="layui-col-md12 layui-col-sm12">
                            <div class="layui-col-md4 layui-col-sm4">
                                <div class="one-click-box opacity">
                                    <div class="one-click-box-top-img">
                                        <img src="resource/images/bigdata/zdjd.png" alt="">
                                    </div>
                                    <div class="one-click-box-top-text">制度解读</div>
                                </div>
                            </div>
                            <div class="layui-col-md4 layui-col-sm4">
                                <div class="one-click-box opacity">
                                    <div class="one-click-box-top-img">
                                        <img src="resource/images/bigdata/alfx.png" alt="">
                                    </div>
                                    <div class="one-click-box-top-text">案例解析</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-card" style="height: 652px;">
                    <div class="layui-card-header main-list-header task-title-style">
                        <span>团队管理</span>
                        <span class="teamEdit" onclick="teamEdit()" style="display:none">团队编辑</span>
                    </div>
                    <div class="layui-card-content-box">
                        <div class="layui-col-md12 layui-col-sm12">
                            <div class="peoples-box">
                                <div class="peoples-box-title">
                                    <div class="people-box-point"></div>
                                    <div class="people-box-title-text">团队人员信息</div>
                                </div>
                                <div class="people-contents">
                                    <div class="people-contents-one">
                                        <div class="people-contents-one-left">团队负责人</div>
                                        <div class="people-contents-one-rights" id="loadPeoples1">

                                        </div>
                                    </div>
                                    <div class="people-contents-one">
                                        <div class="people-contents-one-left">团队成员</div>
                                        <div class="people-contents-one-rights" id="loadPeoples2">

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="peoples-box">
                                <div class="peoples-box-title">
                                    <div class="people-box-point"></div>
                                    <div class="people-box-title-text">团队业务领域</div>
                                </div>
                                <div class="people-contents">
                                    <div class="people-contents-one">
                                        <div class="people-contents-one-left" style="width: 70px;">业务领域</div>
                                        <div class="people-contents-one-zerk">主</div>
                                        <div class="people-contents-one-rights" id="contentszhu">

                                        </div>
                                    </div>
                                    <div class="people-contents-one">
                                        <div class="people-contents-one-left" style="width: 70px;"></div>
                                        <div class="people-contents-one-zerk zerk-2">次</div>
                                        <div class="people-contents-one-rights" id="contentsCi">

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
<div class="layui-layer-content-my" style="display: none;"><div class="layui-table-tips-main-my"><div class="text-left" id="lastRisk"></div><span class="icon iconfont icon-guanbishixin" onclick="closeRisk()"></span></div>

</div>
</body>

<!--#include virtual ="include/version.html"-->
<script src="resource/js/jsencrypt/index.js?v=6.5" type="text/javascript"></script>
<script src="resource/js/jsencrypt/jsencrypt.js?v=6.5" type="text/javascript"></script>
<script src="resource/js/common.js?v=11.04" type="text/javascript"></script>
<script src="resource/js/lib/ueditor/third-party/jquery-ueditor.js?v=6.5" type="text/javascript"></script>
<script src="resource/js/audit/pro/common/common.js?v=6.5" type="text/javascript"></script>
<script src="resource/js/audit/colligate/morder/baseinfo/mobaseinfo.js?v=6.5" type="text/javascript"></script>
<script src="resource/js/echarts/echarts.min.js?v=6.5" type="text/javascript"></script>
<script src="resource/js/audit/online/onlineModel/echartsText/infographic.js?v=6.5" type="text/javascript"></script>
<script src="resource/js/audit/online/onlineModel/echartsText/macarons.js?v=6.5" type="text/javascript"></script>
<script src="resource/js/audit/colligate/morder/common/updateAnimation.js?v=6.5" type="text/javascript"></script>
<script src="resource/js/preDealTimeIndex.js" type="text/javascript"></script>
<script type="text/javascript" src="resource/js/formSelects/formSelects-v4.js"></script>

<script>
    var currentIndex = 0
</script>
<script id="changeListsTpl" type="text/html">
    {{#  layui.each(d, function(index, item){ }}
    <div class="top-change-list-one {{currentIndex==index?'actived':''}}" onclick="tabsClick('{{item.groupId}}',{{index}},'{{item.teamInfoId}}')">
        {{item.groupName}}
    </div>
    {{# }); }}
</script>

<script id="STATUS_TYPE" type="text/html">
    {{# if(d.status == "T") { }}
    <span style="color:#2CB790" title="草稿">草稿</span>
    {{# }}} {{# if(d.status == "D") { }}
    <span style="color:#2CB790" title="作废">作废</span>
    {{# }}} {{# if(d.status == "0") { }}
    <span style="color:#2CB790" title="编辑风研成果">编辑风研成果</span>
    {{# }}} {{# if(d.status == "1") { }}
    <span style="color:#2CB790" title="团队负责人审核">团队负责人审核</span>
    {{# }}} {{# if(d.status == "2" || d.status == "4" || d.status == "7") { }}
    <span style="color:#c20000" title="集团审批完成">集团审批完成</span>
    {{# }}} {{# if(d.status == "3") { }}
    <span style="color:#2CB790" title="集团审批完成">集团审批完成</span>
    {{# }}} {{# if(d.status == "5") { }}
    <span style="color:#FC5915" title="到接口人">到接口人</span>
    {{# }}} {{# if(d.status == "6") { }}
    <span style="color:#2CB790" title="审批中">审批中</span>
    {{# }}}
</script>

<script id="loadGroupMemberTpL" type="text/html">
    {{#  layui.each(d, function(index, item){ }}
    <div class="people-contents-one-rights-one">
        <div class="name-lan" title="{{item.memberProvinceName}}">{{item.memberProvinceName}}</div>
        <div class="name-black" title="{{item.analystPersonName}}">{{item.analystPersonName}}</div>
    </div>
    {{# }); }}
</script>

<script id="loadBusinessFieldTpL" type="text/html">
    {{#  layui.each(d, function(index, item){ }}
    <div class="people-contents-one-rights-one">
        <div class="name-black">{{item.codeText}}</div>
    </div>
    {{# }); }}
</script>
<!--风研会议看板-->
<script id="meeting_content" type="text/html">

      {{#  layui.each(d, function(index, item){ }}
    <div class="order-4-table-tr layui-row">
        <div class="order-4-table-td order-4-table-th1 ovflowHidden" title="{{item.title}}">{{item.title}}</div>
        <div class="order-4-table-td order-4-table-th2 ovflowHidden" title="{{item.meetingDate}}">{{item.meetingDate}}</div>
    </div>
     {{# }); }}
</script>
<script>
    var startTime = '';
    var endTime = '';
    var groupId_ = '', teamInfoId_ = '';
    layui.use(
        [
            "jqform",
            "element",
            "jquery",
            "laytpl",
            "layer",
            "jqfrm",
            "table",
            "laydate",
            "selectPlus",
            "jqbind",
        ],
        function () {
            var tpl = layui.laytpl,
                element = layui.element,
                form = layui.jqform,
                ctx = top.global.ctx,
                jqbind = layui.jqbind,
                $ = layui.jquery,
                table = layui.table,
                laydate = layui.laydate,
                layer = layui.layer,
                selectPlus = layui.selectPlus,
                frm = layui.jqfrm;
            jqbind.init();
            var changeTabsLists = [];
            var datetype = '';
            var payment = '';
            var windResearchType = false;

            // 任务类型 加載
            function initTaskStatuss(type, data) {
               var dictList = [];
                values = [];
                for (var i = 0; i < data.length; i++) {
                    var o = data[i];
                    if(o.code!='4'&&o.code!='T'){
                   //     values.push(o.codeText)
                    }
                    if(o.code!='T'){
                        dictList.push(o)
                    }
                }
                selected = selectPlus.render({
                    el: '#relateDept',
                    data: dictList,
                    values: values,
                    valueName: "codeText",
                    label: ['codeText'],
                    valueSeparator: ","
                });
                form.render();
            }

            //获取任务类型字典
            getDicList('risk_research_status', initTaskStatuss);
            $.ajax({
                <!-- 请求的URL地址 -->
                url: ctx + "/inputResults/querySwitch",
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ }),
                contentType: "application/json;charset=UTF-8",
                success: function (res) {
                    if (res.httpCode == 200) {
                        <!-- 根据返回的开关状态，显示或隐藏提交按钮 -->
                        if(res.data=='true'){
                            windResearchType = true
                            $('#windResearch').removeClass('btn-no-drop')
                            $('#addResearch').addClass('btn-no-drop')
                        }else{
                            windResearchType = false
                            $('#windResearch').addClass('btn-no-drop')
                            $('#addResearch').removeClass('btn-no-drop')
                        }
                    }
                },
                error: function (data) {
                }
            });

            //发起风研会议
            window.riskOpen = function () {
                top.layer.open({
                    type: 2,
                    title: '风研会议',
                    content: 'views/audit/colligate/morder/baseinfo/risk/addRisk.html',
                    area: ['80%', '80%'],
                    success: function (layero, index) {

                    }
                })
            };
            //团队看板：更多
            window.meetingQuery = function () {
                var title = '风研会议查询';
                var meetingDateStart = startTime + "-01-01";
                var meetingDateEnd = endTime + "-12-31";
                var target =
                    '/views/audit/colligate/morder/baseinfo/risk/meetingOne.html?groupId=' + groupId_
                +'&meetingDateStart=' + meetingDateStart
                +'&meetingDateEnd=' + meetingDateEnd;
                $("#tableMenu").data("title", title).data("url", target).click();
            };
            //风研会议看板table
            window.loadMeeting = function () {
                $.ajax({
                    url: ctx + '/bdata/meeting/queryOneRiskMeetingList',
                    data: JSON.stringify({
                        meetingDateStart: startTime + "-01-01",
                        meetingDateEnd: endTime + "-12-31",
                        groupId: groupId_,
                        page: 1,
                        limit: 5
                    }),
                    dataType: "json",
                    type: "POST",
                    contentType: "application/json;charset=UTF-8",
                    success: function (ret) {
                        if (ret.httpCode == 200) {
                            var getTpl = meeting_content.innerHTML,
                                view = document.getElementById('group_table');
                            tpl(getTpl).render(ret.data, function (html) {
                                view.innerHTML = html;
                            });

                        }
                    }
                });

            }
            //独立刷新
            $(".order-button-update").on('click', function () {
                var type = $(this).attr('layui-type');
                if (type == 1) {
                    loadMeeting();
                }
                buttonUpdateAnimation(this);
            });
            //团队编辑
            window.teamEdit = function () {
                top.layer.open({
                    type: 2,
                    title: '团队编辑',
                    content: 'views/audit/colligate/riskResearch/initiatedByTeamLeader/add.html?groupId=' + groupId_,
                    area: ['90%', '90%'],
                    success: function (layero, index) {

                    },
                    end: function () {
                        onloadFunction();
                    }
                })
            };
            preDealTime({
                ctx: ctx,
                funcFlag: "SJYWZT",
                type: 'year',
                range: true,
                format: 'yyyy',
            }, form, laydate);

            layui.form.on('radio(quickchoose)', function (data) {
                datetype = +data.value;
                var monthId;
                if (!datetype) {
                    $('#payment').attr("disabled", false)
                    return
                } else if (datetype == 1) {
                    monthId = (new Date()).getFullYear() + " - " + (new Date()).getFullYear();
                } else if (datetype == 2) {
                    monthId = ((new Date()).getFullYear() - 1) + " - " + ((new Date()).getFullYear() - 1);
                }
                startTime = monthId.split(' - ')[0];
                endTime = monthId.split(' - ')[1];
                $("#payment").val(monthId);
                $('#payment').attr("disabled", true);
            });

            window.onloadFunction = function () {
                var time = $("#payment").val();
                startTime = time.split(" - ")[0];
                endTime = time.split(" - ")[1];
                $.ajax({
                    url: ctx + "/bdata/riskResearchInfo/queryCurrentUserGroupList",
                    type: "POST",
                    dataType: "json",
                    contentType: "application/json;charset=UTF-8",
                    success: function (ret) {
                        if (ret.httpCode == 200) {
                            changeTabsLists = ret.data;
                            if (changeTabsLists && changeTabsLists.length) {
                                groupId_ = changeTabsLists[currentIndex].groupId;
                                teamInfoId_ = changeTabsLists[currentIndex].teamInfoId;
                                lp(tpl, changeTabsLists, $("#changeListsTpl").html(), $('#changeLists'));
                                form.render()
                                //获取任务类型字典
                                getDicList('risk_research_status', initTaskStatus);
                                //团队成员
                                loadGroupMember();
                                //业务领域
                                // loadBusinessField();
                                //风研会议
                                loadMeeting();
                            } else {
                                layer.alert("您没有该功能权限，无法进入！", {icon: 2, title: "提示"}, function (index) {
                                    layer.close(index);

                                    window.parent.close();
                                });
                            }

                        } else {
                            frm.error(ret.string);
                        }
                    },
                    error: function (ret) {
                        frm.error("网络连接失败！");
                    }
                });

            }

            var dictList = []//字典
            // 任务类型 加載
            function initTaskStatus(type, data) {
                dictList = data;
                //查询列表
                initAchieveList();
            }

            window.tabsClick = function (id, index, teamInfoId) {
                groupId_ = id;
                teamInfoId_ = teamInfoId;
                currentIndex = index
                lp(tpl, changeTabsLists, $("#changeListsTpl").html(), $('#changeLists'));
                //查询列表
                initAchieveList();
                //团队成员
                loadGroupMember();
                //风研会议
                loadMeeting();
                form.render()
            }
            var tableData = [];
            //风研成果列表
            window.initAchieveList = function () {
                var specialObj = selected.getChecked();
                var relateDept = specialObj.data;
                var status = [];
                for (var i = 0; i < relateDept.length; i++) {
                    status.push(relateDept[i].code)
                }
                table.render({
                    elem: "#table_1",
                    id: "table_1",
                    url: ctx + "/bdata/riskResearchInfo/queryRiskResearchListByGroupId",
                    where: {
                        groupId: changeTabsLists[currentIndex].groupId,
                        startYear: startTime,
                        endYear: endTime,
                        topicName: $('#specialSubject').val(),
                        subTopicName: $('#subtopic').val(),
                        analysisPersonName: $('#analysts').val(),
                        initialFocusRisk: $('#initialFocusRisk').val(),
                        status:status
                    },
                    page: true,
                    height: "590",
                    cols: [
                        [
                            {
                                title: "风研编号",
                                width: "9%",
                                field: "achievementCode",
                                align: "left",

                            },
                            {
                                title: "专题",
                                width: "9%",
                                field: "topicName",
                                align: "left",

                            },
                            {
                                title: "子专题",
                                align: "left",
                                width: "9%",
                                field: "subTopicName",

                            },
                            {
                                title: "聚焦风险",
                                align: "center",
                                width: "200",
                                style:'text-align:left',
                                field: "initialFocusRiskText",
                                templet: function (d) {
                                    var text = "";
                                    text =
                                        '<div onclick=initialFocusRisk(this,' + d.LAY_TABLE_INDEX +',"initialFocusRisk")>' +d.initialFocusRiskText+'</div>';
                                    return text;
                                },
                            },
                            {
                                title: "分析人员",
                                align: "left",
                                width: "9%",
                                field: "analystPersonName",

                            },
                            {
                                title: "提交日期",
                                align: "center",
                                width: "9%",
                                field: "commitDate",
                            },
                            {
                                title: "状态",
                                align: "center",
                                width: "130",
                                field: "status",
                                templet: function (d) {
                                    if(d.status=='2'||d.status=='4'||d.status=='5'||d.status=='7'){
                                        return   '<span style="color:#c20000" >'+fromatComon(d.status,dictList)+'</span>'
                                    }else{
                                        return   '<span style="color:#2CB790">'+fromatComon(d.status,dictList)+'</span>'
                                    }
                                }
                                // toolbar: "#STATUS_TYPE",
                            },
                            {
                                title: "领导看板",
                                align: "center",
                                width: "9%",
                                field: "status",
                                templet: function (d) {
                                    if (d.status == "4") {
                                        return '<div >展示</div>';
                                    } else {
                                        return '<div >不展示</div>';
                                    }
                                }
                            },
                            {
                                title: "发起人",
                                align: "center",
                                width: "8%",
                                field: "commitUserName",
                            },
                            {
                                title: "上报集团",
                                align: "center",
                                width: "9%",
                                field: "commitGroupStatus",
                                templet: function (d) {
                                    if (d.commitGroupStatus == "1") {
                                        return '<div  title="是">是</div>';
                                    } else if (d.commitGroupStatus == "0") {
                                        return '<div  title="否">否</div>';
                                    } else if (d.commitGroupStatus == "D") {
                                        return '<div  title="作废">作废</div>';
                                    } else if (d.status == "5") {
                                        return '<div title="到集团接口人">到集团接口人</div>';
                                    } else {
                                        return '<div ></div>';
                                    }
                                }
                            },
                            {
                                title: '核查单位',
                                align: 'center',
                                width: '100',
                                field: 'checkProv'
                            },
                            {
                                title: '发现问题',
                                align: 'center',
                                width: '100',
                                field: 'problemNum',
                                templet: function (d) {
                                    return '<div onclick=openFxwtTable("' + d.LAY_INDEX +  '","' +  d.problemNum + '","' + d.id+ '")  title="' + d.problemNum + '" class="text-red cursor">' + d.problemNum + '</div>'
                                }
                            },
                            {
                                field: "operate",
                                title: "操作",
                                align: "center",
                                fixed: 'right',
                                width: "100",
                                templet: function (d) {
                                    if (d.status == 'T') {
                                        // if (d.status == 'T'&&loadCurUser().loginName == d.commitLoginName) {
                                        let text = "";
                                        text =
                                            '<div class="table-flex-j-s">' +
                                            '   <span class="icon iconfont text-red cursor table-btn" title="查看" onclick=detailLeaderManager("' + d.id + '","' + d.buttonType + '")>&#xe651;</span>' +
                                            '   <span class="icon iconfont text-red cursor table-btn" title="编辑" onclick=editLeaderManager("' + d.id + '","' + d.buttonType + '")>&#xe62e;</span>' +
                                            '   <span class="icon iconfont text-red cursor table-btn" title="删除" onclick=deleteById("' + d.id + '")>&#xe6e1;</span>' +
                                            // '   <span class="icon iconfont text-red cursor table-btn" title="查看阶段性成果" onclick=historicalAchievements("' + d.id + '")>&#xe691;</span>' +
                                            '</div>';
                                        return text;
                                    } else {
                                        let text = "";
                                        text =
                                            '<div class="table-flex-j-s">' +
                                            '   <span class="icon iconfont text-red cursor table-btn" title="查看" onclick=detailLeaderManager("' + d.id + '","' + d.buttonType + '")>&#xe651;</span>' +
                                            // '   <span class="icon iconfont text-red cursor table-btn" title="查看阶段性成果" onclick=historicalAchievements("' + d.id + '")>&#xe691;</span>' +
                                            '</div>';
                                        return text;
                                    }
                                },
                            },
                        ],
                    ],
                    done: function (res) {
                        tableData = res.data;
                    },
                });
            };
            //编辑
            window.editLeaderManager = function (id,buttonType) {
                addAndEdit('edit', id,buttonType)
            }

            window.openFxwtTable = function (num,id) {
                    var time = startTime - 1;
                    var scoreStartTime = startTime;
                    if(num>1){
                        top.layer.open({
                            type: 2,
                            title: '发现问题',
                            content: 'views/audit/colligate/morder/baseinfo/fxwt-table.html?startTime=' + time +'&scoreStartTime='+scoreStartTime+ '&endTime=' + endTime  + '&id=' + id + '&types=2',
                            area: ['90%', '80%'],
                            success: function (layero, index) {
                            },
                            end: function () {
                            }
                        });
                    }
                    if(num==1){
                        $.ajax({
                            type: 'post',
                            url: ctx + '/bdata/leaderHomeDetail/queryWenTiDetail',
                            dataType: "json",
                            data: JSON.stringify({
                                startTime: time,
                                scoreStartTime: scoreStartTime,
                                endTime: endTime,
                                type:2,
                                id:id
                            }),
                            contentType: "application/json;charset=UTF-8",
                            success: function (ret) {
                                if (ret.httpCode === 200) {
                                    var data = ret.data;
                                    openFxwtPreView(data[0].ACHIEVEMENT_ID)
                                }
                            }
                        });
                    }
            }

            window.openFxwtPreView = function(problemId){
                var  flag = '2'
                top.layer.open({
                    type: 2,
                    title: '问题详情',
                    content: "views/audit/pro/reform/reformreadrecord/reformTodoTaskBaseQuery.html?problemId="+problemId+"&flag="+flag,
                    area: ['90%', '80%'],
                    success: function (layero, index) {
                    },
                    end: function () {
                    }
                });
            }
            //查看阶段性成果
            window.historicalAchievements = function (id) {
                var indexs = top.layer.open({
                    title: '查看阶段性成果',
                    content:
                        "views/audit/colligate/morder/baseinfo/stageAchievements/historList.html" +
                        "?id=" + id + "&type=1",
                    type: 2,
                    area: ["600px", "80%"],
                    fixed: true,
                    maxmin: false,
                    resize: false,
                    end: function (index, layero) {
                        initAchieveList()
                    }
                });
            }

            //查看聚焦风险
            window.initialFocusRisk = function (obj,index,indexobj) {
                // var indexs = top.layer.open({
                //     title: '聚焦风险',
                //     content:tableData[index].initialFocusRisk,
                //     area: ["80%", "80%"],
                //     fixed: true,
                //     maxmin: false,
                //     resize: false,
                //     end: function (index, layero) {
                //     }
                // });
                $('.layui-layer-content-my').show()
                $('#lastRisk').empty()
                var titles = tableData[index].initialFocusRisk
                $('#lastRisk').append(titles)
                $('.layui-layer-content-my').css('left',$(obj).offset().left).css('top',$(obj).offset().top)

            }

            window.closeRisk = function(){
                $('.layui-layer-content-my').hide()
            }
            //新增
            window.addLeaderManager = function (type) {
                if((type==2&&!windResearchType)||(type==1&&windResearchType)){

                }else{
                    addAndEdit('add','',type)
                }
            }
            //删除
            window.deleteById = function (id) {
                top.layer.confirm('确认删除该条数据？', {icon: 3, title: "提示"}, function (index) {
                    top.layer.close(index);
                    var layerIndex = window.parent.layer.load(99, {shade: [0.4, '#fff']});

                    $.ajax({
                        url: ctx + "/bdata/riskResearchInfo/v2/deleteById", // 更新接口路径
                        type: 'POST',
                        dataType: 'JSON',
                        data: JSON.stringify({
                            riskResearchId: id // 更新入参
                        }),
                        contentType: "application/json;charset=UTF-8",
                        success: function (res) {
                            window.parent.layer.close(layerIndex);
                            if (res.httpCode == 200) {
                                top.layer.msg('删除成功', {icon: 1, time: 2000}, function () {
                                });
                                initAchieveList(); // 假设这是刷新数据的函数
                            } else {
                                frm.validate(res.msg); // 假设这是处理错误信息的函数
                            }
                        },
                        error: function (res) {
                            window.parent.layer.close(layerIndex);
                        }
                    });
                });
            }
            //录入阶段性成果
            window.stageAchievements = function () {
                var title = '录入阶段性成果'
                var indexs = top.layer.open({
                    title: title,
                    content:
                        "views/audit/colligate/morder/baseinfo/stageAchievements/add.html",
                    type: 2,
                    area: ["80%", "80%"],
                    fixed: true,
                    maxmin: false,
                    resize: false,
                    end: function (index, layero) {
                        initAchieveList()
                    }
                });
            }
            window.addAndEdit = function (type,id,buttonType) {
                console.log(buttonType)
                var title = ''
                var ids = id
                if (type == 'add') {
                    title = '新增'
                    ids = '';
                } else {
                    title = '编辑'
                }
                var indexs = top.layer.open({
                    title: title,
                    content:
                        "views/audit/bigdata/remoteDataKanban/riskResearchInput/wind-research-achievements-add-edit-new.html" +
                        "?type=0&id=" + ids +
                        '&groupName='+encodeURIComponent(changeTabsLists[currentIndex].groupName)+
                        '&groupId=' + changeTabsLists[currentIndex].groupId+'&buttonType='+buttonType,
                    type: 2,
                    area: ["80%", "80%"],
                    fixed: true,
                    maxmin: false,
                    resize: false,
                    end: function (index, layero) {
                        initAchieveList()
                    },
                    yes: function (index, layero) {

                    },
                    btn2: function (index, layero) {
                    },
                    success: function (layero, index) {

                    },
                });
            }

            //详情
            window.detailLeaderManager = function (id,buttonType) {
                var indexs = top.layer.open({
                    title: '详情',
                    content:
                        "views/audit/bigdata/remoteDataKanban/riskResearchInput/wind-research-achievements-detail-new.html" +
                        "?id=" + id +'&buttonType='+buttonType,
                    type: 2,
                    area: ["80%", "80%"],
                    fixed: true,
                    maxmin: false,
                    resize: false,
                    yes: function (index, layero) {

                    },
                    btn2: function (index, layero) {

                    },
                    success: function (layero, index) {
                    },
                });
            }
            //团队人员信息
            window.loadGroupMember = function () {
                $.ajax({
                    url: ctx + "/bdata/riskResearchInfo/queryAnalysisPersonsByGroupId",
                    type: "POST",
                    dataType: "json",
                    data: JSON.stringify({
                        groupId: changeTabsLists[currentIndex].groupId
                    }),
                    contentType: "application/json;charset=UTF-8",
                    success: function (ret) {
                        if (ret.httpCode == 200) {
                            var data = ret.data;
                            var leader = [];
                            var member = [];
                            $.each(data, function (i, obj) {
                                if (obj.memberRole == "10008001") {
                                    if (obj.analystPersonPostId == loadCurUserPost().id && teamInfoId_ == '') {
                                        $(".teamEdit").show();
                                    } else {
                                        $('.teamEdit').hide();
                                    }
                                    //组长
                                    leader.push(obj);
                                } else {
                                    member.push(obj);
                                }
                            });
                            lp(tpl, leader, $("#loadGroupMemberTpL").html(), $('#loadPeoples1'));
                            lp(tpl, member, $("#loadGroupMemberTpL").html(), $('#loadPeoples2'));
                            form.render();
                        } else {
                            frm.error(ret.string);
                        }
                    },
                    error: function (ret) {
                        frm.error("网络连接失败！");
                    }
                });
            }
            //团队业务领域
            window.loadBusinessField = function () {
                $.ajax({
                    url: ctx + "/bdata/riskResearchInfo/initDicList",
                    type: "POST",
                    dataType: "json",
                    data: JSON.stringify({
                        groupId: changeTabsLists[currentIndex].groupId
                    }),
                    contentType: "application/json;charset=UTF-8",
                    success: function (ret) {
                        if (ret.httpCode == 200) {
                            var zhu = ret.data.fieldList;
                            var ci = ret.data.fieldList;
                            lp(tpl, zhu, $("#loadBusinessFieldTpL").html(), $('#contentszhu'));
                            lp(tpl, ci, $("#loadBusinessFieldTpL").html(), $('#contentsCi'));
                            form.render();
                        } else {
                            frm.error(ret.string);
                        }
                    },
                    error: function (ret) {
                        frm.error("网络连接失败！");
                    }
                });
            }

        }
    );
</script>
