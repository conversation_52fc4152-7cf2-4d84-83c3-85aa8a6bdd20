<!--#include virtual ="include/header.html"-->
<body>
<!-- 人员选择面板 -->
<div class="layui-fluid larry-wrapper">
    <div class="layui-fluid larry-wrapper" id="userPanel">
        <div class="layui-tab-brief" lay-filter="userTab">
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show" style="top:0;">
                    <div style="padding: 5px;">
                        <div class="layui-row layui-col-space10">
                            <div class="layui-col-xs8">
                                <div class="layui-card">
                                    <div class="layui-card-header">待选择人员</div>
                                    <div class="layui-card-body">
                                        <form class="layui-form layui-form-pane form-conmon form-conmon-more">
                                            <div class="layui-row layui-col-space5">
                                                <div class="layui-col-xs10">
                                                    <div class="layui-form-item layui-form-item-sm">
                                                        <input class="layui-input" id="searchSingleContent"
                                                               name="searchSingleContent" placeholder="支持姓名的模糊查询" type="text"/>
                                                    </div>
                                                </div>
                                                <div class="layui-col-xs2">
                                                    <button class="layui-btn btn-outline layui-btn-normal layui-btn-sm" id="searchSingleBtn"
                                                            onclick="searchUsers()"
                                                            type="button">
                                                        <i class="iconfont search-icon">&#xe60b;</i>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="layui-form-item layui-form-item-sm"
                                                 style="height: 450px; overflow: auto;">
                                                <ul class="ztree" id="allTree"></ul>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-xs4">
                                <div class="layui-card">
                                    <div class="layui-card-header">已选择人员</div>
                                    <div class="layui-card-body" id="checkedAllUser" style="height: 373px;overflow: auto">
                                        <span></span>
                                    </div>
                                </div>
                            </div>
                        </div>


                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script id="checkStaffList" type="text/html">
    {{# layui.each(d, function(index, item){ }}
    <div class="layui-form-item layui-form-item-sm layui-form" style="margin-bottom:4px;">
        <label class="layui-form-label" style="text-align: left">{{item.name}}</label>
        <div class="layui-input-block">
            <select id="select{{index}}" name="select{{index}}" lay-filter="select{{index}}">
                {{# layui.each(selectList, function(indexs, obj){ }}
                <option value="{{obj.code}}" {{item.select==obj.code?'selected':''}}>{{obj.codeText}}</option>
                {{# }); }}
            </select>
        </div>
    </div>
    {{# }); }}
</script>
<!--#include virtual ="include/version.html"-->
<script type="text/javascript">
    var userList = [],
        checkStyle = getUrlParam("checkStyle");
    var accountabilityId = getUrlParam("accountabilityId");
    var checkBelow = getUrlParam("checkBelow");
    // 兼容旧参数名
    var mainId = getUrlParam("mainId") || accountabilityId;
    var selectList = [];
    layui.use(['jqform', 'table', 'laytpl', 'jquery', 'laydate', 'layer', 'jqbind', 'jqztree', 'jqfrm'], function () {
        var $ = layui.jquery,
            ctx = top.global.ctx,
            $ZTree = layui.jqztree,
            layer = layui.layer,
            form = layui.jqform,
            tpl = layui.laytpl,
            frm = layui.jqfrm;

        /* 再审计人员类型*/
        // 显示加载提示
        var dictLoadingIndex = layer.msg('正在加载人员类型...', {
            icon: 16,
            shade: 0.3,
            time: 0 // 不自动关闭
        });

        $.ajax({
            url: ctx + "/index/querySysDicByType",
            dataType: "JSON",
            type: "POST",
            data: {
                type: "accountbility_group_role"
            },
            success: function (ret) {
                layer.close(dictLoadingIndex);
                selectList = ret.data;
            },
            error: function (e) {
                layer.close(dictLoadingIndex);
                layer.msg('加载人员类型失败！', { icon: 2 });
                console.info(e);
            }
        });

        // 搜索用户函数
        window.searchUsers = function() {
            // 禁用搜索按钮，防止重复点击
            $('#searchSingleBtn').prop('disabled', true).addClass('layui-btn-disabled');

            // 调用查询函数
            treeListData1('2');

            // 2秒后重新启用按钮
            setTimeout(function() {
                $('#searchSingleBtn').prop('disabled', false).removeClass('layui-btn-disabled');
            }, 2000);
        };

        window.treeListData1 = function (types) {
            // 显示加载提示
            var loadingMsg = types === '2' ? '正在搜索人员...' : '正在加载人员数据...';
            var userTreeLoadingIndex = layer.msg(loadingMsg, {
                icon: 16,
                shade: 0.3,
                time: 0 // 不自动关闭
            });

            $.ajax({
                url: ctx + "/prj/overallAccountabilitynew/queryAuditUserTree",
                dataType: "json",
                contentType: "application/json;charset=UTF-8",
                type: "POST",
                data: JSON.stringify({
                    accountabilityId: accountabilityId,
                    checkBelow: checkBelow,
                    nodeId: '',
                    userName: $('#searchSingleContent').val()
                }),
                success: function (ret) {
                    layer.close(userTreeLoadingIndex);
                    if (ret) {
                        //单位树形结构
                        var setting = {
                            check: {
                                enable: true,
                                chkStyle: (checkStyle == 'radio') ? "radio" : "checkbox",
                                chkboxType: {
                                    "Y": "",
                                    "N": ""
                                },
                                radioType: "all"
                            },
                            data: {
                                simpleData: {
                                    enable: true
                                }
                            },
                            callback: {
                                onCheck: ztreeOnCheck1,
                                beforeAsync: true
                            },
                            view: {
                                showIcon: false
                            }
                        };
                        var zTree = $.fn.zTree.init($("#allTree"), setting, ret);//渲染单位树形结构
                        if (sessionStorage.getItem('chooseUserList') && sessionStorage.getItem('chooseUserList').length > 0) {
                            init(zTree) //本地存储回显
                        } else {
                            if(types!=='2'){
                                if (zTree.getCheckedNodes() && zTree.getCheckedNodes().length > 0) {
                                userList = zTree.getCheckedNodes()
                                console.log(userList)
                                lp(tpl, zTree.getCheckedNodes(), $("#checkStaffList").html(), $('#checkedAllUser'));
                                form.render();
                            }
                            }

                        }
                    }
                },
                error: function (e) {
                    layer.close(userTreeLoadingIndex);
                    layer.msg('加载人员数据失败！', { icon: 2 });
                    console.info(e);
                }
            });
        };

        //树形结构点击
        window.ztreeOnCheck1 = function (e, treeId, treeNode) {
            if (!treeNode.checked) {//删除
                deleteCompany(treeNode);
            } else {//选中
                addCompany(treeNode);
            }
        };
        //单位删除方法
        window.deleteCompany = function (treeNode) {
            layui.each(userList, function (index, item) {
                if (item.id == treeNode.id) {
                    userList.splice(index, 1);
                }
            });
            checkStaffFunction(userList);
        };
        //从数组中新增单个单位
        window.addCompany = function (treeNode) {
            treeNode.select = "zy";
            treeNode.selectName = "组员";


            for(var i=0; i<userList.length; i++ ){
                if(userList[i].id === treeNode.id){
                    return false
                }
            }

            userList.push(treeNode)
            checkStaffFunction(userList);

        };
        // 数据处理回调函数
        window.filter = function (treeId, parentNode, res) {
            if (res == null || res.length == 0) {
                frm.info("未查询到数据！");
            }
            return res;
        };
        var names = '';
        //下拉选择事件触发
        window.selectFunction = function () {
            layui.each(userList, function (index, item) {
                form.on("select(select" + index + ")", function (data) {
                    userList[index].select = data.value;
                    userList[index].selectName = $(data.elem).find("option:selected").text();
                });
            });

        };
        //渲染数据
        window.checkStaffFunction = function (data) {
            if (data) {
                lp(tpl, data, $("#checkStaffList").html(), $('#checkedAllUser'));
                form.render();
            }
            selectFunction();
        };
        // 初始化加载
        treeListData1();

        // 为搜索输入框添加回车事件
        $('#searchSingleContent').on('keypress', function(e) {
            if (e.which === 13) { // 回车键
                e.preventDefault();
                searchUsers();
            }
        });

        window.getuserNames = function () {
            return userList;
        }
        window.init = function (zTree) {
            if (sessionStorage.getItem('chooseUserList')) {
                var list = JSON.parse(sessionStorage.getItem('chooseUserList'));
                userList = list;
                layui.each(userList, function (index, item) {
                    if(item.select==""){
                        item.select = "B";
                        item.selectName = "组员";
                    }
                });
                checkStaffFunction(userList);
                console.log(userList)
                lp(tpl, list, $("#checkStaffList").html(), $('#checkedAllUser'));
                form.render();
                for (var i = 0; i < list.length; i++) {
                    var node = zTree.getNodeByParam("id", list[i].id);
                    if (node != null) {
                        zTree.checkNode(node, true)
                    }
                }
            }
        }
    });
</script>
</body>
</html>
