<!--项目编号-->
<!--#include virtual ="include/header.html"-->
<link href="resource/css/audit/online/tagmodel/tagIndex.css?v=6.5" rel="stylesheet" type="text/css">
<link href="resource/css/audit/online/tagmodel/tagCommon.css?v=6.5" rel="stylesheet" type="text/css">
<link href="resource/css/number-risk-diagnosis/index.css?v=6.5" rel="stylesheet" type="text/css" />
<style>
    .layui-layer-title {
        text-align: left;
    }
 .new-table .layui-table-view .layui-table td,.new-table .layui-table-view .layui-table th{border-left:1px solid #ddd;border-right:1px solid #ddd;}
.new-style .layui-input-block{margin-left:180px !important;;}
.layui-common-body{background:#fff;}
.layui-form-item{margin-bottom:10px;}
.tab_2 .new-style .layui-input-block{margin-left:180px;}
.tab_2 .new-style .layui-form-label{width:175px;}
.new-style .layui-input-block{margin-left:180px;}
.new-style .layui-form-label{width:110px;}
.layui-search-new.new-style .layui-form-label{padding:0 6px 0 0;}
.ict-header-list{display:inline-block;width:100%;text-align:left;}
.ict-header-li{padding:0 26px;box-sizing:border-box;display:inline-block;cursor:pointer;}
.ict-header-title{font-family:PingFangSC,PingFang SC;font-size:15px;color:#333333;font-style:normal;border-bottom:3px solid #fff;height:42px;}
.ict-header-li.active .ict-header-title{color:#C20000;border-bottom:3px solid #C20000;}
.ict-search-box{width:100%;margin-top:8px;background:#F7F9FA;padding:10px 10px 0 10px;box-sizing:border-box;}
.ict-search-form{width:calc(100% - 150px);float:left;}
.ict-search-btn{width:100px;float:right;}
.model-btn-submit{margin-bottom:10px;}
.search-input input{height:42px;width:450px;}
.search-input{margin-top:15px;}
.search-input .search-btn-right{width:100px;height:42px;border-radius:0 4px 4px 0;background:#C20000;}
.model-btn.model-btn-submit{background-color:#C20000;border:1px solid #C20000;}
.search-input .search-btn-right.search-btn-right-2{margin-left:20px;width:120px;border-radius:4px;}
.search-input .search-btn-right.search-btn-right-2 .search-btn-right-text{padding:0;}
.new-style .layui-input-block .input-p{padding:4px 0;display:inline-block;box-sizing:border-box;}
.search-tip{color:#C20000;padding:20px 0px;padding-left:30px;}
.new-style .layui-form-item.layui-form-item-sm .layui-form-label{width:180px;}
.time-choose{flex:1;display:flex;align-items:center;justify-content:flex-end;}
.time-choose .layui-form-label{width:130px;font-size:14px;color:#333333;}
.time-choose input{border:1px solid #eee;text-align:left;width:240px;}
#projectRiskId{opacity:0;}
.projects-list{display:flex;flex-wrap:wrap;padding:5px 5px 10px 10px;}
.projects-list .project-list-one{width:calc((100% - 24px) / 3);margin-right:12px;border:1px solid #eee;box-sizing:border-box;margin-bottom:12px;}
.projects-list .project-list-one:nth-child(3n){margin-right:0px;}
.projects-list .project-list-one .project-list-title{background:#eee;color:#333333;height:35px;line-height:35px;padding:0px 20px;text-align:center;font-size:15px;font-weight:600;}
.projects-list .project-list-one .project-list-title .iconfont{color:#C20000;margin-left:12px;font-size:20px;cursor:pointer;font-weight:normal;}
.tips{position:relative;}
.project-content{padding:12px;height:130px;overflow-y:auto;box-sizing:border-box;}
.project-content .content-one{display:flex;min-height:35px;line-height:35px;}
.project-content .content-one .one-left{width:60%;text-align:left;color:#333333;font-size:15px;}
/*.project-content .content-one .one-right{flex:1;padding-left:15px;color:#666666;font-size:15px;font-weight:600; text-align: left;}*/
/*.others-tip{position:absolute;padding:12px;z-index:8888;display:none;}*/
/*.tip-one{display:flex;}*/
/*.tip-one .tip-left{width:80px;font-size:14px;color:#333;text-align:right;font-weight: bold}*/
    .others-tip{position:absolute;border:1px solid #eee;padding:12px;left:34px;bottom:0px;width:380px;z-index:8888;background-color:#fff;-webkit-background-clip:content;border-radius:4px;box-shadow:1px 1px 50px rgba(0,0,0,.3);display:none;}
    .tip-one{display:flex;}
    .tip-one .tip-left{width:80px;font-size:14px;color:#333;font-weight:bold;text-align:right;}

    .tip-one .tip-right{font-size:14px;color:#333;font-weight:400;padding-left:10px;text-align:left;flex:1;}
.others-tip .icofont{font-family:'iconfont';position:absolute;right:3px;top:-6px;font-size:18px;color:#C20000;cursor:pointer;}

</style>

<body>
    <div class="layui-common-body layui-row">
        <div class="tab-menu">
            <a data-parent="true" href="javascript:" id="tableMenu" style="color: #c00;text-decoration:underline;">
                <i class="iconfont" data-icon=""></i>
            </a>
        </div>
        <div class="layui-row layui-col-md12 layui-col-sm12 layui-col-lg12">
            <div class="layui-row">
                <div class="layui-common-box">
                    <div class="layui-row layui-common-card" style="padding-top: 4px;min-height: 750px;">

                        <!--项目编号-使用-->
                        <div class="tab_1 tab_box">
                            <div class="search-input">
                                <input id="projectNumber" placeholder="请输入项目编号" type="text" />
                                <div class="search-btn-right" onclick="searchClick()">
                                    <span class="icon iconfont icon-chaxun"></span>
                                    <span class="search-btn-right-text">搜索</span>
                                </div>
                            </div>

                            <!--<div class="search-tip">项目存在以下风险：突击计收、推迟计收、人为跨省延长链条、先冲收年底前再计收。</div>-->
                        </div>

                        <!--项目编号-使用-->
                        <div class="tab_1 tab_box">
                            <div class="layui-common-header">
                                <div class="layui-common-card-header">
                                    <div class="layui-card-header-title">基本信息（金额单位：元）</div>
                                    <div class="time-choose">
                                        <label class="layui-form-label color">分析账期</label>
                                        <input class="layui-input" id="date2" readonly type="text">
                                    </div>
                                    <!--<div class="float-right">
                                        金额单位：元
                                    </div>-->
                                </div>
                            </div>
                            <div class="layui-card-body main-list-body layui-row" style="padding-top:10px;">
                                <div class="layui-form new-style layui-row" id="tab_2_data">

                                </div>
                            </div>
                            <div class="layui-common-header">
                                <div class="layui-common-card-header" style="display: flex;">
                                    <div class="layui-card-header-title">计收金额信息（金额单位：元）</div>
                                    <div class="time-choose">
                                        <label class="layui-form-label color">诊断账期</label>
                                        <input class="layui-input" id="date1" readonly type="text">
                                    </div>
                                </div>


                            </div>
                            <div id="projectRiskId" class="layui-card-body main-list-body">
                                <div class="layui-form new-table">
                                    <table class="layui-table jq-even" id="index_table" lay-filter="indexTable"></table>
                                </div>
                            </div>

                            <div class="layui-common-header">
                                <div class="layui-common-card-header" style="display: flex;">
                                    <div class="layui-card-header-title">项目风险信息（金额单位：元）</div>
                                </div>
                            </div>

                            <div class="projects-list" id="projectsList">
                              
                            </div>

                            <div class="layui-common-header">
                                <div class="layui-common-card-header" style="display: flex;">
                                    <div class="layui-card-header-title">项目合同信息</div>
                                </div>
                            </div>

                            <div class="layui-card-body main-list-body layui-row" style="padding-top:10px;">
                                <div class="layui-form new-table layui-row">
                                    <table class="layui-table jq-even" id="risk_profile_table"  lay-filter="risk_profile_table"></table>
                                </div>
                            </div>

                        </div>
                    </div>

                </div>
            </div>
        </div>

    </div>
    <!--#include virtual ="include/version.html"-->
    <script src="resource/js/jquery/jquery.js" type="text/javascript"></script>
    <script src="resource/js/echarts/echarts.min.js?v=6.5" type="text/javascript"></script>

    <script id="project-list" type="text/html">
        {{# layui.each(d, function(index, item){ }}

        <div class="project-list-one">
            <div class="project-list-title">
                <span>{{item.title}}</span>
                <span class="tips">
                    <span class="icon iconfont icon-bangzhu" onclick="openInfos({{index}})">

                    
                    </span>
                    <div class="others-tip" id="ohters-tip-{{index}}">
                        <span class="icon icofont icon-guanbi1" onclick="closeInfos({{index}})"></span>
                        <div class="tip-one">
                            <div class="tip-left">风险描述</div>
                            <div class="tip-right">{{item.riskDescription}}</div>
                        </div>
        
                        <div class="tip-one">
                            <div class="tip-left">风险影响</div>
                            <div class="tip-right">{{item.riskImpact}}</div>
                        </div>
                       
                       
                    </div>
                </span>
                

               
            </div>
            <div class="project-content">
                {{# layui.each(item.riskFieldVos, function(index2, item2){ }}
                <div class="content-one">
                    <div class="one-left">{{item2.fieldName}}</div>
                    <div class="one-right">{{item2.fieldValue}}</div>
                </div>
                {{# }); }}
               
            </div>


           

        </div>
        {{# }); }}
    </script>


    <script id="select-sub-tpl" type="text/html">
    <option value="">--请选择--</option>
    {{# layui.each(d.list, function(index, item){ }}
    <option value="{{item.CODE}}">{{item.CODETEXT}}</option>
    {{# }); }}
</script>
    <script id="select-tpl" type="text/html">
    <option value="">--请选择--</option>
    {{# layui.each(d.list, function(index, item){ }}
    <option value="{{item.code}}">{{item.codeText}}</option>
    {{# }); }}
</script>
    <script id="toolbar" type="text/html">
        <a class="table-btn" lay-event="detail" title="详情">
            <i class="iconfont">&#xe651;</i>
        </a>
    </script>
    <!--基本信息-->
    <script id="tab2data-tpl" type="text/html">
        <div class="float-left layui-col-md6 layui-col-sm4 layui-col-lg4">
            <div class="layui-form-item layui-form-item-sm">
                <label class="layui-form-label color">账期:</label>
                <div class="layui-input-block">
                    <span class="layui-input ovflowHidden" title="{{d.periodName||''}}">{{d.periodName||''}}</span>
                </div>
            </div>
        </div>
        <div class="float-left layui-col-md6 layui-col-sm4 layui-col-lg4">
            <div class="layui-form-item layui-form-item-sm">
                <label class="layui-form-label color">公司名称:</label>
                <div class="layui-input-block">
                    <span class="layui-input ovflowHidden" title="{{d.comName||''}}">{{d.comName||''}}</span>
                </div>
            </div>
        </div>
    <div class="float-left layui-col-md6 layui-col-sm4 layui-col-lg4">
        <div class="layui-form-item layui-form-item-sm">
            <label class="layui-form-label color">项目编号:</label>
            <div class="layui-input-block">
                <span class="layui-input ovflowHidden" title="{{d.projectNumber||''}}">{{d.projectNumber||''}}</span>
            </div>
        </div>
    </div>
    <div class="float-left layui-col-md6 layui-col-sm4 layui-col-lg4">
        <div class="layui-form-item layui-form-item-sm">
            <label class="layui-form-label color">项目名称:</label>
            <div class="layui-input-block">
                <span class="layui-input ovflowHidden" title="{{d.projectName||''}}">{{d.projectName||''}}</span>
            </div>
        </div>
    </div>
    <div class="float-left layui-col-md6 layui-col-sm4 layui-col-lg4">
        <div class="layui-form-item layui-form-item-sm">
            <label class="layui-form-label color">客户名称:</label>
            <div class="layui-input-block">
                <span class="layui-input ovflowHidden" title="{{d.customerName||''}}">{{d.customerName||''}}</span>
            </div>
        </div>
    </div>

    <div class="float-left layui-col-md6 layui-col-sm4 layui-col-lg4">
        <div class="layui-form-item layui-form-item-sm">
            <label class="layui-form-label color">项目状态:</label>
            <div class="layui-input-block">
                <span class="layui-input ovflowHidden" title="{{d.projectStatus||''}}">{{d.projectStatus||''}}</span>
            </div>
        </div>
    </div>
    <div class="float-left layui-col-md6 layui-col-sm4 layui-col-lg4">
        <div class="layui-form-item layui-form-item-sm">
            <label class="layui-form-label color">立项日期:</label>
            <div class="layui-input-block">
                <span class="layui-input ovflowHidden" title="{{d.projectDate||''}}">{{d.projectDate||''}}</span>
            </div>
        </div>
    </div>
    <div class="float-left layui-col-md6 layui-col-sm4 layui-col-lg4">
        <div class="layui-form-item layui-form-item-sm">
            <label class="layui-form-label color">项目计收类型名称:</label>
            <div class="layui-input-block">
                <span class="layui-input ovflowHidden" title="{{d.incomeTypeName||''}}">{{d.incomeTypeName||''}}</span>
            </div>
        </div>
    </div>
    <div class="float-left layui-col-md6 layui-col-sm4 layui-col-lg4">
        <div class="layui-form-item layui-form-item-sm">
            <label class="layui-form-label color">产品线名称:</label>
            <div class="layui-input-block">
                <span class="layui-input ovflowHidden" title="{{d.productLineName||''}}">{{d.productLineName||''}}</span>
            </div>
        </div>
    </div>
    <div class="float-left layui-col-md6 layui-col-sm4 layui-col-lg4">
        <div class="layui-form-item layui-form-item-sm">
            <label class="layui-form-label color">主产品:</label>
            <div class="layui-input-block">
                <span class="layui-input ovflowHidden" title="{{d.productCode1Name||''}}">{{d.productCode1Name||''}}</span>
            </div>
        </div>
    </div>
    <div class="float-left layui-col-md6 layui-col-sm4 layui-col-lg4">
        <div class="layui-form-item layui-form-item-sm">
            <label class="layui-form-label color">项目经理:</label>
            <div class="layui-input-block">
                <span class="layui-input ovflowHidden" title="{{d.projectManager||''}}">{{d.projectManager||''}}</span>
            </div>
        </div>
    </div>
    <div class="float-left layui-col-md6 layui-col-sm4 layui-col-lg4">
        <div class="layui-form-item layui-form-item-sm">
            <label class="layui-form-label color">销售经理:</label>
            <div class="layui-input-block">
                <span class="layui-input ovflowHidden" title="{{d.salesName||''}}">{{d.salesName||''}}</span>
            </div>
        </div>
    </div>
    <div class="float-left layui-col-md6 layui-col-sm4 layui-col-lg4">
        <div class="layui-form-item layui-form-item-sm">
            <label class="layui-form-label color">收入合同含税总额:</label>
            <div class="layui-input-block">
                <span class="layui-input ovflowHidden" title="{{thousands(d.contractAmount)||''}}">{{thousands(d.contractAmount)||'0.00'}}</span>
            </div>
        </div>
    </div>
    <div class="float-left layui-col-md6 layui-col-sm4 layui-col-lg4">
        <div class="layui-form-item layui-form-item-sm">
            <label class="layui-form-label color">收入合同不含税总额:</label>
            <div class="layui-input-block">
                <span class="layui-input ovflowHidden" title="{{thousands(d.contractAmountNotax)||''}}">{{thousands(d.contractAmountNotax)||'0.00'}}</span>
            </div>
        </div>
    </div>
    <div class="float-left layui-col-md6 layui-col-sm4 layui-col-lg4">
        <div class="layui-form-item layui-form-item-sm">
            <label class="layui-form-label color">预算毛利率(%不含垫资):</label>
            <div class="layui-input-block">
                <span class="layui-input ovflowHidden" title="{{d.budgetMargin||''}}">{{d.budgetMargin}}</span>
            </div>
        </div>
    </div>

    <div class="float-left layui-col-md6 layui-col-sm4 layui-col-lg4">
        <div class="layui-form-item layui-form-item-sm">
            <label class="layui-form-label color">总累计毛利率（%）:</label>
            <div class="layui-input-block">
                <span class="layui-input ovflowHidden" title="{{d.totalMargin||''}}">{{d.totalMargin||''}}</span>
            </div>
        </div>
    </div>
    <div class="float-left layui-col-md6 layui-col-sm4 layui-col-lg4">
        <div class="layui-form-item layui-form-item-sm">
            <label class="layui-form-label color">项目收入总额:</label>
            <div class="layui-input-block">
                <span class="layui-input ovflowHidden" title="{{thousands(d.projectRevTotal)||''}}">{{thousands(d.projectRevTotal)||0.00}}</span>
            </div>
        </div>
    </div>
    <div class="float-left layui-col-md6 layui-col-sm4 layui-col-lg4">
        <div class="layui-form-item layui-form-item-sm">
            <label class="layui-form-label color">其中：集成服务收入:</label>
            <div class="layui-input-block">
                <span class="layui-input ovflowHidden" title="{{thousands(d.rev1Amount)||''}}">{{thousands(d.rev1Amount)||'0.00'}}</span>
            </div>
        </div>
    </div>
    <div class="float-left layui-col-md6 layui-col-sm4 layui-col-lg4">
        <div class="layui-form-item layui-form-item-sm">
            <label class="layui-form-label color">其中：工程服务收入:</label>
            <div class="layui-input-block">
                <span class="layui-input ovflowHidden" title="{{thousands(d.rev2Amount)||''}}">{{thousands(d.rev2Amount)||'0.00'}}</span>
            </div>
        </div>
    </div>
    <div class="float-left layui-col-md6 layui-col-sm4 layui-col-lg4">
        <div class="layui-form-item layui-form-item-sm">
            <label class="layui-form-label color">其中：租赁服务收入:</label>
            <div class="layui-input-block">
                <span class="layui-input ovflowHidden" title="{{thousands(d.rev3Amount)||''}}">{{thousands(d.rev3Amount)||'0.00'}}</span>
            </div>
        </div>
    </div>
    <div class="float-left layui-col-md6 layui-col-sm4 layui-col-lg4">
        <div class="layui-form-item layui-form-item-sm">
            <label class="layui-form-label color">其中：设备销售收入:</label>
            <div class="layui-input-block">
                <span class="layui-input ovflowHidden" title="{{thousands(d.rev4Amount)||''}}">{{thousands(d.rev4Amount)||'0.00'}}</span>
            </div>
        </div>
    </div>
    <div class="float-left layui-col-md6 layui-col-sm4 layui-col-lg4">
        <div class="layui-form-item layui-form-item-sm">
            <label class="layui-form-label color">本月末应收账款:</label>
            <div class="layui-input-block">
                <span class="layui-input ovflowHidden" title="{{thousands(d.receivableAmount)||''}}">{{thousands(d.receivableAmount)||'0.00'}}</span>
            </div>
        </div>
    </div>
    <div class="float-left layui-col-md6 layui-col-sm4 layui-col-lg4">
        <div class="layui-form-item layui-form-item-sm">
            <label class="layui-form-label color">总累计坏账计提:</label>
            <div class="layui-input-block">
                <span class="layui-input ovflowHidden" title="{{thousands(d.totalBds)||''}}">{{thousands(d.totalBds)||'0.00'}}</span>
            </div>
        </div>
    </div>
    <div class="float-left layui-col-md6 layui-col-sm4 layui-col-lg4">
        <div class="layui-form-item layui-form-item-sm">
            <label class="layui-form-label color">参与方式:</label>
            <div class="layui-input-block">
                <span class="layui-input ovflowHidden" title="{{d.particiMethod||''}}">{{d.particiMethod||''}}</span>
            </div>
        </div>
    </div>
    <div class="float-left layui-col-md6 layui-col-sm4 layui-col-lg4">
        <div class="layui-form-item layui-form-item-sm">
            <label class="layui-form-label color">计划初验时间:</label>
            <div class="layui-input-block">
                <span class="layui-input ovflowHidden" title="{{d.planEarlyDate||''}}">{{d.planEarlyDate||''}}</span>
            </div>
        </div>
    </div>
    <div class="float-left layui-col-md6 layui-col-sm4 layui-col-lg4">
        <div class="layui-form-item layui-form-item-sm">
            <label class="layui-form-label color">计划终验时间:</label>
            <div class="layui-input-block">
                <span class="layui-input ovflowHidden" title="{{d.planFinalDate||''}}">{{d.planFinalDate||''}}</span>
            </div>
        </div>
    </div>
</script>

    <script>
        var tabType = '1';//tab 1主页面  2项目编号 3客户名称 4供应商名称 5省分/本地网风险画像
        layui.use(['jqform', 'table', 'laytpl', 'jquery', 'laydate', 'layer', 'jqbind', 'jqfrm'], function () {
            var $ = layui.jquery,
                ctx = top.global.ctx,
                frm = layui.jqfrm,
                layer = layui.layer,
                laydate = layui.laydate,
                form = layui.jqform,
                tpl = layui.laytpl,
                table = layui.table,
                jqbind = layui.jqbind;
            var tableIns;
            var projectNumber = decodeURI(getUrlParam("projectNumber"))
            var periodName = getUrlParam("periodName")
            var projectDate = getUrlParam("projectDate")
            var initiationTimeValEnd ;
            if (getNowDate()>15){
                initiationTimeValEnd = getMonthAll()[1]
            }else {
                initiationTimeValEnd = getMonthAll()[2]
            }
            var initiationTimeValStart = getMonthArr(initiationTimeValEnd,12)[0] ;
            if (projectDate){
                initiationTimeValStart = projectDate.split('-')[0]+projectDate.split('-')[1]
            }
            var date2val;
            if (periodName && periodName !=' null'){
                date2val = periodName
            }else{
                date2val = initiationTimeValEnd
            }

            $("#date2").val(date2val);
            // 诊断会计期间
            laydate.render({
                elem: "#date1",
                range: true,
                type: 'month',
                format: 'yyyyMM',
                value: initiationTimeValStart + ' - ' + initiationTimeValEnd,
                isInitValue: true,
                showBottom: true, //关闭底部框 去掉取消、确定、清空按钮
                done:function(val) {
                    projectAmountFun()
                }
            });

            laydate.render({
                elem: "#date2",
                type: 'month',
                format: 'yyyyMM',
                value: date2val,
                isInitValue: true,
                showBottom: true, //关闭底部框 去掉取消、确定、清空按钮
                done:function(val) {
                    projectIdFun()
                }
            });

            window.openInfos = function(index){
                
                 $('#ohters-tip-' + index).show()
                // layer.open({
                //     type: 1,
                //     title:'提示',
                //     id: 'ID-demo-layer-offset-'+ index, // 防止重复弹出
                //     content: $("#ohters-tip-"+index),
                //     area: ['600px', '400px'],
                //     btn: '确定',
                //     btnAlign: 'r', // 按钮居中
                //     yes: function(){
                //         layer.closeAll();
                //     }
                // });
            }

            window.closeInfos = function(index){
                
                $('#ohters-tip-' + index).hide()

            }

            // 重置
            window.restFun = function () {
               $("#projectNumber").val("");
                form.render();
                reloadForm()
            };

            //reloadForm 查询
            window.reloadForm = function () {
                projectIdFun()
                projectAmountFun()
                findRiskInfoByProjectNumber()
                riskProfileTable()
            };

            window.searchClick =function (){
                $('#projectRiskId').css('opacity',0)
                projectIdFun()
                projectAmountFun()
                findRiskInfoByProjectNumber()
                riskProfileTable()
            }

            window.findRiskInfoByProjectNumber = function(){
                var date2 = $("#date2").val()
                $.ajax({
                    url: ctx + "/ict/findRiskInfoByProjectNumber",
                    dataType: "JSON",
                    type: "POST",
                    data: JSON.stringify({
                        projectNumber: $('#projectNumber').val(),
                        periodName: date2
                    }),
                    contentType: "application/json;charset=UTF-8",
                    success: function (res) {
                        if (res.httpCode == 200) {
                            //项目近18个月计收金额展示
                            if (res.data&&res.data.length > 0) {
                                lp(tpl, res.data, $("#project-list").html(), $("#projectsList"));
                            }else{
                                lp(tpl, [], $("#project-list").html(), $("#projectsList"));
                            }
                        } else {
                            frm.error(res.msg)
                            lp(tpl, [], $("#project-list").html(), $("#projectsList"));
                        }

                        form.render();
                    },
                    error: function (e) {
                    }
                });
            }

            //独立查询条件
            window.projectIdFun = function () {
                var projectNumber = $('#projectNumber').val()
                var date2 = $("#date2").val()
                if (!projectNumber) {
                    layer.alert("请输入项目编号！", { icon: 5, closeBtn: 0 }, function (index) {
                        layer.close(index);
                    });
                } else {
                    var indexD = layer.load(1, {
                        shade: [0.1, "#fff"],
                    });
                    //项目编号
                    $.ajax({
                        url: ctx + "/ict/findRiskBaseByProjectNumber",
                        dataType: "JSON",
                        type: "POST",
                        data: JSON.stringify({
                            projectNumber: $('#projectNumber').val(),
                            periodName: date2
                        }),
                        contentType: "application/json;charset=UTF-8",
                        success: function (res) {
                            if (res.httpCode == 200) {
                                //项目近18个月计收金额展示
                                if (res.data&&res.data.length > 0) {
                                    lp(tpl, res.data[0], $("#tab2data-tpl").html(), $("#tab_2_data"));
                                    $('#projectRiskId').css('opacity',1)
                                }else {
                                    lp(tpl, {budgetMargin:''}, $("#tab2data-tpl").html(), $("#tab_2_data"));
                                    $('#projectRiskId').css('opacity',1)
                                }
                            } else {
                                frm.error(res.msg)
                            }

                            form.render();
                            layer.close(indexD);
                        },
                        error: function (e) {
                            layer.close(indexD);
                        }
                    });

                }
            }


            // 项目详情
            window.riskProfileTable = function () {
                var colZ = [
                    {
                        field: 'projectNumber', title: 'ERP项目编号', align: 'center', width: '150'
                    },
                    {
                        field: 'projectName', title: 'ERP项目名称', align: 'center', width: '200', style: 'text-align: left;'
                    },
                    {
                        field: 'classifyName', title: '合同分类名称', align: 'center', width: '260', style: 'text-align: left;'
                    },
                    {
                        field: 'proName', title: '省份名称', align: 'center', width: '80'
                    },
                    {
                        field: 'comName', title: '公司名称', align: 'center', width: '180', style: 'text-align: left;'
                    },
                    {
                        field: 'deptName', title: '部门名称', align: 'center', width: '150', style: 'text-align: left;'
                    },
                    {
                        field: 'contractGlobalsn', title: '合同编号', align: 'center', width: '190'
                    },
                    {
                        field: 'contractName', title: '合同名称', align: 'center', width: '300', style: 'text-align: left;'
                    },
                    {
                        field: 'weSignName', title: '我方主体名称', align: 'center', width: '300', style: 'text-align: left;'
                    },
                    {
                        field: 'otherSignName', title: '对方主体名称', align: 'center', width: '200', style: 'text-align: left;'
                    },
                    {
                        field: 'businessRegistrationPlace', title: '企业工商注册地', align: 'center', width: '200', style: 'text-align: left;'
                    },
                    {
                        field: 'rmbMoney', title: '含增值税合同本位币金额', align: 'center', width: '185', style: 'text-align: right;', templet: function (d) { return thousands(d.rmbMoney); }
                    },
                    {
                        field: 'rmbunvatamount', title: '不含增值税合同本位币金额', align: 'center', width: '198', style: 'text-align: right;', templet: function (d) { return thousands(d.rmbunvatamount); }
                    },
                    {
                        field: 'vatrate', title: '增值税税率', align: 'center', width: '100'
                    },
                    {
                        field: 'arrairStatusName', title: '业务状态名称', align: 'center', width: '110'
                    },
                    {
                        field: 'contractStatusName', title: '合同状态名称', align: 'center', width: '110'
                    },
                    {
                        field: 'sendApprovalDate', title: '送审日期', align: 'center', width: '150'
                    },
                    {
                        field: 'approveEndTime', title: '送审流程结束日期', align: 'center', width: '150'
                    },
                    {
                        field: 'finalSignTime', title: '最终签章日期', align: 'center', width: '150'
                    },
                    {
                        field: 'precondition', title: '合同启动依据', align: 'center', width: '150', style: 'text-align: left;'
                    },
                    {
                        field: 'lastApproverName', title: '最终审批人', align: 'center', width: '95'
                    },
                    {
                        field: 'selectModelName', title: '对方选择方式名称', align: 'center', width: '150'
                    },
                    {
                        field: 'changeTypeName', title: '合同性质名称', align: 'center', width: '110'
                    },
                    {
                        field: 'performTimeLimitName', title: '合同履行期限名称', align: 'center', width: '135'
                    },
                    {
                        field: 'takerName', title: '承办人名称', align: 'center', width: '95'
                    },
                    {
                        field: 'mobilePhone', title: '承办人电话', align: 'center', width: '118'
                    },
                    {
                        field: 'moneyTypeName', title: '合同币种名称', align: 'center', width: '110'
                    },
                    {
                        field: 'exchangeRate', title: '汇率', align: 'center', width: '70'
                    },
                    {
                        field: 'rn', title: '含增值税合同金额', align: 'center', width: '135', style: 'text-align: right;', templet: function (d) { return thousands(d.rn); }
                    },
                    {toolbar: '#toolbar', width: 80, title: "操作", align: 'center', fixed: 'right'}
                ];
                var projectNumber = $('#projectNumber').val()
                if (!projectNumber) {
                    layer.alert("请输入项目编号！", { icon: 5, closeBtn: 0 }, function (index) {
                        layer.close(index);
                    });
                    return false;
                }
                table.render({
                    elem: '#risk_profile_table',
                    id: '#risk_profile_table',
                    url: ctx + '/contract-array/getContractPage',
                    where: {
                        comName: '',
                        projectNumber: projectNumber,
                        projectName: '',
                        contractName: ''
                    },
                    page: true,
                    limit: 10,
                    cols: [colZ],
                    done: function (res, curr, count) {

                    }
                });
            }

            table.on('tool(risk_profile_table)', function(obj){
                var data = obj.data;
                if (obj.event === 'detail') {
                    layer.open({
                        title: '附件列表',
                        content:
                            "/jtaudithtml/views/audit/bigdata/ictDiagnosticEquipment/supplierFileList.html?contractGlobalsn="+data.contractGlobalsn,
                        type: 2,
                        area: ["70%", "80%"],
                        fixed: true
                    });
                }
            });

            window.projectAmountFun = function () {
                var projectNumber = $('#projectNumber').val()
                var date1 = $("#date1").val() ? ($("#date1").val()).split(" - ") : [];
                if (!projectNumber) {
                    layer.alert("请输入项目编号！", { icon: 5, closeBtn: 0 }, function (index) {
                        layer.close(index);
                    });
                } else {
                    var indexD = layer.load(1, {
                        shade: [0.1, "#fff"],
                    });
                    //项目编号
                    $.ajax({
                        url: ctx + "/ict/findRiskAmountByProjectNumber",
                        dataType: "JSON",
                        type: "POST",
                        data: JSON.stringify({
                            projectNumber: $('#projectNumber').val(),
                            periodNameStart: date1[0],
                            periodNameEnd: date1[1]
                        }),
                        contentType: "application/json;charset=UTF-8",
                        success: function (res) {
                            if (res.httpCode == 200) {
                                //项目近18个月计收金额展示
                                if (res.data) {
                                    projectIdTable(res.data.projectChargeAmountMaps, res.data.projectChargeAmountVos)
                                } else {
                                    projectIdTable([], [])
                                }

                            } else {
                                frm.error(res.msg)
                            }
                            form.render();
                            layer.close(indexD);
                        },
                        error: function (e) {
                            layer.close(indexD);
                        }
                    });

                }
            }

            if (projectNumber && projectNumber!='null') {
                $('#projectNumber').val(projectNumber)
                projectIdFun()
                projectAmountFun()
                findRiskInfoByProjectNumber()
                riskProfileTable()
            }else{
                $('#projectRiskId').css('opacity',1)
            }

            // 项目近12个月计收金额展示
            window.projectIdTable = function (projectChargeAmountMaps, projectChargeAmountVos) {

                var colsList = [[
                    { field: 'name', title: '账期', align: 'center', width: '170', fixed: 'left' },

                ]];

                if (projectChargeAmountVos && projectChargeAmountVos.length > 0) {
                    $.each(projectChargeAmountVos, function (i, item) {
                        colsList[0].push({
                            field: item.approvedDate,
                            title: item.approvedDate,
                            width: "125",
                            align: 'center',
                            style: 'text-align: right;',
                            templet: function (d) {
                                if (d.name == '税率') {
                                    return thousands(d[item.approvedDate]) + '%';
                                } else {
                                    return thousands(d[item.approvedDate]);
                                }
                            }

                        });
                    })

                     tableIns = table.render({
                        elem: '#index_table',
                        id: '#indexTable',
                        data: projectChargeAmountMaps,
                        page: false,
                        limit: 9999,
                        cols: colsList,
                        done: function (res, curr, count) {
                        }
                    });


                } else {
                     tableIns = table.render({
                        elem: '#index_table',
                        id: '#indexTable',
                        data: [],
                        page: false,
                        limit: 9999,
                        cols: colsList,
                        done: function (res, curr, count) {
                        }
                    });
                }




            }


            /*function getMonth(num) {
                var _curDate = new Date(),
                    _curTime = _curDate.getTime(), //当前纪元时间
                    _oneDayTime = 24 * 3600 * 1000, //一天的总毫秒数
                    _thisMonSumDay = new Date(_curDate.getFullYear(), _curDate.getMonth() + 1, 0).getDate(); //当前月份总天数
                var _resultArr = [];
                for (var i = 1, j = _curTime; i <= num; i++, j -= _thisMonSumDay * _oneDayTime) {
                    //月迭代
                    if (_resultArr.length >= num) {
                        break;
                    }
                    //当月里的时间点，作为当月依据
                    var _thisMonth = new Date(j);
                    //重新赋值当前月天数
                    _thisMonSumDay = new Date(_thisMonth.getFullYear(), _thisMonth.getMonth() + 1, 0).getDate();

                    _resultArr.push(getNowFormatDate(new Date(j)));
                }
                return _resultArr;
            }*/



            function getNowFormatDate(pre) {
                let date = new Date()
                let year = date.getFullYear() - pre
                let month = date.getMonth() + 1
                if (month < 10) month = '0' +  month // 如果月份是个位数，在前面补0
                return  year + '-'  + month
            }
            function getMonthAll() {
                let end = getNowFormatDate(0)
                let begin = getNowFormatDate(1)
                var d1 = begin;
                var d2 = end;
                var dateArry = new Array();
                var s1 = d1.split("-");
                var s2 = d2.split("-");
                var mCount = 0;
                if (parseInt(s1[0]) < parseInt(s2[0])) {
                    mCount = (parseInt(s2[0]) - parseInt(s1[0])) * 12 + parseInt(s2[1]) - parseInt(s1[1]) + 1;
                } else {
                    mCount = parseInt(s2[1]) - parseInt(s1[1]) + 1;
                }
                if (mCount > 0) {
                    var startM = parseInt(s1[1]);
                    var startY = parseInt(s1[0]);
                    for (var i = 0; i < mCount; i++) {
                        if (startM < 12) {
                            dateArry[i] = startY + "" + (startM > 9 ? startM : "0" + startM);
                            startM += 1;
                        } else {
                            dateArry[i] = startY + "" + (startM > 9 ? startM : "0" + startM);
                            startM = 1;
                            startY += 1;
                        }
                    }
                }
                dateArry.reverse()
                return dateArry;
            }
            function getNowDate() {
                let date = new Date()
                return  date.getDate();
            }

            function getMonthArr(date, n) {
                let dateArr = [];
                let year = date.substr(0,4);
                let month = date.substr(-2,2);
                let m = Number(month);

                if(n < m) {
                    //1.n<month的情况
                    for(let i=m-n+1;i<=month;i++){
                        let m1 = i < 10 ? "0" + i : i;
                        dateArr.push(year + '' + m1);
                    }
                } else {
                    //2.n>month的情况
                    for(let i=(12-(n-m)+1);i<=12;i++){
                        let m1 = i < 10 ? "0" + i : i;
                        dateArr.push((year-1) + '' + m1);
                    }
                    for(let i=1;i<=m;i++){
                        let m1 = i < 10 ? "0" + i : i;
                        dateArr.push(year + '' + m1);
                    }
                }
                return dateArr;

            }


        });
    </script>
</body>
