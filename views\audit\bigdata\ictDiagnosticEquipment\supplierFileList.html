<!--#include virtual ="include/header.html"-->
<link href="resource/css/audit/online/tagmodel/tagIndex.css?v=6.5" rel="stylesheet" type="text/css"/>
<link href="resource/css/audit/online/tagmodel/tagCommon.css?v=6.5" rel="stylesheet" type="text/css"/>
<style>
    .top-candel {
        width: 100%;
        height: 60px;
        background: rgba(244, 244, 244, 0.39);
        box-sizing: border-box;
        padding: 0px 20px;
        display: flex;
        align-items: center;
    }

    .top-candel .left-search {
        display: flex;
        align-items: center;
    }

    .top-candel .left-search .left-search-name {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 400;
        color: #333333;
        padding-right: 12px;
    }

    .top-candel .left-search input {
        width: 160px;
        height: 32px;
        background: rgba(255, 255, 255, 0.39);
        border: 1px solid #D9D9D9;
        border-radius: 1px;
        padding-left: 10PX;
        padding-right: 10PX;
        font-size: 14PX;
    }

    .right-btn {
        display: flex;
        align-items: center;
    }

    .empty-flex {
        flex: 1
    }

    .model-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 80px !important;
        border-radius: 4px !important;
    }

    .model-btn i {
        margin-right: 3px;
    }

    .model-btn.other-btn-submit {
        background: #FCECED;
        color: #C20000;
        border: 1px solid #C20000;
        margin-left: 12px;
    }

    .bottom-table {
        margin-top: 8px;
        padding-bottom: 15px;
    }

    .table-flex-j-s {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .table-flex-j-s span {
        cursor: pointer;
        color: #C20000;
        font-size: 15px;
    }

    .table-flex-j-s span:last-child {
        margin-left: 25px;
    }

    .layui-laydate .laydate-btns-clear {
        display: inline-block !important;
    }
</style>

<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <button class="layui-btn btn-outline layui-btn-normal layui-btn-sm" style="float: right;"
                        onclick="batchDownload();"
                        type="button">
                    <i class="iconfont search-icon">&#xe6f5;</i> 批量下载
                </button>
                <div class="bottom-table">
                    <table class="layui-table jq-even" id="table_1" lay-filter="table_1"></table>
                </div>
            </div>
        </div>
    </div>
</div>
</body>

<script id="upload" type="text/html">
    <a href='/jtauditwo/files/downLoad/{{encrypt(d.attachmentId)}}' class="table-btn tip-edit"
       title="下载">
        <i class="iconfont">&#xeac3;</i>
    </a>
</script>

<!--#include virtual ="include/version.html"-->
<script src="resource/js/jquery/jquery.js" type="text/javascript"></script>

<script id="toolbar">
    var contractGlobalsn = getUrlParam('contractGlobalsn');
    layui.use([
        "jqform",
        "table",
        "laytpl",
        "jquery",
        "laydate",
        "layer",
        "jqbind",
        "upload",
        "jqztree",
        "laypage",
        "laydate",
        "jqfrm"
    ], function () {
        var $ = layui.jquery,
            ctx = top.global.ctx,
            $ZTree = layui.jqztree,
            layer = layui.layer,
            form = layui.jqform,
            tpl = layui.laytpl,
            table = layui.table,
            upload = layui.upload,
            laypage = layui.laypage,
            laydate = layui.laydate,
            frm = layui.jqfrm,
            jqbind = layui.jqbind;
        jqbind.init();

        // 调用接口获取文件列表
        window.tabelIns1 = function () {
            table.render({
                    elem: "#table_1",
                    id: 'table_1',
                    even: true,
                    page: true,
                    url: ctx + '/colligate/contractInfo/getConFileList',
                    where: {
                        contractGlobalsn: contractGlobalsn,
                    },
                    isClient: false,
                    cols: [
                        [
                            {type: 'checkbox', width: '3%', align: 'center', style: 'text-align: center;'},
                            {type: "numbers", title: "序号", align: "center", width: "5%"},
                            {
                                field: "contractName",
                                title: "合同名称",
                                align: "center",
                                width: "33%",
                                style: "text-align:left",
                            },
                            {
                                field: "sourceName",
                                title: "附件名称",
                                align: "center",
                                width: "34%",
                                style: "text-align:left",
                            },
                            {
                                field: "fileTypeName",
                                title: "附件类型",
                                align: "center",
                                width: "15%",
                            },
                            {title: '操作', align: 'center', toolbar: '#upload'},
                        ]
                    ],
                }
            )
        }
        tabelIns1()

        /**
         * 批量下载报审资料
         * @returns {boolean}
         */
        window.batchDownload = function () {
            var checkedRows = table.checkStatus("table_1");
            var checkedData = checkedRows.data;
            var checkedLength = checkedData.length;
            var files = [];
            $.each(checkedData, function (i, obj) {
                var id = obj.attachmentId;
                if (id) {
                    files.push(id);
                }
            });
            if (0 == checkedLength) {
                layer.msg('请至少选择一条附件信息！', {icon: 2});
                return false;
            }
            var url = ctx + "/files/downLoad/" + files.toString() + "/附件";
            window.location.href = url;
        };
    });
</script>
