<!--ICT诊断仪-->
<!--ICT诊断仪-->
<!--#include virtual ="include/header.html"-->
<link href="resource/css/audit/online/tagmodel/tagIndex.css?v=6.5" rel="stylesheet" type="text/css">
<link href="resource/css/audit/online/tagmodel/tagCommon.css?v=6.5" rel="stylesheet" type="text/css">
<link href="resource/css/number-risk-diagnosis/index.css?v=6.5" rel="stylesheet" type="text/css"/>
<style>
    .new-table .layui-table-view .layui-table td, .new-table .layui-table-view .layui-table th {
        border-left: 1px solid #ddd;
        border-right: 1px solid #ddd;
    }
    .layui-table-view .layui-table {
        min-width: 100%;
    }
    .table_list .layui-table-body{
        overflow-x: hidden;
    }
    .layui-common-body {
        background: #fff;
    }

    .layui-form-item {
        margin-bottom: 10px;
    }

    .tab_2 .new-style .layui-input-block {
        margin-left: 177px;
    }

    .tab_2 .new-style .layui-form-label {
        width: 175px;
    }

    .new-style .layui-input-block {
        margin-left: 112px;
    }

    .new-style .layui-form-label {
        width: 110px;
    }

    .layui-search-new.new-style .layui-form-label {
        padding: 0 6px 0 0;
    }

    .ict-header-list {
        display: inline-block;
        width: 100%;
        text-align: left;
    }

    .ict-header-li {
        padding: 0 26px;
        box-sizing: border-box;
        display: inline-block;
        cursor: pointer;
    }

    .ict-header-title {
        font-family: PingFangSC, PingFang SC;
        font-size: 15px;
        color: #333333;
        font-style: normal;
        border-bottom: 3px solid #fff;
        height: 42px;
    }

    .ict-header-li.active .ict-header-title {
        color: #C20000;
        border-bottom: 3px solid #C20000;
    }

    .ict-search-box {
        width: 100%;
        margin-top: 8px;
        background: #F7F9FA;
        padding: 10px 10px 12px 10px;
        box-sizing: border-box;
    }

    .ict-search-form {
        width: calc(100% - 130px);
        float: left;
    }

    .ict-search-btn {
        width: 100px;
        float: right;
    }

    .model-btn-submit {
        margin-bottom: 10px;
    }

    .search-input input {
        height: 42px;
        width: 450px;
    }

    .search-input {
        margin-top: 15px;
    }

    .search-input .search-btn-right {
        width: 100px;
        height: 42px;
        border-radius: 0 4px 4px 0;
        background: #C20000;
    }

    .model-btn.model-btn-submit {
        background-color: #C20000;
        border: 1px solid #C20000;
    }

    .search-input .search-btn-right.search-btn-right-2 {
        margin-left: 20px;
        width: 120px;
        border-radius: 4px;
    }

    .search-input .search-btn-right.search-btn-right-2 .search-btn-right-text {
        padding: 0;
    }

    .new-style .layui-input-block .input-p {
        padding: 4px 0;
        display: inline-block;
        box-sizing: border-box;
    }

    .formButton {
        position: relative;
        left: 120px;
    }

    .formButton button {
        height: 32px;
        line-height: 32px;
    }

    .layui-input[disabled="disabled"] {
        background: #eee !important;
    }

    .layui-laydate .laydate-btns-clear {
        display: inline-block;
    }

    .goClick {
        color: #C20000;
        cursor: pointer;
    }

    .model-btn.model-btn-export {
        min-width: 88px;
        padding: 0 12px;
        box-sizing: border-box;
        background-color: #ffffff;
        border: 1px solid #c20000;
        border-radius: 2px;
        float: right;
    }

    .model-btn.model-btn-export span {
        color: #c20000;
    }

    .model-btn.model-btn-export i {
        color: #c20000;
    }

</style>
<style>
    .card-tab{
        display: inline-block;
    }
    .card-tab .tab-item{
        display: inline-block;
    }
    .card-tab .tab-item-box{
        cursor: pointer;
        margin-left: 10px;
        min-width: 88px;
        padding: 0 10px;
        height: 28px;
        background: #FCF8F8;
        border-radius: 2px;
        border: 1px solid #c20000;
    }
    .card-tab .tab-item-box .tab-item-box-name{
        width: 100%;
        line-height: 26px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #c20000;
        text-align: center;
        font-style: normal;
    }
    .card-tab .tab-item-box.active{
        background: #c20000;
    }
    .card-tab .tab-item-box.active .tab-item-box-name{
        color:#fff;
    }
</style>

<body>

<div class="layui-common-body layui-row">

    <div class="tab-menu">
        <a data-parent="true" href="javascript:" id="tableMenu" style="color: #c00;text-decoration:underline;">
            <i class="iconfont" data-icon=""></i>
        </a>
    </div>

    <div class="layui-row layui-col-md12 layui-col-sm12 layui-col-lg12">
        <div class="layui-row">
            <div class="layui-common-box">
                <div class="layui-row layui-common-card">
                    <div class="layui-card-body main-list-body layui-row"
                         style="min-height: 750px; padding-top: 4px;">
                        <div class="ict-search-box layui-row">
                            <div class="ict-search-form layui-form new-style layui-row">
                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="layui-form-item layui-form-item-sm">
                                        <label class="layui-form-label color">归属省分</label>
                                        <div class="layui-input-block">
                                            <select id="provCode" lay-filter="provCode" name="provCode"></select>
                                        </div>
                                    </div>
                                </div>
                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="layui-form-item layui-form-item-sm">
                                        <label class="layui-form-label color">归属地市</label>
                                        <div class="layui-input-block">
                                            <select id="areaCode" lay-filter="areaCode" name="areaCode"></select>
                                        </div>
                                    </div>
                                </div>
                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="layui-form-item layui-form-item-sm">
                                        <label class="layui-form-label color">风险大类</label>
                                        <div class="layui-input-block">
                                            <select id="riskCategoryCode" lay-filter="riskCategoryCode"
                                                    name="riskCategoryCode"></select>
                                        </div>
                                    </div>
                                </div>
                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="layui-form-item layui-form-item-sm">
                                        <label class="layui-form-label color">风险小类</label>
                                        <div class="layui-input-block">
                                            <select id="riskSubCategoryCode" lay-filter="riskSubCategoryCode"
                                                    name="riskSubCategoryCode"></select>
                                        </div>
                                    </div>
                                </div>
                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="layui-form layui-form-item layui-form-item-sm">
                                        <label class="layui-form-label color">类型</label>
                                        <div class="right-value">
                                            <div class=" layui-input-block">
                                                <input lay-filter="riskTypeRadio" lay-skin="primary" name="type"
                                                       value="1" title="按立项期间" type="radio" checked>
                                                <input lay-filter="riskTypeRadio" lay-skin="primary" name="type"
                                                       value="2" title="按计收期间" type="radio">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="layui-form-item layui-form-item-sm">
                                        <label class="layui-form-label color">立项期间</label>
                                        <div class="layui-input-block">
                                            <input class="layui-input" id="initiationTime" readonly type="text">
                                        </div>
                                    </div>
                                </div>

                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="layui-form-item layui-form-item-sm">
                                        <label class="layui-form-label color">截止账期</label>
                                        <div class="layui-input-block">
                                            <input class="layui-input" id="date1" readonly type="text">
                                        </div>
                                    </div>
                                </div>
                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="layui-form-item layui-form-item-sm">
                                        <label class="layui-form-label color">计收期间</label>
                                        <div class="layui-input-block">
                                            <input class="layui-input" id="date2" readonly type="text">
                                        </div>
                                    </div>
                                </div>

                                <div class="float-right  formButton">
                                    <button type="button" class="layui-btn" onclick="goProjectInfo()">全量合同信息查询
                                    </button>
                                    <button type="button" class="layui-btn" onclick="goProjectItem()">按项目编号
                                    </button>
                                    <button type="button" class="layui-btn"
                                            onclick="goProjectKhItem()">按客户名称
                                    </button>
                                    <button type="button" class="layui-btn" onclick="goProjectGys()">按供应商</button>

                                </div>
                            </div>
                            <div class="ict-search-btn">
                                <div class="model-btn model-btn-submit" onclick="reloadForm()" title="查询">
                                    <i class="iconfont search-icon">&#xe60b;</i>
                                    <span>查询</span>
                                </div>
                                <div class="model-btn model-btn-reset" onclick="restFun()" title="重置">
                                    <i class="iconfont search-icon">&#xe63a;</i>
                                    <span>重置</span>
                                </div>
                            </div>
                        </div>


                        <div class="tab_1 tab_box">
                            <div class="layui-common-header">
                                <div class="layui-common-card-header">
                                    <div class="layui-card-header-title"><span>ICT项目信息</span>
                                        <div class="card-tab">
                                            <div  class="tab-item">
                                                <div  class="tab-item-box active" data-code="1">
                                                    <div  class="tab-item-box-name">统计图信息</div>
                                                </div>
                                            </div>
                                            <div  class="tab-item">
                                                <div  class="tab-item-box"  data-code="2">
                                                    <div  class="tab-item-box-name">列表信息</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-card-body main-list-body">
                                <div class="work-trend-box" data-code="1" >
                                    <div id="yearTrendItem1" style="width: 100%; height: 300px"></div>
                                </div>
                                <div class="work-trend-box new-table table_list"  data-code="2" style="display: none">
                                    <table class="layui-table jq-even"  id="table_list"
                                           lay-filter="table_list"></table>
                                </div>
                            </div>

                        </div>


                        <div class="tab_2 tab_box">
                            <div class="layui-common-header">
                                <div class="layui-common-card-header">
                                    <div class="layui-card-header-title">ICT项目风险信息（金额单位：亿元）</div>

                                    <div class="model-btn model-btn-export" onclick="exportBtn()" title="导出明细">
                                        <i class="iconfont search-icon">&#xe60c;</i>
                                        <span>导出明细</span>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-card-body main-list-body">
                                <div class="layui-form new-table">
                                    <table class="layui-table jq-even" id="risk_profile_table"
                                           lay-filter="risk_profile_table"></table>
                                </div>
                            </div>
                        </div>


                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--#include virtual ="include/version.html"-->
<script src="resource/js/jquery/jquery.js" type="text/javascript"></script>
<script src="resource/js/echarts/echarts.min.js?v=6.5" type="text/javascript"></script>
<script id="select-sub-tpl" type="text/html">
    {{# if (d.list.length == 1){ }}
    {{# layui.each(d.list, function(index, item){ }}
    <option value="{{item.provCode}}">{{item.provName}}</option>
    {{# }); } else { }}
    <option value="">--请选择--</option>
    {{# layui.each(d.list, function(index, item){ }}
    <option value="{{item.provCode}}">{{item.provName}}</option>
    {{# }); }}
    {{#  }  }}
</script>
<script id="select-area-tpl" type="text/html">
    {{# if (d.list.length == 1){ }}
    {{# layui.each(d.list, function(index, item){ }}
    <option value="{{item.areaCode}}">{{item.areaName}}</option>
    {{# }); } else { }}
    <option value="">--请选择--</option>
    {{# layui.each(d.list, function(index, item){ }}
    <option value="{{item.areaCode}}">{{item.areaName}}</option>
    {{# }); }}
    {{#  }  }}
</script>

<script id="select-tpl" type="text/html">
    <option value="">--请选择--</option>
    {{# layui.each(d.list, function(index, item){ }}
    <option value="{{item.lev1Code}}">{{item.lev1Name}}</option>
    {{# }); }}
</script>
<script>

    layui.use(['jqform', 'table', 'laytpl', 'jquery', 'laydate', 'layer', 'jqbind', 'jqfrm'], function () {

        var $ = layui.jquery,
            ctx = top.global.ctx,
            layer = layui.layer,
            frm = layui.jqfrm,
            laydate = layui.laydate,
            form = layui.jqform,
            tpl = layui.laytpl,
            table = layui.table,
            jqbind = layui.jqbind;
        var tabCode = '1';

        var ictProjectSumVosList = []

        // Tab切换事件
        $('.tab-item-box').click(function() {
            // 更新tab样式
            $('.tab-item-box').removeClass('active');
            $(this).addClass('active');
            tabCode = $(this).data('code');
            $('.work-trend-box').hide();
            $('.work-trend-box[data-code="' + tabCode + '"]').show();
            if(tabCode=='1'){
                getIctProjectInfo()
            }else{
                tableList()
            }
        });

        //查询条件 部分
        console.log(loadCurOrg())
        //省分 查询后赋值
        var provCodeSearch = "";
        //地市 查询后赋值
        var areaCodeSearch = "";
        if (loadCurOrg().orgGrade == "P") {
            provCodeSearch = loadCurOrg().orgCode
        } else if (loadCurOrg().orgGrade == "A") {
            console.log(loadCurOrg())
        }
        /* 省分*/
        $.ajax({
            url: ctx + "/ict/getProv",
            dataType: "JSON",
            type: "POST",
            data: JSON.stringify({}),
            contentType: "application/json;charset=UTF-8",
            success: function (ret) {
                if (ret.data.length == 1) {
                    provCodeSearch = ret.data[0].provCode
                    getArea(ret.data[0].provCode)
                }
                var data = {list: ret.data};
                var getTpl = $("#select-sub-tpl").html();
                var obj = $("#provCode");
                tpl(getTpl).render(data, function (html) {
                    obj.html(html);
                });
                form.render();
            },
            error: function (e) {
                console.info(e);
            }
        });
        //监听类型单选框
        form.on('radio(riskTypeRadio)', function (data) {
            if (data.value == "1") {
                $('#date2').attr('disabled', true)
                $('#date1').val("");
                $('#initiationTime').val("");
                $('#date2').val("");
                $('#date1').attr('disabled', false)
                $('#initiationTime').attr('disabled', false)
            } else {
                $('#date1').val("");
                $('#initiationTime').val("");
                $('#date2').val("");
                $('#date2').attr('disabled', false)
                $('#date1').attr('disabled', true)
                $('#initiationTime').attr('disabled', true)
            }
        });
        //省分下拉框触发
        form.on('select(provCode)', function (data) {
            getArea(data.value);
        });

        //加载地市下拉列表
        function getArea(provCode) {
            // var provCode = $('#provCode').val();
            $.ajax({
                url: ctx + "/ict/getArea"
                , dataType: "json"
                , type: "POST"
                , data: JSON.stringify({provCode: provCode})
                , contentType: "application/json;charset=UTF-8"
                , success: function (res) {
                    if (res.data.length == 1) {
                        areaCodeSearch = res.data[0].areaCode
                    }
                    var data = {list: res.data};
                    var getTpl = $("#select-area-tpl").html();
                    var obj = $("#areaCode");
                    tpl(getTpl).render(data, function (html) {
                        obj.html(html);
                    });
                    form.render();
                }
                , error: function (e) {
                }
            })
        };

        /* 风险大类*/
        $.ajax({
            url: ctx + "/ict/getRiskCategory",
            dataType: "JSON",
            type: "POST",
            contentType: "application/json;charset=UTF-8",
            data: JSON.stringify({
                fCode: ""
            }),
            success: function (ret) {
                var data = {list: ret.data};
                var getTpl = $("#select-tpl").html();
                var obj = $("#riskCategoryCode");
                tpl(getTpl).render(data, function (html) {
                    obj.html(html);
                });
                form.render();
            },
            error: function (e) {
                console.info(e);
            }
        });

        //风险大类下拉框触发
        form.on('select(riskCategoryCode)', function (data) {
            riskSubCategoryCodeFunction(data.value);
        });

        //风险小类下拉列表
        function riskSubCategoryCodeFunction(fCode) {
            var riskCategoryCode = $('#riskCategoryCode').val();
            $.ajax({
                url: ctx + "/ict/getRiskCategory",
                dataType: "JSON",
                type: "POST",
                contentType: "application/json;charset=UTF-8",
                data: JSON.stringify({
                    fCode: fCode
                }),
                success: function (ret) {
                    var data = {list: ret.data};
                    var getTpl = $("#select-tpl").html();
                    var obj = $("#riskSubCategoryCode");
                    tpl(getTpl).render(data, function (html) {
                        obj.html(html);
                    });
                    form.render();
                },
                error: function (e) {
                    console.info(e);
                }
            });
        }

        var lastDefaultMonth = getMonthAll()[0].substr(-2, 2)
        if (lastDefaultMonth.indexOf('0') != -1) {
            lastDefaultMonth = lastDefaultMonth.substr(-1, 1)
            if (lastDefaultMonth == 1) {
                lastDefaultMonth = 12
            } else {
                lastDefaultMonth = Number(lastDefaultMonth) - 1
            }

        } else {
            lastDefaultMonth = Number(lastDefaultMonth) - 1
        }

        if (lastDefaultMonth < 10) {
            lastDefaultMonth = getMonthAll()[0].substr(0, 4) + '0' + lastDefaultMonth
        } else {
            lastDefaultMonth = getMonthAll()[0].substr(0, 4) + lastDefaultMonth
        }

        var initiationTimeValEnd;
        if (getNowDate() > 15) {
            initiationTimeValEnd = getMonthAll()[1]
        } else {
            initiationTimeValEnd = getMonthAll()[2]
        }
        var initiationTimeValStart = getMonthArr(initiationTimeValEnd, 12)[0];


        // 诊断立项期间
        laydate.render({
            elem: "#initiationTime",
            range: true,
            type: 'month',
            value: initiationTimeValStart + ' - ' + initiationTimeValEnd,
            format: 'yyyyMM',
            done: function (val) {
                /*if (val || $('#date1').val()) {
                    $('#date2').attr('disabled', true)
                    $('#date1').attr('disabled', false)
                    $('#initiationTime').attr('disabled', false)

                } else if (!val && !$('#date1').val() && !$('#date2').val()) {
                    $('#date2').attr('disabled', false)
                    $('#date1').attr('disabled', false)
                    $('#initiationTime').attr('disabled', false)
                } else if (!val && !$('#date1').val() && $('#date2').val()) {
                    $('#date2').attr('disabled', false)
                    $('#date1').attr('disabled', false)
                    $('#initiationTime').attr('disabled', false)
                }*/
            }
        });


        //诊断截至账期
        laydate.render({
            elem: "#date1",
            range: false,
            type: 'month',
            format: 'yyyyMM',
            value: initiationTimeValEnd,
            done: function (val) {
                /*if (val || $('#initiationTime').val()) {
                    $('#date2').attr('disabled', true)
                    $('#date1').attr('disabled', false)
                    $('#initiationTime').attr('disabled', false)
                } else if (!val && !$('#initiationTime').val() && !$('#date2').val()) {
                    $('#date2').attr('disabled', false)
                    $('#date1').attr('disabled', false)
                    $('#initiationTime').attr('disabled', false)
                } else if (!val && !$('#initiationTime').val() && $('#date2').val()) {
                    $('#date2').attr('disabled', false)
                    $('#date1').attr('disabled', false)
                    $('#initiationTime').attr('disabled', false)
                }*/
            }
        });

        // 诊断会计期间
        laydate.render({
            elem: "#date2",
            range: true,
            type: 'month',
            format: 'yyyyMM',
            isInitValue: true,
            showBottom: true, //关闭底部框 去掉取消、确定、清空按钮
            done: function (val) {
                /*if (val) {
                    $('#date1').attr('disabled', true)
                    $('#initiationTime').attr('disabled', true)
                } else {
                    $('#date1').attr('disabled', false)
                    $('#initiationTime').attr('disabled', false)
                }*/
            }
        });

        $('#date2').attr('disabled', true)


        // 重置
        window.restFun = function () {
            $("#provCode").val("");
            $("#areaCode").val("");
            $("#riskCategoryCode").val("");
            $("#riskSubCategoryCode").val("");
            $("#initiationTime").val("");
            $("#date1").val("");
            $("#date2").val("");

            $('#date1').val(initiationTimeValEnd)
            $('#initiationTime').val(initiationTimeValStart + ' - ' + initiationTimeValEnd)

            $('#date2').attr('disabled', true)
            $('#date1').attr('disabled', false)
            $('#initiationTime').attr('disabled', false)
            $("input[name='type'][value='1']").attr("checked", true);
            form.render();
            reloadForm()
        };

        //查询条件 获取
        window.returnParams = function () {
            var provCode = $("#provCode").val();
            var areaCode = $("#areaCode").val();
            var riskCategoryCode = $("#riskCategoryCode").val();
            var riskSubCategoryCode = $("#riskSubCategoryCode").val();
            var initiationTime = $("#initiationTime").val() ? ($("#initiationTime").val()).split(" - ") : [];
            var date2 = $("#date2").val() ? ($("#date2").val()).split(" - ") : [];
            var date1 = $("#date1").val()
            var params = {
                provCode: provCode || '',
                areaCode: areaCode || '',
                riskCategoryCode: riskCategoryCode || '',
                riskSubCategoryCode: riskSubCategoryCode || '',
                projectDateStart: initiationTime[0] || '',
                projectDateEnd: initiationTime[1] || '',
                accountPeriodStart: date2[0] || '',
                accountPeriodEnd: date2[1] || '',
                diagnosisPeriod: date1 || ''
            }
            return params
        }


        //reloadForm 查询
        window.reloadForm = function (type) {
            provCodeSearch = returnParams().provCode
            areaCodeSearch = returnParams().areaCode
            var initialValue = $('input[name="type"]:checked').val();
            if(initialValue=='1'&&returnParams().diagnosisPeriod==''){
                frm.error("请选择截止账期！");
                return false;
            }else if(initialValue=='1'&&returnParams().projectDateStart==''){
                frm.error("请选择立项期间！");
                return false;
            }else if(initialValue=='2'&&returnParams().accountPeriodStart==''){
                frm.error("请选择计收期间！");
                return false;
            }
            if(tabCode=='1'){
                getIctProjectInfo()
            }else{
                tableList()
            }
            $.ajax({
                url: ctx + "/ict/getIctProjectRiskInfo",
                dataType: "JSON",
                type: "POST",
                contentType: "application/json;charset=UTF-8",
                data: JSON.stringify(returnParams()),
                success: function (ret) {
                    if (ret.httpCode == 200) {
                        if (ret.data.projectRiskInfoVos && ret.data.projectRiskInfoVos.length > 0 && ret.data.ictProjectSumVo) {
                            ret.data.projectRiskInfoVos.push(ret.data.ictProjectSumVo)
                            riskProfileTable(ret.data.projectRiskInfoVos)
                        } else {
                            riskProfileTable(ret.data.projectRiskInfoVos)
                        }

                    } else {
                        frm.error(ret.msg);
                    }

                    form.render();
                },
                error: function (e) {
                    console.info(e);
                }
            });


        }

        //getIctProjectInfo 统计图信息
        window.getIctProjectInfo = function () {
            provCodeSearch = returnParams().provCode
            areaCodeSearch = returnParams().areaCode
            var indexD = layer.load(1, {
                shade: [0.1, "#fff"],
            });

            //判断标签在tabCode 统计图信息 还是 列表信息
            $.ajax({
                url: ctx + "/ict/getIctProjectInfo",
                dataType: "JSON",
                type: "POST",
                contentType: "application/json;charset=UTF-8",
                data: JSON.stringify(returnParams()),
                success: function (ret) {
                    if (ret.httpCode == 200) {
                        ictProjectSumVosList = ret.data.ictProjectSumVos
                        echartsData(ret.data.ictProjectSumVos)
                        getIctProjectInfoAll()
                    } else {
                        frm.error(ret.msg);
                    }

                    form.render();
                    layer.close(indexD);
                },
                error: function (e) {
                    console.info(e);
                    layer.close(indexD);
                }
            });
        }

        window.getIctProjectInfoAll = function () {
            provCodeSearch = returnParams().provCode
            areaCodeSearch = returnParams().areaCode

            //判断标签在tabCode 统计图信息 还是 列表信息
            $.ajax({
                url: ctx + "/ict/getIctProjectInfoAll",
                dataType: "JSON",
                type: "POST",
                contentType: "application/json;charset=UTF-8",
                data: JSON.stringify(returnParams()),
                success: function (ret) {
                    if (ret.httpCode == 200) {
                        ictProjectSumVosList = ret.data.ictProjectSumVos
                        echartsData(ret.data.ictProjectSumVos)
                    } else {
                        frm.error(ret.msg);
                    }

                    form.render();
                },
                error: function (e) {
                    console.info(e);
                }
            });
        }


        // 列表信息
        window.tableList = function () {
            provCodeSearch = returnParams().provCode
            areaCodeSearch = returnParams().areaCode
            var indexD = layer.load(1, {
                shade: [0.1, "#fff"],
            });
            $.ajax({
                url: ctx + "/ict/getIctProjectInfoData",
                dataType: "JSON",
                type: "POST",
                contentType: "application/json;charset=UTF-8",
                data: JSON.stringify(returnParams()),
                success: function (ret) {
                    if (ret.httpCode == 200) {
                        ictProjectData(ret.data.ictProjectSumVos,'1')
                        getIctProjectInfoDataAll()
                    } else {
                        frm.error(ret.msg);
                    }
                    layer.close(indexD);
                    form.render();
                },
                error: function (e) {
                    layer.close(indexD);
                    console.info(e);
                }
            });

        }

        // 列表信息全部数据
        window.getIctProjectInfoDataAll = function () {
            provCodeSearch = returnParams().provCode
            areaCodeSearch = returnParams().areaCode
            $.ajax({
                url: ctx + "/ict/getIctProjectInfoDataAll",
                dataType: "JSON",
                type: "POST",
                contentType: "application/json;charset=UTF-8",
                data: JSON.stringify(returnParams()),
                success: function (ret) {
                    if (ret.httpCode == 200) {
                        ictProjectData(ret.data.ictProjectSumVos,'2')
                    } else {
                        frm.error(ret.msg);
                    }

                    form.render();
                },
                error: function (e) {
                    console.info(e);
                }
            });

        }

        window.ictProjectData = function (data,type) {
            // 保存滚动条位置
            var scrollPosition = $('.layui-table-body').scrollTop();
            var height=null;
            if(data.length>8){
                height='500';
            }
            table.render({
                elem: '#table_list',
                id: 'table_list',
                data:data,
                height:height,
                page: false,
                limit: 9999,
                cols: [[
                    {field: 'provName', title: '单位', align: 'center', width: '15%'},
                    {field: 'countNum', title: '项目数', align: 'center', width: '15%',
                        templet: function (d) {
                            return  '<p class="cursor text-red" lay-event="jumpPage"  title="'+d.countNum+'"   >'+d.countNum+'</p>'  ;
                        }},
                    {field: 'projectIncome', title: '项目收入（亿元）', align: 'center', width: '25%', style: 'text-align: right;',templet: function (d) {
                            return formatNumContainNull(d.projectIncome);
                        }},
                    {field: 'projectArrears', title: '项目欠费（亿元）', align: 'center', width: '25%', style: 'text-align: right;',templet: function (d) {
                            return formatNumContainNull(d.projectArrears);
                        }},
                    {field: 'arrearsIncomeRadio', title: '欠费占收比（%）', align: 'center', width: '20%', style: 'text-align: right;',templet: function (d) {
                            return formatNumContainNull(d.arrearsIncomeRadio);
                        }},
                    // {field: 'grossProfitRate', title: '毛利率（%）', align: 'center', width: '20%', style: 'text-align: right;',templet: function (d) {
                    //         return formatNumContainNull(d.grossProfitRate);
                    //     }}
                ]],
                done: function (res, curr, count) {
                    if(type=='2'){
                        $('.table_list .layui-table-body').scrollTop(scrollPosition);
                    }
                }
            });
        }

        table.on('tool(table_list)', function(obj){
            var data = obj.data;
            if (obj.event === 'jumpPage') {
                var provCode1;
                var areaCode1;
                //柱状图点击事件，省分条件根据是否查询条件是否有值并且查询条件是否使用，省分使用后，跳转时的省分就用查询时的；为使用就用柱状图的
                if (!areaCodeSearch) {
                    areaCode1 = provCodeSearch ? data.provCode : '';
                } if(data.provName=='合计'){
                    areaCode1 =  '';
                }else {
                    areaCode1 = areaCodeSearch;
                }
                provCode1 = provCodeSearch ? provCodeSearch : data.provCode;
                goClickItem(provCode1, areaCode1)
            }
        });

        //echarts接口查询
        window.echartsData = function (data) {
            var yearTrendItemChart1 = echarts.init(document.getElementById('yearTrendItem1'));
            itemChart(data, yearTrendItemChart1);
        }
        var xmsr = false;//项目收入 是否展示
        var xmqf = false;//项目欠费 是否展示
        //echart渲染
        window.itemChart = function (data, yearTrendItemChart) {
            console.log(xmsr, xmqf)
            if (!data) {
                data = []
            }
            var xData = [];
            var yData1 = [];
            var yData2 = [];
            var yData3 = [];

            $.each(data, function (i, item) {
                xData.push(item.provName)
                yData1.push((Number(item.arrearsIncomeRadio) * 100).toFixed(2))
                yData2.push(item.projectIncome)
                yData3.push(item.projectArrears)
            })

            var yearTrendItemOption = {
                tooltip: {
                    trigger: 'item',
                    axisPointer: { // 坐标轴指示器，坐标轴触发有效
                        type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
                    }
                },
                grid: {
                    left: '90',
                    right: '160',
                    bottom: '130',
                    top: '40',
                },
                legend: {
                    orient: 'vertical',
                    data: ['欠费占收比（%）', '项目收入（亿元）', '项目欠费（亿元）'],
                    // selected: {
                    //     '项目收入（亿元）': xmsr, // 置灰
                    //     '项目欠费（亿元）': xmqf  // 置灰
                    // },
                    textStyle: {
                        color: "#666666"
                    },
                    itemWidth: 15,
                    itemHeight: 10,
                    itemGap: 25,
                    type: 'plain',
                    right: '-10',
                    top: '12%',
                },
                xAxis: {
                    type: 'category',
                    data: xData,
                    axisLine: {

                        lineStyle: {
                            color: '#cdd5e2'

                        }
                    },
                    axisLabel: {
                        interval: 0, //隔几项显示一个标签
                        rotate: "45", //标签倾斜的角度，旋转的角度是-90到90度
                        textStyle: {
                            color: "#666666"
                        }
                    },
                },

                yAxis: [{
                    type: 'value',
                    name: "欠费占收比（%）",
                    nameTextStyle: {
                        color: "#666666"
                    },
                    axisLine: {
                        show: false,
                        lineStyle: {
                            color: '#cdd5e2'
                        }
                    },
                    splitLine: {
                        show: false,
                    },
                    axisLabel: {
                        textStyle: {
                            color: "#666666"
                        }
                    },
                },

                    {
                        type: "value",
                        name: "金额(亿元)",
                        nameTextStyle: {
                            color: "#666666"
                        },
                        position: "right",
                        axisLine: {
                            lineStyle: {
                                color: '#cdd5e2'
                            }
                        },
                        splitLine: {
                            show: false,
                        },
                        axisLabel: {
                            show: true,
                            formatter: "{value}", //右侧Y轴文字显示
                            textStyle: {
                                color: "#666666"
                            }
                        }
                    }
                ],
                series: [{
                    name: '欠费占收比（%）',
                    type: 'line',
                    yAxisIndex: 0,
                    barGap: 0,
                    barWidth: '10px',
                    itemStyle: {
                        normal: {
                            color: '#ff7875',
                            barBorderRadius: 0,
                        },
                    },
                    data: yData1
                },
                    {
                        name: '项目收入（亿元）',
                        yAxisIndex: 1,
                        type: 'bar',
                        barWidth: '10px',
                        barGap: 0,
                        itemStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                    offset: 0,
                                    color: '#3d93f2'
                                }, {
                                    offset: 1,
                                    color: '#5dc1fd'
                                }]),
                                barBorderRadius: 0,
                            }

                        },
                        data: yData2
                    },
                    {
                        name: '项目欠费（亿元）',
                        yAxisIndex: 1,
                        type: 'bar',
                        barWidth: '10px',
                        barGap: 0,
                        itemStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                    offset: 0,
                                    color: '#01c871'
                                }, {
                                    offset: 1,
                                    color: '#55f49c'
                                }]),
                                barBorderRadius: 0,
                            }
                        },
                        data: yData3
                    }
                ]
            };

            yearTrendItemChart.setOption(yearTrendItemOption);
            yearTrendItemChart.on('click', function (params) {
                var provCode1;
                var areaCode1;
                console.log('ictProjectSumVosList',ictProjectSumVosList)
                //柱状图点击事件，省分条件根据是否查询条件是否有值并且查询条件是否使用，省分使用后，跳转时的省分就用查询时的；为使用就用柱状图的
                if (!areaCodeSearch) {
                    areaCode1 = provCodeSearch ? ictProjectSumVosList[params.dataIndex].provCode : '';
                } else {
                    areaCode1 = areaCodeSearch;
                }
                provCode1 = provCodeSearch ? provCodeSearch : ictProjectSumVosList[params.dataIndex].provCode;
                goClickItem(provCode1, areaCode1)
            });
            // 监听图例选择变化事件
            yearTrendItemChart.on('legendselectchanged', function (event) {
                var isSelected = event.selected[event.name]; // 图例的选中状态
                if (event.name == '项目收入（亿元）') {
                    xmsr = isSelected;
                }
                if (event.name == '项目欠费（亿元）') {
                    xmqf = isSelected;
                }
            });
        };


        // ICT项目风险信息像
        window.riskProfileTable = function (data) {
            table.render({
                elem: '#risk_profile_table',
                id: '#risk_profile_table',
                data: data,
                page: false,
                limit: 9999,
                cols: [[
                    {
                        field: 'provName', title: '省分', align: 'center', width: '22%',
                        templet: function (d) {
                            if (d.provName == '小计') {
                                return '<span class="text-red">' + d.provName + '</span>';
                            } else {
                                return d.provName;
                            }
                        }
                    },
                    // { field: 'areaName', title: '地市', align: 'center', width: '100' },
                    {field: 'category', title: '风险大类', align: 'center', width: '16%'},
                    {field: 'subCategory', title: '风险小类', align: 'center', width: '17.9%'},
                    {
                        //htmlEncodeByRegExp
                        field: 'countNum', title: '项目数量', align: 'center', width: '14%', templet: function (d) {
                            if (d.provName == '合计') {
                                return '<div class="goClick" onclick=goClickTableItem("' + $("#provCode").val() + '","' + areaCodeSearch + '","' + d.categoryCode + '","' + d.subCategoryCode + '")>' + d.countNum + '</div>';
                            } else {
                                return '<div class="goClick" onclick=goClickTableItem("' + d.provCode + '","' + areaCodeSearch + '","' + d.categoryCode + '","' + d.subCategoryCode + '")>' + d.countNum + '</div>';
                            }

                        }
                    },
                    {
                        field: 'contractAmount',
                        title: '收入合同金额',
                        align: 'center',
                        width: '10%',
                        style: 'text-align: right;',
                        templet: function (d) {
                            return thousands(d.contractAmount);
                        }
                    },

                    {
                        field: 'projectIncome',
                        title: '已计收金额',
                        align: 'center',
                        width: '10%',
                        style: 'text-align: right;',
                        templet: function (d) {
                            return thousands(d.projectIncome);
                        }
                    },

                    {
                        field: 'projectArrears',
                        title: '欠费金额',
                        align: 'center',
                        width: '10%',
                        style: 'text-align: right;',
                        templet: function (d) {
                            return thousands(d.projectArrears);
                        }
                    }/*,
                        {
                            field: 'ctAmt', title: '冲收金额', align: 'center', width: '8%', style: 'text-align: right;',
                            templet: function (d) {
                                return thousands(d.ctAmt);
                            }
                        },
                        {
                            field: 'agingDate', title: '冲收前账龄', align: 'center', width: '9%', style: 'text-align: right;',
                            templet: function (d) {
                                return thousands(d.agingDate);
                            }
                        },
                        {
                            field: 'monthTotal', title: '冲收后账龄', align: 'center', width: '9%', style: 'text-align: right;',
                            templet: function (d) {
                                return thousands(d.monthTotal);
                            }
                        },
                        {
                            field: 'totalBds', title: '少计提坏账准备', align: 'center', width: '10%', style: 'text-align: right;',
                            templet: function (d) {
                                return thousands(d.totalBds);
                            }
                        }*/,
                ]],
                done: function (res, curr, count) {
                    // mergeTable(res,['provName','category','subCategory'],[0,1,2])
                }
            });
        }

        window.goClickItem = function (provCode, areaCode) {

            closeTab("项目明细");
            var selectedValue = $('input[name="type"]:checked').val();

            $("#tableMenu")
                .data("title", "项目明细")
                .data(
                    "url",
                    '/views/audit/bigdata/ictDiagnosticEquipment/project-detail.html?selectedValue='+selectedValue+'&provCode=' + provCode + '&areaCode=' + areaCode + '&projectDateStart=' + returnParams().projectDateStart + '&projectDateEnd=' + returnParams().projectDateEnd + '&diagnosisPeriod=' + returnParams().diagnosisPeriod + '&accountPeriodStart=' + returnParams().accountPeriodStart + '&accountPeriodEnd=' + returnParams().accountPeriodEnd + '&riskCategoryCode=' + returnParams().riskCategoryCode + '&riskSubCategoryCode=' + returnParams().riskSubCategoryCode).click();
        }


        window.goClickTableItem = function (provCode, areaCode, categoryCode, subCategoryCode) {


            var selectedValue = $('input[name="type"]:checked').val();
            closeTab("项目明细");
            $("#tableMenu")
                .data("title", "项目明细")
                .data(
                    "url",
                    '/views/audit/bigdata/ictDiagnosticEquipment/project-detail.html?selectedValue='+selectedValue+'&provCode=' + provCode + '&areaCode=' + areaCode + '&projectDateStart=' + returnParams().projectDateStart + '&projectDateEnd=' + returnParams().projectDateEnd + '&diagnosisPeriod=' + returnParams().diagnosisPeriod + '&accountPeriodStart=' + returnParams().accountPeriodStart + '&accountPeriodEnd=' + returnParams().accountPeriodEnd + '&riskCategoryCode=' + categoryCode + '&riskSubCategoryCode=' + subCategoryCode).click();
        }


        window.goProjectItem = function () {
            closeTab("按项目编号汇总风险");
            $("#tableMenu")
                .data("title", "按项目编号汇总风险")
                .data(
                    "url",
                    '/views/audit/bigdata/ictDiagnosticEquipment/projectRisk.html').click();
        }

        window.goProjectInfo = function () {
            closeTab("全量合同信息查询");
            $("#tableMenu")
                .data("title", "全量合同信息查询")
                .data(
                    "url",
                    '/views/audit/colligate/contract/list.html').click();
        }

        window.goProjectGys = function () {
            closeTab("按项目供应商汇总风险");
            $("#tableMenu")
                .data("title", "按项目供应商汇总风险")
                .data(
                    "url",
                    '/views/audit/bigdata/ictDiagnosticEquipment/projectGys.html').click();
        }

        window.goProjectKhItem = function () {
            closeTab("按客户名称汇总风险");
            $("#tableMenu")
                .data("title", "按客户名称汇总风险")
                .data(
                    "url",
                    '/views/audit/bigdata/ictDiagnosticEquipment/projectkh.html').click();
        }
        //导出

        window.exportBtn = function () {

            var indexD = layer.load(1, {
                shade: [0.1, "#fff"],
            });

            $.ajax({
                url: ctx + "/ict/exportIctProjectInfo",
                dataType: "JSON",
                type: "POST",
                contentType: "application/json;charset=UTF-8",
                data: JSON.stringify(returnParams()),
                success: function (ret) {
                    if (ret.httpCode == 200) {
                        layer.msg('系统已在后台导出处理，请您耐心等待，稍后请从“工作台--我的工具--模型导出下载”中进行文件下载。<br/>为提高服务器空间利用率，请您及时清理删除已下载的文件，谢谢您的配合！', {
                            time: 0
                            , icon: 6
                            , btn: ['确定']
                        });
                    } else {
                        frm.error(ret.msg);
                    }

                    form.render();
                    layer.close(indexD);
                },
                error: function (e) {
                    console.info(e);
                    layer.close(indexD);
                }
            });
        };

        reloadForm()


        function getNowFormatDate(pre) {
            let date = new Date()
            let year = date.getFullYear() - pre
            let month = date.getMonth() + 1
            if (month < 10) month = '0' + month // 如果月份是个位数，在前面补0
            return year + '-' + month
        }

        function getNowDate() {
            let date = new Date()
            return date.getDate();
        }

        function getMonthAll() {
            let end = getNowFormatDate(0)
            let begin = getNowFormatDate(1)
            var d1 = begin;
            var d2 = end;
            var dateArry = new Array();
            var s1 = d1.split("-");
            var s2 = d2.split("-");
            var mCount = 0;
            if (parseInt(s1[0]) < parseInt(s2[0])) {
                mCount = (parseInt(s2[0]) - parseInt(s1[0])) * 12 + parseInt(s2[1]) - parseInt(s1[1]) + 1;
            } else {
                mCount = parseInt(s2[1]) - parseInt(s1[1]) + 1;
            }
            if (mCount > 0) {
                var startM = parseInt(s1[1]);
                var startY = parseInt(s1[0]);
                for (var i = 0; i < mCount; i++) {
                    if (startM < 12) {
                        dateArry[i] = startY + "" + (startM > 9 ? startM : "0" + startM);
                        startM += 1;
                    } else {
                        dateArry[i] = startY + "" + (startM > 9 ? startM : "0" + startM);
                        startM = 1;
                        startY += 1;
                    }
                }
            }
            dateArry.reverse()
            return dateArry;
        }

        function getMonthArr(date, n) {
            let dateArr = [];
            let year = date.substr(0, 4);
            let month = date.substr(-2, 2);
            let m = Number(month);
            console.log(year)
            console.log(m)

            if (n < m) {
                //1.n<month的情况
                for (let i = m - n + 1; i <= month; i++) {
                    let m1 = i < 10 ? "0" + i : i;
                    dateArr.push(year + '' + m1);
                }
            } else {
                //2.n>month的情况
                for (let i = (12 - (n - m) + 1); i <= 12; i++) {
                    let m1 = i < 10 ? "0" + i : i;
                    dateArr.push((year - 1) + '' + m1);
                }
                for (let i = 1; i <= m; i++) {
                    let m1 = i < 10 ? "0" + i : i;
                    dateArr.push(year + '' + m1);
                }
            }
            return dateArr;

        }


    })


</script>

</body>
