<!--
	合同明细页面
-->
<!--#include virtual ="include/header.html"-->
<style>
    .search-condition{
        padding-right: 252px!important;
    }
    .search-btn{
        width: 228px!important;
    display: flex;
    align-items: center;
    top: 35px!important;
    height: 35px!important;
    }
    .search-btn .layui-form-item{
        margin-right:8px
    }
    .search-btn .layui-form-item:last-child{
        margin-right: 0px;
    }
</style>
<body onload="loadBodyInfo()">
<!-- 查询条件 -->
<div class="layui-fluid larry-wrapper">
    <section class="panel panel-padding">
        <form class="layui-form layui-form-pane form-conmon form-conmon-more" data-params='{"bind":true}'
              id="cgDetailForm">
            <!--<input id="monthIdStart" name="monthIdStart" type="hidden">
            <input id="monthIdEnd" name="monthIdEnd" type="hidden">-->
            <input id="vendorName" name="vendorName" type="hidden">
            <input id="vendorId" name="vendorId" type="hidden">
            <input id="provCode" name="provCode" type="hidden">
            <!--<input id="contractCreatetimeStart" name="contractCreatetimeStart" type="hidden">
            <input id="contractCreatetimeEnd" name="contractCreatetimeEnd" type="hidden">-->
            <input id="hi_name" name="contractName" type="hidden">
            <input id="hi_purid" name="purid" type="hidden">
            <div class="layui-row layui-col-space10 transition-500ms search-condition">
                <!--<div class="layui-col-md4 layui-col-sm4">
                    <div class="layui-form-item layui-form-item-sm">
                        <label class="layui-form-label">截止账期</label>
                        <div class="layui-input-block">
                            <input readonly type = "text" class="layui-input" id="monthIds" lay-filter = "monthIds"  placeholder="账期"/>
                        </div>
                    </div>
                </div>-->
                <div class="layui-col-md4 layui-col-sm4" id="provDivId" style="display:none;">
                    <div class="layui-form-item layui-form-item-sm">
                        <label class="layui-form-label">省分</label>
                        <div class="layui-input-block">
                            <select id="provId" lay-filter="provId" lay-search="" name="provId">
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md4 layui-col-sm4">
                    <div class="layui-form-item layui-form-item-sm">
                        <label class="layui-form-label">地市</label>
                        <div class="layui-input-block">
                            <select id="eparchyCode" lay-filter="eparchyCode" lay-search="" name="areaCode">
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md4 layui-col-sm4">
                    <div class="layui-form-item layui-form-item-sm">
                        <label class="layui-form-label">合同名称</label>
                        <div class="layui-input-block">
                            <input class="layui-input" id="name" lay-filter="segment1" placeholder="合同名称" type="text">
                        </div>
                    </div>
                </div>
                <!--<div class="layui-col-md4 layui-col-sm4">-->
                <!--<div class="layui-form-item layui-form-item-sm">-->
                <!--<label class="layui-form-label">供应商名称</label>-->
                <!--<div class="layui-input-block">-->
                <!--<input type="text" class="layui-input" id="orgName"  lay-filter = "orgName" placeholder="供应商名称">-->
                <!--</div>-->
                <!--</div>-->
                <!--</div>-->
                <div class="layui-col-md4 layui-col-sm4">
                    <div class="layui-form-item layui-form-item-sm">
                        <label class="layui-form-label">合同编号</label>
                        <div class="layui-input-block">
                            <input class="layui-input" id="contractGlobalsn" lay-filter="contractGlobalsn" name="contractGlobalsn"
                                   placeholder="合同编号" type="text"/>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md4 layui-col-sm4">
                    <div class="layui-form-item layui-form-item-sm">
                        <label class="layui-form-label">采购流水号</label>
                        <div class="layui-input-block">
                            <input class="layui-input" id="purid" lay-filter="purid" placeholder="采购流水号" type="text">
                        </div>
                    </div>
                </div>



                <div class="search-btn">
                    <div class="layui-form-item">
                        <button class="layui-btn layui-btn-sm" id="searchBtn" onclick="queryDetailTableList();"
                                type="button"><i class="iconfont search-icon">&#xe61b;</i> 查询
                        </button>
                    </div>
                    <div class="layui-form-item">
                        <button class="layui-btn layui-btn-danger layui-btn-sm" onclick="resetForm()" type="button"><i
                                class="iconfont search-icon">&#xe63a;</i> 重置
                        </button>
                    </div>
                    <div class="layui-form-item" >
                        <button class="layui-btn btn-outline layui-btn-warm layui-btn-sm" id="exportBtn" onclick="backgroundExport('csv');"
                                type="button">
                            <i class="iconfont search-icon">&#xe60c;</i> 导出
                        </button>
                    </div>
                </div>
            </div>

        </form>

        <!-- 数据区 -->
        <div class="layui-form">

            <div class="layui-row">
                <table class="layui-table jq-even" id="DetailTable" lay-filter="DetailTable"></table>
            </div>
        </div>
    </section>
</div>

</body>
<!--#include virtual ="include/tpl/select-cat.html"-->
<!--#include virtual ="include/version.html"-->
<style>
    .layui-table-cell {
        height: auto;
        line-height: 28px;
        padding: 0 0px;
        position: relative;
        overflow: hidden;
        text-overflow: ellipsis;
    }
</style>

<script id="toolBar" type="text/html">
    <a class="table-btn" lay-event="detail" title="查看">
        <i class="iconfont">&#xe651;</i>
    </a>
</script>

<script type="text/javascript">
    function loadBodyInfo() {
        var $, ctx;
        layui.use(['jquery', 'jqbind', 'table', 'jqfrm', 'jqform', 'layer', 'laydate', 'laytpl'], function () {
            $ = layui.jquery;
            ctx = top.global.ctx;
            var form = layui.jqform,
                layer = layui.layer,
                tpl = layui.laytpl,
                laydate = layui.laydate,
                table = layui.table;
            /**
             * 重置功能
             */
            window.resetForm = function () {
                // var myInput = document.getElementById("monthIds");
                // myInput.defaultValue = $("#monthIds").attr("defaultValue");
                /* $("#contractCreatetimeStart").val("");
                 $("#contractCreatetimeEnd").val("");*/
                document.getElementById("cgDetailForm").reset()
                queryDetailTableList();
            };
            //去左右空格;
            window.trimLeftAndRight = function () {
                $("input[type='text']").each(function () {
                    $("#hi_" + this.id).val(this.value.trim());
                });
            };
            /**
             * 合同创建日期
             */
            /*laydate.render({
                elem: '#contractCreatetime'
                ,range: '~'
                ,format: 'yyyy-MM-dd'
                ,done: function(value, date, endDate) {
                    if(value!=null){
                        var arr=value.split('~');
                        $("#contractCreatetimeStart").val($.trim(arr[0]));
                        $("#contractCreatetimeEnd").val($.trim(arr[1]));
                    }
                }
            });*/
            //查询数据区
            var tableIns, cols;
            window.queryDetailTableList = function () {
                var indexZG = layer.load({
                    type: 3
                });
                trimLeftAndRight();
                tableIns = table.render({
                    elem: '#DetailTable'
                    , url: ctx + '/online/cooperationAnalyze/contractRecordDetail'
                    , where: transformToJson($("#cgDetailForm").serializeArray())
                    , cols: getTitleName()
                    , height: 'full-140'
                    , even: true
                    , page: true
                });
            };


            window.getTitleName = function () {
                cols = [[
                    {type: 'numbers', title: '序号', align: 'center', width: '40'}
                    , {field: 'PROV_NAME', title: '省分', align: 'center', width: '70'}
                    , {field: 'AREA_NAME', title: '地市', align: 'center', width: '90'}
                    , {
                        field: 'CONTRACT_GLOBALSN',
                        title: '合同编号',
                        align: 'center',
                        width: '420',
                        style: "text-align:left"
                    }
                    , {field: 'CONTRACT_NAME', title: '合同名称', align: 'center', width: '420', style: "text-align:left"},
                    {field: 'CONTRACT_NOTE', title: '合同说明', align: 'center', width: '120'},
                    {field: 'CLASSIFYNAME', title: '合同类型', align: 'center', width: '120'},
                    {field: 'WE', title: '我方主体', align: 'center', width: '120'},
                    {field: 'OTHERSIDE', title: '对方全称', align: 'center', width: '120'},
                    {field: 'CURRENCYTYPE', title: '合同币种编码', align: 'center', width: '120'},
                    {field: 'HR_ORG_CODE', title: '部门编号', align: 'center', width: '120'},
                    {field: 'HR_ORG_NAME', title: '部门名称', align: 'center', width: '120'},
                    {field: 'EMP_HR', title: '合同承办人的HR员工号', align: 'center', width: '120'},
                    {field: 'EMP_NAME', title: '合同承办人的HR的姓名', align: 'center', width: '120'},
                    {field: 'SUM', title: '含增值税合同金额', align: 'center', width: '120'},
                    {field: 'UNVATAMOUNT', title: '不含增值税合同额', align: 'center', width: '120'},
                    {field: 'VAT_RATE', title: '增值税税率', align: 'center', width: '120'},
                    {field: 'VATAX', title: '增值税税额', align: 'center', width: '120'},
                    {field: 'VENDOR_CODE', title: '对方编码', align: 'center', width: '120'},
                    {field: 'VENDOR_NAME', title: '对方名称', align: 'center', width: '120'},
                    {field: 'LOCALSN', title: '合同流水号', align: 'center', width: '120'},
                    {field: 'PURID', title: '采购合同流水号', align: 'center', width: '120'},
                    {field: 'PURCHASERESULTID', title: '采购结果编号', align: 'center', width: '120'},
                    {field: 'COMANYNAME', title: '所属组织', align: 'center', width: '120'},
                    {field: 'PROFESSIONALTYPE', title: '专业类型', align: 'center', width: '120'},
                    {field: 'CONTRACTSTATUS', title: '合同状态编码', align: 'center', width: '120'},
                    {field: 'APPROVEENDTIME', title: '合同审批完成时间', align: 'center', width: '120'},
                    {field: 'SIGNDATE', title: '签订盖章时间', align: 'center', width: '120'},
                    {field: 'STARTDATE', title: '合同履行开始日期', align: 'center', width: '120'},
                    {field: 'ENDDATE', title: '合同履行结束日期', align: 'center', width: '120'},
                    {field: 'CREATETIME', title: '合同起草时间', align: 'center', width: '120'},
                    {field: 'PERFORMERNAME', title: '合同履行人', align: 'center', width: '120'},
                    {field: 'EMAIL', title: '合同承办人邮箱前缀', align: 'center', width: '120'},
                    {field: 'RATE', title: '合同付款比例', align: 'center', width: '120'},
                    {field: 'CHANGETYPE', title: '关联类型编码', align: 'center', width: '120'},
                    {field: 'ISMALL', title: '商城协议/合同编码', align: 'center', width: '120'},
                    {field: 'PAYMENTMETHOD', title: '采购协议类型编码', align: 'center', width: '120'},
                    {field: 'ENTERPERFORMTIME', title: '合同进入履行时间', align: 'center', width: '120'},
                    {field: 'TEMPLATENAME', title: '标准文本名称', align: 'center', width: '120'},
                    {field: 'TEMPLATE_ID', title: '标准文本属性', align: 'center', width: '120'},
                    {field: 'IS_STRUCT', title: '是否结构化', align: 'center', width: '120'},
                    {field: 'ENFORCE', title: '使用范围', align: 'center', width: '120'},
                    {field: 'USETYPE', title: '标准文本使用类型', align: 'center', width: '120'},
                    {title: '操作', width: '70', align: 'center',toolbar: '#toolBar', fixed: 'right' },
                ]];
                return cols;
            };
            /**
             * 页面初始化
             */
            //表单绑定前查询数据操作
            form.beforeBind = function () {
                //账期
                /*var montStart=$("#monthIdStart").val();
                if(montStart!=""){
                    var monthVal=$("#monthIdEnd").val();
                    $("#monthIds").val(monthVal);
                    $("#monthIds").attr("defaultValue",monthVal);
                }*/
                var provId = $("#provCode").val();
                $.ajax({
                    url: ctx + "/online/partnersNewDetail/queryProvOrAreaList"
                    , dataType: "JSON"
                    , type: "POST"
                    , contentType: "application/json;charset=UTF-8"
                    , data: JSON.stringify({provCode: provId})
                    , success: function (data) {
                        var comValue = data.data;
                        var list = comValue.list;
                        //渲染下拉列表
                        var getTplArea = $('#select-tpl').html();
                        var list = {list: list};
                        if (provId == null || provId == '') {
                            //显示省分下拉
                            $("#provDivId").show();
                            tpl(getTplArea).render(list, function (html) {
                                $("#provId").html(html);
                            });
                        } else {
                            //不显示省分下来，只显示地市下拉
                            $("#provDivId").hide();
                            tpl(getTplArea).render(list, function (html) {
                                $("#eparchyCode").html(html);
                            });
                        }

                        form.render('select');
                    }

                });
                queryDetailTableList();
            };

            /**
             * 根据省分查询地市
             */
            form.on("select(provId)", function (data) {
                var provId = data.value;
                $("#provCode").val(provId);
                //根据省分查地市
                $.ajax({
                    url: ctx + "/online/partnersNewDetail/queryProvOrAreaList"
                    , dataType: "JSON"
                    , type: "POST"
                    , contentType: "application/json;charset=UTF-8"
                    , data: JSON.stringify({provCode: provId})
                    , success: function (data) {
                        var comValue = data.data;
                        var areaList = comValue.list;
                        //渲染地市下拉列表
                        var getTplArea = $('#select-tpl').html();
                        var areaList = {list: areaList};
                        tpl(getTplArea).render(areaList, function (html) {
                            $("#eparchyCode").html(html);
                        });
                        form.render('select');
                    }
                })

            });

            form.init({
                "form": "#cgDetailForm"
            });


            //后台导出方法
            window.backgroundExport = function (exportFileType) {
                window.location.href = ctx + "/online/cooperationAnalyze/exportContractRecords?" +
                    "contractGlobalsn=" + $("[name='contractGlobalsn']").val() +
                    "&contractName=" + $("[name='contractName']").val() +
                    "&provCode=" + $("[name='provCode']").val() +
                    "&areaCode=" + $("[name='areaCode']").val() +
                    "&purid=" + $("[name='purid']").val() +
                    "&vendorName=" + $("[name='vendorName']").val();
            }

            //监听操作
            table.on('tool(DetailTable)', function(obj){
                var data = obj.data; //获得当前行数据
                var layEvent = obj.event; //获得 lay-event 对应的值（也可以是表头的 event 参数对应的值）

                if(layEvent == 'detail'){ //选择文件
                    var indexs = top.layer.open({
                        title: '附件列表',
                        content:
                            "/jtaudithtml/views/audit/colligate/contract/dialog.html?contractGlobalsn=" + data.CONTRACT_GLOBALSN,
                        type: 2,
                        area: ["70%", "80%"],
                        fixed: true,
                        maxmin: false,
                        resize: false,
                    });
                }
            });
        });
    }
</script>
</html>
