<!-- 风研成果 -->
<!--#include virtual ="include/header.html"-->
<link href="resource/css/ordinary-welcome.css" rel="stylesheet">
<!-- <link rel="stylesheet" href="resource/css/lead.css"> -->
<link href="resource/css/style-red.css" id="linkid" rel="stylesheet" type="text/css">
<link href="resource/css/new-style.css?v=6.5" media="all" rel="stylesheet" type="text/css">
<link href="resource/css/style.css?v=6.5" rel="stylesheet" type="text/css">
<link href="resource/css/enterLine.css" media="all" rel="stylesheet" type="text/css">
<link rel="stylesheet" type="text/css" href="resource/css/formSelects-v4.css" />
<style>
   .one-row{display:flex;align-items:center;}
.one-row-left{height:32px;line-height:32px;width:90px;text-align:right;padding-right:6px;box-sizing: border-box}
.one-row:last-child{position:relative;}
.row-table .one-row-table{position:relative;cursor:pointer;margin-right:8px;display:inline-block;padding:0px 20px;height:30px;line-height:30px;border:1px solid #D9D9D9;border-radius:1px;font-size:14px;color:#333333;font-weight:400;white-space:nowrap;min-width:150px;text-align:center;}
.row-table .one-row-table .top-title{height:20px;line-height:20px;display:block;position:absolute;top:-14px;left:5px;text-align:center;background:#fff;padding:0px 10px;color:#c20000;font-size:12px;display:none;}
.row-table .one-row-table .top-close{font-size:22px;color:#c20000;position:absolute;top:-12px;;right:-9px;background:#fff;padding:0px 5px;cursor:pointer;height:20px;line-height:20px;display:none;}
.last-btn{}
.last-btn-one{display:inline-block;cursor:pointer;height:28px;line-height:28px;border:1px solid #c20000;box-sizing:border-box;border-radius:4px;padding:0px 8px;}
.last-btn-one .iconfont{color:#c20000;font-size:13px;margin-right:4px;}
.last-btn-one span:last-child{font-size:13px;font-family:PingFang SC;font-weight:400;color:#c20000;}
.last-btn-one:last-child{margin-right:0px;}
form.layui-form{display:flex;align-items:center;width:100%;}
.layui-form-select{height:32px;box-sizing:border-box;border-radius:1px;width:100%;}
.layui-form-select input{height:32px;border:1px solid #D9D9D9;box-sizing:border-box;border-radius:1px;background-color:transparent !important;width:100%;}
.bottom-table{margin-top:12px;padding-bottom:15px;}
.row-table .one-row-table:hover .open-popup{display:block;}
.open-popup{min-width:400px;position:absolute;top:31px;left:0px;background-color:#fff;border-radius:4px;box-shadow:0px 0px 10px 0px rgb(155 11 9 / 10%);padding:15px;z-index:9999;min-height:40px;display:none;text-align:left;}
.open-popup .one-open-grid{cursor:pointer;margin-right:8px;display:inline-block;padding:0px 20px;height:30px;line-height:30px;border:1px solid #D9D9D9;border-radius:1px;font-size:14px;color:#333333;font-weight:400}
.open-popup .one-open-grid-checked{color:#c20000;border-color:#c20000;}
.icon-bianji{color:#c20000;font-size:16px;cursor:pointer;}
/*.layui-table[lay-even] tr:nth-child(even){background-color:#fff !important;}*/
.icon-xingxing1{font-size:16px;color:#FFB955;}
.row-table{flex:1}
.layui-input,.layui-textarea,.layui-select{height:32px;}
.my-layui-box{padding:15px;box-sizing:border-box;padding-left:0px;padding-bottom:0px;}
.my-row-style{flex:1;margin-right:20px}
.one-rows{display:flex;}
.search-box{flex:1;padding-bottom: 10px;box-sizing: border-box;}
.layui-form-checkbox{position:relative;display:inline-block;vertical-align:middle;height:24px;line-height:24px;margin-right:5px;/* padding-right:30px;*/
 border:1px solid #d2d2d2;background-color:#fff;cursor:pointer;font-size:0;border-radius:4px;-webkit-transition:.1s linear;transition:.1s linear;box-sizing:border-box;width:24px;padding-right:0px;}
.layui-form-checkbox i{width:24px;font-size:13px;}
.xm-select-title div.xm-select-label>span{border-color:#c20000 !important;}
.xm-select-parent .xm-select-title div.xm-select-label>span{background-color:#c20000}
.xm-select-parent dl dd.xm-select-this div i{color:#c20000}
/*.ie-header{*/
 /*position:absolute;*/
 /*top:0;*/
 /*}
*/
 .layui-table-cell{/* padding:0 0 0 15px;*/
}
.topcon{padding:0 10px;}
.layui-select-btn-span{margin-right:22px;}
.order4-top{width:100%;height:141px;background:#FFFFFF;border-radius:4px;padding:11px;box-sizing:border-box;}
.order4-top-li{width:50%;float:left;height:60px;padding:5px;box-sizing:border-box;}
.order4-top-li-bg{width:100%;height:100%;background:#F6F7F8;line-height:50px;padding:0 24px;box-sizing:border-box;display:flex;justify-content:space-between;}
.order4-top-li-left{font-size:15px;font-family:PingFangSC-Regular,PingFang SC;font-weight:400;color:#333333;overflow:hidden;}
.order4-top-li-right{font-size:20px;font-family:PingFangSC-Medium,PingFang SC;font-weight:500;color:#c20000;}
.rightone{height:141px;padding-right:0 !important;background-image:url(resource/images/welcomefour1.png);background-size:100% 100%;box-shadow:0px 0px 10px 0px rgba(155,11,9,0.1);border-radius:4px;padding-top:15px;box-sizing:border-box;}
.manman{margin-top:90px;}
.orderTitle{padding-left:10px;}
.tplText{}
.tplNum{color:#333333;font-size:14px;width:15%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;display:flex;align-items:center;justify-content:flex-end;font-family:Helvetica;}
.achieveBox .layui-row{border-bottom-width:0;padding:8px 0;padding-right:32px;}
.achieveBoxF,.achieveBoxG{height:268px;}
.achieveBoxG .center-btn{padding:26px 0 6px;}
#container1,#container2,#container3{height:268px;}
#middleCharts{margin-top:0;width:100%;height:100%;}
.middle-charts{width:323px;height:380px;background:#FFFFFF;box-shadow:0px 0px 7px 0px rgba(0,0,0,0.09);border-radius:2px;}
.middleList{width:100%;margin-top:12px;}
.overflowhidden-2{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;}
.middleList .layui-border-box.layui-table-view{margin-top:0 !important;}
.middleList .layui-table{margin:0}
.middleList .layui-table tr td{padding:2px 0px;}
.middleList .layui-table tr td .layui-table-cell{height:48px;display:flex;align-items:center;overflow:hidden;text-overflow:inherit;white-space:initial;box-sizing:border-box;font-size:14px;font-family:PingFangSC-Regular,PingFang SC;font-weight:400;color:#333333;}
.middleList .layui-table-header th,.middleList .layui-table-header{background-color:#F4F8FC !important;}
.middleList .layui-table th,.layui-table td,.layui-table[lay-skin="line"],.layui-table[lay-skin="row"],.layui-table-view,.layui-table-header,.layui-table-tool,.layui-table-page,.layui-table-fixed-r,.layui-table-tips-main{border-color:#DDD !important;}
.middleList thead .layui-table-cell{font-weight:initial;color:#000;}
.middleList .layui-table tbody tr:hover,.middleList .task-todo.html .layui-table-hover,.middleList .layui-table-click{background:#fff;}
.preliminary_assessment{width:100%;}
.preliminary_assessment li{width:100%;}
.preliminary_assessment li .content{display:inline-block;width:calc(100% - 100px);overflow:hidden;height:20px;line-height:20px;}
.preliminary_assessment li .span1,.preliminary_assessment li .span2{font-size:13px;overflow:hidden;font-family:PingFangSC-Regular,PingFang SC;font-weight:400;display:inline-block;line-height:20px;padding-left:7px;box-sizing:border-box;width:60px;height:20px;border-radius:1px;}
.preliminary_assessment li .span1{color:#FF8404;background:rgba(255,176,94,0.1);}
.preliminary_assessment li .span2{color:#238CDB;background:rgba(111,154,205,0.1);}
.flex-header{display:flex;align-items:center;justify-content:space-between;}
.flex-header .width-span{width:74px;}
.flex-header .order-button-update{width:74px;text-align:right;}
.flex-header .layui-form-radioed>i,.flex-header .layui-form-radio>i:hover{color:#c20000;}
.flex-header .layui-form-radio *{display:inline-block;vertical-align:middle;font-weight:400;}
.left-table-height{height:300px;}
.order-4-table{width:100%;padding:8px 0;box-sizing:border-box;}
.order-4-table .order-4-table-header{width:100%;height:44px;background:#F4F8FC;border-bottom:1px solid #E6EAEE;}
.order-4-table .order-4-table-th{float:left;width:20%;height:44px;line-height:44px;font-size:14px;font-family:PingFangSC-Medium,PingFang SC;font-weight:bold;box-sizing:border-box;color:#333333;}
.order-4-table-th1{padding-left:38px;width:22% !important;}
.order-4-table-th2{padding-left:38px;}
.order-4-table-th3{padding-left:38px;width:15% !important;}
.order-4-table-th4{padding-right:38px;text-align:right;width:15% !important}
.order-4-table-th5{padding-right:38px;text-align:right;width:15% !important}
.order-4-table-th6{padding-right:38px;text-align:right;width:12% !important}
.order-4-table .order-4-table-body{height:240px;overflow-y:auto;width:100%;}
.order-4-table .order-4-table-tr{border-bottom:1px solid #E6EAEE;width:100%;}
.order-4-table .order-4-table-td{float:left;width:20%;height:44px;line-height:44px;font-size:14px;font-family:PingFangSC-Medium,PingFang SC;font-weight:500;box-sizing:border-box;color:#333333;}
#personChart,#achievesearchChart{padding:8px 0;}
.person-header{font-size:13px;font-weight:bold;text-align:center;height:44px;background:#f5f8fc;line-height:44px;}
#personChart1 .layui-row:nth-child(even),#personChart2 .layui-row:nth-child(even),#achievesearchChart1 .layui-row:nth-child(even),#achievesearchChart2 .layui-row:nth-child(even){background:#fff;}
.order-4-jifen .layui-row.layui-row-check{width:100%;margin-top:6px;box-sizing:border-box;border:1px solid #E6EAEE;}
.person-data{font-size:13px;height:36px;line-height:36px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;}
.layui-card-header .layui-select-brn1{width:calc(100% - 148px);text-align:center;}
.layui-card-header .layui-select-brn2{width:calc(100% - 252px);text-align:center;}
.order4-top-bg-1{background:url('resource/images/bigdata/fybg.png') center center no-repeat;background-size:100% 100%;border-radius:6px;width:100%;height:100%;padding:20px 25px;display:flex;align-items:center;box-sizing:border-box;justify-content:space-between;}
.order4-top-bg-2{border-radius:6px;width:100%;height:100%;padding:20px 25px;display:flex;align-items:center;box-sizing:border-box;justify-content:space-between;border:1px solid #FF6E6E;}
.items-project-one{margin-right:25px;display:flex;flex-direction:column;align-items:center;justify-content:center;}
.items-project-one:last-child{margin-right:0px;}
.items-project-one .items-project-one-top{font-size:16px;font-family:PingFangSC-Regular,PingFang SC;font-weight:400;color:#333333;line-height:22px;text-align:center;}
.items-project-one .items-project-one-bottom{font-size:28px;font-family:PingFangSC-Medium,PingFang SC;font-weight:500;color:#333333;line-height:40px;margin-top:8px;text-align:center;}
.order4-top-bg-1 .iconfont{color:#c20000;font-size:24px;position:relative;top:15px;left:-15px;}
.icon-line{width:1px;height:41px;background-color:#FF6E6E;}
.header-btn-1{height:28px;background:#FCF8F8;border:1px solid #c20000;border-radius:2px;line-height:28px;padding:0px 15px;font-size:14px;font-family:PingFangSC-Regular,PingFang SC;font-weight:400;color:#c20000;box-sizing:border-box;margin-left:36px;margin-right:12px;cursor:pointer;}
.header-btn-2{height:28px;background:#FCF8F8;border:1px solid #c20000;border-radius:2px;line-height:28px;padding:0px 15px;font-size:14px;font-family:PingFangSC-Regular,PingFang SC;font-weight:400;color:#c20000;box-sizing:border-box;cursor:pointer;}
.header-btn-1.actived,.header-btn-2.actived{height:28px;background:#c20000;border-radius:2px;line-height:28px;padding:0px 15px;font-size:14px;font-family:PingFangSC-Regular,PingFang SC;font-weight:400;color:#FFFFFF;cursor:pointer;}
#btns-group{display:flex;align-items:center;margin-right:25px;}
#btns-group .header-btn-2{margin-right:12px;}
#btns-group .header-btn-2:last-child{margin-right:0px;}
.stars{font-size:18px;width:100%;}
.icon-xingxing1{font-size:16px;color:#FFB955;}
.layui-table tbody tr:first-child>td>div{text-align:center !important;}
.layui-table tbody tr:first-child>td>div>div{text-align:center !important;}
.layui-table tbody tr:first-child>td>div{height:45px !important;}
td[data-field='initialFocusRiskProv']>div{display:block !important;padding:0px !important;}
td[data-field='initialFocusRiskProv']>div div{padding:0px 10px;border-bottom:1px solid #DDD !important;height:48px;line-height:22px;text-align:left;text-overflow:-o-ellipsis-lastline;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;}
td[data-field='initialFocusRiskProv']>div div:last-child{border-bottom:none !important;}
td[data-field='initialFocusRiskProv'] .initialFocusRiskProv{    display: flex;align-items: center;justify-content: center;}
td[data-field='initialFocusRisk']>div{display:block !important;padding:0px !important;}
td[data-field='initialFocusRisk']>div div{padding:0px 10px;border-bottom:1px solid #DDD !important;height:48px;line-height:22px;text-align:left;text-overflow:-o-ellipsis-lastline;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;}
td[data-field='initialFocusRisk']>div div:last-child{border-bottom:none !important;}
@media only screen and (min-width:1440px) and (max-width:1600px){.items-project-one .items-project-one-top{font-size:14px;}
.items-project-one .items-project-one-bottom{font-size:20px;}
}
@media only screen and (min-width:1200px) and (max-width:1440px){.my-layui-box{padding-right:8px;}
.one-checkbox>label{font-size:12px;}
.layui-form-checkbox{position:relative;display:inline-block;vertical-align:middle;height:20px;line-height:20px;margin-right:5px;/* padding-right:30px;*/
 border:1px solid #d2d2d2;background-color:#fff;cursor:pointer;font-size:0;border-radius:4px;-webkit-transition:.1s linear;transition:.1s linear;box-sizing:border-box;width:20px;padding-right:0px;}
.layui-form-checkbox i{width:20px;font-size:12px;}
.one-row-table{margin-bottom:5px;}
}
@media only screen and (min-width:1440px) and (max-width:1600px){.my-layui-box{padding-right:8px;}
.one-row-table{margin-bottom:5px;}
.one-checkbox>label{font-size:12px;}
.layui-form-checkbox{position:relative;display:inline-block;vertical-align:middle;height:20px;line-height:20px;margin-right:5px;/* padding-right:30px;*/
 border:1px solid #d2d2d2;background-color:#fff;cursor:pointer;font-size:0;border-radius:4px;-webkit-transition:.1s linear;transition:.1s linear;box-sizing:border-box;width:20px;padding-right:0px;}
.layui-form-checkbox i{width:20px;font-size:12px;}
}
.layui-common-card{background-color:#fff;border-radius:4px;box-shadow:0px 0px 10px 0px rgba(155,11,9,0.1);}
.layui-layer-content-my{
    background: none;
    padding: 0;
    box-shadow: 0 1px 6px rgba(0,0,0,.1);
    position: relative;
    line-height: 22px;
    min-width: 12px;
    font-size: 12px;
    _float: left;
    border-radius: 4px;
    color: #fff;
    position: absolute;
}
.layui-table-tips-main-my{
    max-height: 90%;
    padding: 8px 15px;
    padding-right: 18px;
    font-size: 14px;
    overflow-y: auto;
    background-color: #fff;
    color: #333;
    border-color: #dcdcdc;
    border-width: 1px;
    border-style: solid;
    margin-top: -10px;
}
.layui-table-tips-main-my .icon-guanbishixin{
    position: absolute;
    right: -6px;
    top: -14px;
    color:#666;
    font-size: 24px;
    cursor: pointer
}

</style>

<body>
    <div class="layui-fluid larry-wrapper audit-fluid" style="padding-top:10px;">
        <div class="tab-menu">
            <a data-parent="true" href="javascript:" id="tableMenu" style="color: #c00;text-decoration:underline;">
                <i class="iconfont" data-icon=""></i>
            </a>
        </div>
        <div class="layui-row layui-common-card my-layui-box">
            <div class="one-rows">
                <div class="search-box">
                    <form class="layui-form ">
                        <div class="layui-row my-row-style">
                            <div class="layui-col-md2 layui-col-sm2 layui-col-lg2">
                                <div class="one-row">
                                    <div class="one-row-left">风研账期</div>
                                    <div class="row-table">
                                        <input class="layui-input" id="payment" name="payment" readonly placeholder="账期"
                                            type="text">
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md2 layui-col-sm2 layui-col-lg2">
                                <div class="one-row">
                                    <div class="one-row-left">积分产生账期</div>
                                    <div class="row-table">
                                        <input class="layui-input" id="scoreTime" name="scoreTime" readonly placeholder="账期"
                                               type="text">
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md2 layui-col-sm2 layui-col-lg2">
                                <div class="one-row">
                                    <div class="one-row-left">来源</div>
                                    <div class="row-table">
                                        <select id="spanTypeName" lay-filter="spanTypeName" lay-search=""
                                            name="spanTypeName"></select>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md2 layui-col-sm2 layui-col-lg2">
                                <div class="one-row">
                                    <div class="one-row-left">业务领域</div>
                                    <div class="row-table">
                                        <select id="fieldCode" lay-filter="fieldCode" lay-search=""
                                                name="fieldCode"></select>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md2 layui-col-sm2 layui-col-lg2">
                                <div class="one-row">
                                    <div class="one-row-left">专题</div>
                                    <div class="row-table">
                                        <input class="layui-input" id="topicName" name="topicName"  placeholder="专题"
                                               type="text">
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md2 layui-col-sm2 layui-col-lg2">
                                <div class="one-row">
                                    <div class="one-row-left">子专题</div>
                                    <div class="row-table">
                                        <input class="layui-input" id="subTopicName" name="subTopicName"  placeholder="子专题"
                                               type="text">
                                    </div>
                                </div>
                            </div>

                        </div>




                    </form>
                    <div class="layui-col-md12 layui-col-sm12 refer-content" style="margin-top: 10px;">
                        <a class="btnMore" href="javascript:">
                            <span>更多搜索</span>
                            <i class="iconfont slide-down">&#xe603;</i>
                            <i class="iconfont slide-up noneJaudit">&#xe605;</i>
                        </a>
                    </div>
                    <div class="layui-row layui-col-space10 search-more  noneJaudit">
                    <form class="layui-form"  id="searchbox2">
                        <div class="layui-row my-row-style">

                            <div class="layui-col-md2 layui-col-sm2 layui-col-lg2">
                                <div class="one-row">
                                    <div class="one-row-left">对口处室</div>
                                    <div class="row-table layui-form" style="display: flex;">
                                        <select lay-search="" id="departmentCode" lay-filter="departmentCode" required="required"
                                                type="text"></select>
                                    </div>
                                </div>
                            </div>

                            <div class="layui-col-md2 layui-col-sm2 layui-col-lg2">
                                <div class="one-row">
                                    <div class="one-row-left">类型</div>
                                    <div class="row-table layui-form" style="display: flex;">
                                        <select id="topicType" lay-search="" lay-filter="topicType" required="required"
                                                type="text"></select>
                                    </div>
                                </div>
                            </div>

                            <div class="layui-col-md2 layui-col-sm2 layui-col-lg2">
                                <div class="one-row">
                                    <div class="one-row-left">聚焦风险</div>
                                    <div class="row-table">
                                        <input class="layui-input" id="initialFocusRisk" name="initialFocusRisk"
                                            type="text">
                                    </div>
                                </div>
                            </div>


                            <div class="layui-col-md2 layui-col-sm2 layui-col-lg2">
                                <div class="one-row">
                                    <div class="one-row-left">虚拟团队</div>
                                    <div class="row-table">
                                        <select id="groupId" lay-filter="groupId" lay-search="" required="required"
                                            type="text"></select>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md2 layui-col-sm2 layui-col-lg2">
                                <div class="one-row">
                                    <div class="one-row-left">负责人</div>
                                    <div class="row-table">
                                        <input class="layui-input" id="principalName" name="principalName" type="text">
                                    </div>
                                </div>
                            </div>

                            <div class="layui-col-md2 layui-col-sm2 layui-col-lg2">
                                <div class="one-row">
                                    <div class="one-row-left">积分值</div>
                                    <div class="row-table">
                                        <input class="layui-input" id="scoreValue" name="scoreValue"  type="number">
                                    </div>
                                </div>
                            </div>




                        </div>




                    </form>
                    <form class="layui-form" style="margin-top: 10px;margin-bottom:8px;" id="searchbox3">
                        <div class="layui-row my-row-style">
                            <div class="layui-col-md2 layui-col-sm2 layui-col-lg2">
                                <div class="one-row">
                                    <div class="one-row-left">分析人员</div>
                                    <div class="row-table">
                                        <input class="layui-input" id="analystPersonName" name="analystPersonName" type="text">
                                    </div>
                                </div>
                            </div>

                            <div class="layui-col-md4 layui-col-sm4 layui-col-lg4">
                                <div class="one-row" style="align-items: baseline">
                                    <div class="one-row-left">涉及单位</div>
                                    <div class="row-table">
                                        <input class="layui-input" id="involveProvinceName" name="involveProvinceName"  placeholder="涉及单位"
                                               type="text">

                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md6 layui-col-sm6 layui-col-lg6">
                                <div class="one-row">
                                    <div class="one-row-left">审计成果</div>
                                    <div class="row-table layui-form" style="display: flex;">
                                        <div class="one-checkbox">
                                            <label>风险预警单</label>
                                            <input type="checkbox" name="sjcg" value="warning" lay-filter="sjcg"
                                                   class="checkboxs">
                                        </div>

                                        <div class="one-checkbox">
                                            <label>触发项目</label>
                                            <input type="checkbox" name="sjcg" value="project" lay-filter="sjcg"
                                                   class="checkboxs">
                                        </div>

                                        <div class="one-checkbox">
                                            <label>审计通报</label>
                                            <input type="checkbox" name="sjcg" value="tongbao" lay-filter="sjcg"
                                                   class="checkboxs">
                                        </div>

                                        <div class="one-checkbox">
                                            <label>发现问题</label>
                                            <input type="checkbox" name="sjcg" value="wenti" lay-filter="sjcg"
                                                   class="checkboxs">
                                        </div>

                                        <div class="one-checkbox">
                                            <label>产生积分</label>
                                            <input type="checkbox" name="sjcg" value="generate" lay-filter="sjcg"
                                                   class="checkboxs">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>




                    </form>

                    </div>

                </div>


                <div class="last-btn">
                    <div class="last-btn-one" onclick="initAchieveList()" style="margin-bottom: 10px;" id="searchBtn">
                        <span class="icon iconfont icon-chaxun"></span>
                        <span>查询</span>
                    </div>
                    <div class="last-btn-one" onclick="reactFun()" style="margin-bottom: 10px;" id="reastBtn">
                        <span class="icon iconfont icon-lvzhou_zhongzhi"></span>
                        <span>重置</span>
                    </div>
                    <div class="last-btn-one" onclick="exportFengYanResultsList()" style="margin-bottom: 10px;display: inline-block">
                        <i class="iconfont search-icon">&#xe60c;</i>
                        <span>导出</span>
                    </div>

                </div>
            </div>
        </div>
        <div class="court-box">
            <div class="middleList" id="middleList">
                <table class="layui-table jq-even" id="middleListTable" lay-filter="middleList"></table>
            </div>
        </div>

        <div class="layui-layer-content-my" style="display: none;"><div class="layui-table-tips-main-my"><div class="text-left" id="lastRisk"></div><span class="icon iconfont icon-guanbishixin" onclick="closeRisk()"></span></div>
</body>
<!--#include virtual ="include/version.html"-->
<script type="text/javascript" src="resource/js/formSelects/formSelects-v4.js"></script>
<script src="resource/js/pagelog.js?v=6.5" type="text/javascript"></script>

<script id="lsit-1-tpl" type="text/html">
    {{# layui.each(d, function(index1, item1){ }}
    <div class="one-row-table" onclick="firstrowClick(this)">
        <span class="top-one-name" id="{{item1.idName}}">{{item1.typeName}}</span>
        <span class="top-title"></span>
        <span class="top-close" onclick="closeSecondrowClick(this,'{{item1.typeName}}')">×</span>
        <div class="open-popup">
            {{# layui.each(item1.list, function(index2, item2){ }}
            <div class="one-open-grid" onclick="secondrowClick(this,'{{item1.typeName}}')" data-value="{{item2.value}}">
                {{item2.lable}}
            </div>
            {{# }) }}
        </div>
    </div>
    {{# }) }}
</script>

<script id="lsit-2-tpl" type="text/html">
    <option value="">--请选择{{d.name}}--</option>
    {{# layui.each(d.data, function(index1, item1){ }}
    <option value="{{item1}}">{{item1}}</option>
    {{# }) }}
</script>
<!--<script id="select-tpl" type="text/html">-->
<!--    <option value="">&#45;&#45;请选择&#45;&#45;</option>-->
<!--    {{# layui.each(d, function(index, item){ }}-->
<!--    <option value="{{item.topicCode}}">{{item.topicName}}</option>-->
<!--    {{# }); }}-->
<!--</script>-->
<!--<script id="select-sub-tpl" type="text/html">-->
<!--    <option value="">&#45;&#45;请选择&#45;&#45;</option>-->
<!--    {{# layui.each(d, function(index, item){ }}-->
<!--    <option value="{{item.subTopicCode}}">{{item.subTopicName}}</option>-->
<!--    {{# }); }}-->
<!--</script>-->
<script id="select-group-tpl" type="text/html">
    <option value="">--请选择--</option>
    {{# layui.each(d, function(index, item){ }}
    <option value="{{item.ID}}">{{item.GROUP_NAME}}</option>
    {{# }); }}
</script>

<script id="select-dic-tpl" type="text/html">
    <option value="">--请选择--</option>
    {{# layui.each(d, function(index, item){ }}
    <option value="{{item.code}}">{{item.codeText}}</option>
    {{# }); }}
</script>

<script id="select-list-1" type="text/html">
    <option value="">--请选择--</option>
    {{# layui.each(d, function(index, item){ }}
    <option value="{{item.code}}" name="{{item.codeText}}">{{item.codeText}}</option>
    {{# }); }}
</script>
<script type="text/javascript">
    $("title").text("风研过程管理");
    if(loadCurUser().loginName && loadCurUserPost().provinceCode){
        console.log("new_monitor:风研过程管理")
        isTianyanCollectData(loadCurUser().loginName,loadCurUserPost().provinceCode);
    }
    var date = new Date();
    var startTime = date.getFullYear();
    var scoreStartTime = '';//产生积分开始时间 yyyy
    var scoreEndTime = '';//产生积分结束时间 yyyy
    if (getUrlParam('startTime')) {
        startTime = getUrlParam('startTime');
    }
    var endTime = date.getFullYear();
    if (getUrlParam('endTime')) {
        endTime = getUrlParam('endTime');
    }

    if (getUrlParam('scoreStartTime')) {
        scoreStartTime = getUrlParam('scoreStartTime');
    }
    if (getUrlParam('scoreEndTime')) {
        scoreEndTime = getUrlParam('scoreEndTime');
    }

    //涉及单位
    var involveProvince ='';
    if (getUrlParam('involveProvince')) {
        involveProvince = decodeURI(getUrlParam('involveProvince'));
    }
    //专题
    var topicName = '';
    if (getUrlParam('topicName')) {
        topicName = decodeURI(getUrlParam('topicName'));

    }
    //业务领域
    var fieldCode = '';
    if (getUrlParam('fieldCode')) {
        fieldCode = getUrlParam('fieldCode');
    }
    //团队主键
    var groupId = getUrlParam('groupId')?getUrlParam('groupId'):'';

    layui.use(['layer', 'form', 'jqfrm', 'laytpl', 'jqajax', 'laydate', 'laypage', 'table', 'jqform'],
        function () {
            var frm = layui.jqfrm;
            var $ = layui.jquery,
                tpl = layui.laytpl,
                ctx = top.global.ctx,
                form = layui.jqform,
                jqbind = layui.jqbind,
                laydate = layui.laydate,
                table = layui.table,
                laypage = layui.laypage;
            var limit = 10;
            var tableData = []
            var warning = false //预警
            var project = false //项目
            var tongbao = false //通报
            var wenti = false //问题
            var generateScore = 0  //是否产生积分，1是0否
            $('#topicName').val(topicName)
            $('#involveProvinceName').val(involveProvince)
            // 查询 条件展开更多
            $(function () {
                $('.btnMore')
                    .on(
                        'click',
                        function () {
                            var me = $(this), childDwon = me
                                .children('.slide-down'), childUp = me
                                .children('.slide-up');
                            if (childDwon.hasClass('none')) {
                                childDwon.removeClass('none');
                                childUp.addClass('none');
                                me.find('span').text("搜索更多");
                                $('.search-more').stop().hide();
                            } else {
                                childDwon.addClass('none');
                                childUp.removeClass('none');
                                me.find('span').text("收起更多");
                                $('.search-more').stop().show();
                            }
                        })

            });

            laydate.render({
                elem: '#payment', //指定元素
                type: 'year',
                range: true,
                format: 'yyyy',
                value: startTime?startTime + ' - ' + endTime:'',
                isInitValue: true,
                showBottom: true, //关闭底部框 去掉取消、确定、清空按钮
                max: '', //指定最大年份为当年
                done: function (value, date) {
                    $("#payment").val(value);
                    startTime = value.split(' - ')[0]
                    endTime = value.split(' - ')[1]
                }
            });

            laydate.render({
                elem: '#scoreTime', //指定元素
                type: 'year',
                range: true,
                format: 'yyyy',
                value: scoreStartTime?scoreStartTime + ' - ' + scoreEndTime:'',
                isInitValue: true,
                showBottom: true, //关闭底部框 去掉取消、确定、清空按钮
                max: '', //指定最大年份为当年
                done: function (value, date) {
                    $("#scoreTime").val(value);
                    scoreStartTime = value.split(' - ')[0]
                    scoreEndTime = value.split(' - ')[1]
                }
            });

            form.on('checkbox(sjcg)', function (data) {
                if (data.elem.checked) {
                    if (data.elem.value == 'warning') {
                        warning = true
                    }
                    if (data.elem.value == 'project') {
                        project = true
                    }
                    if (data.elem.value == 'tongbao') {
                        tongbao = true
                    }
                    if (data.elem.value == 'wenti') {
                        wenti = true
                    }
                    if (data.elem.value == 'generate') {
                        generateScore = 1
                    }

                } else {

                    if (data.elem.value == 'warning') {
                        warning = false
                    }
                    if (data.elem.value == 'project') {
                        project = false
                    }
                    if (data.elem.value == 'tongbao') {
                        tongbao = false
                    }
                    if (data.elem.value == 'wenti') {
                        wenti = false
                    }
                    if (data.elem.value == 'generate') {
                        generateScore = 0
                    }
                }
            })


            var formSelects = layui.formSelects;

            formSelects.on('involveProvince', function(id, vals, val, isAdd, isDisabled){
                //id:           点击select的id
                //vals:         当前select已选中的值
                //val:          当前select点击的值
                //isAdd:        当前操作选中or取消
                //isDisabled:   当前选项是否是disabled

                //如果return false, 那么将取消本次操作

                if(isAdd){
                    involveProvince.push(val.value)
                }else{
                    involveProvince.splice(involveProvince.indexOf(val.value),1)
                }


            });


            //类型
            $.ajax({
                url: top.global.ctx + "/dict/queryDictList",
                async: false,
                cache: false,
                type: 'POST',
                dataType: "json",
                data: {
                    type: 'DATA_BOARD_TOPIC_TYPE'
                },
                success: function (data) {
                    var getTpl = $('#select-dic-tpl').html();
                    tpl(getTpl).render(data.data, function (html) {
                        $("#topicType").html(html);
                    });
                    form.render();
                },
                error: function (data) {
                }
            });
            //对口处室
            $.ajax({
                url: top.global.ctx + "/bdata/leaderHomeDetail/selectModelUnitNames",
                async: false,
                cache: false,
                type: 'POST',
                dataType: "json",
                data: {
                    type: 'DATA_BOARD_TOPIC_TYPE'
                },
                success: function (data) {
                    var getTpl = $('#select-dic-tpl').html();
                    tpl(getTpl).render(data.data, function (html) {
                        $("#departmentCode").html(html);
                    });
                    form.render();
                },
                error: function (data) {
                }
            });





            $.ajax({
                url: top.global.ctx + "/dict/queryDictList",
                async: false,
                cache: false,
                type: 'POST',
                dataType: "json",
                data: {
                    type: 'DATA_BOARD_SPAN_TYPE'
                },
                success: function (data) {
                    var getTpl = $('#select-dic-tpl').html();
                    tpl(getTpl).render(data.data, function (html) {
                        $("#spanTypeName").html(html);
                    });
                    form.render('select', 'spanTypeName');
                },
                error: function (data) {
                }
            });
            function selectGroupData() {
                $.ajax({
                    url: ctx + "/bdata/remoteDataLeaderHome/selectGroupData",
                    dataType: "json",
                    type: "POST",
                    contentType: "application/json;charset=UTF-8",
                    success: function (ret) {
                        var getTpl = $("#select-group-tpl").html();
                        tpl(getTpl).render(ret.data, function (html) {
                            $("#groupId").html(html);
                        });
                        $("#groupId").val(groupId);
                        form.render('select', 'groupId');
                    },
                    error: function (e) {
                        console.info(e);
                    }
                });
            }
            selectGroupData();
            /**
             * 监听专题下拉列表
             */
            form.on('select(groupId)', function (data) {
                groupId = data.value;
            });

            //导出
            window.exportFengYanResultsList = function() {
                var url = ctx + "/bdata/remoteDataLeaderHome/exportFengYanResultsList?" +
                    "startTime="+startTime+
                    "&endTime="+endTime+
                    "&topicName="+encodeURI($("#topicName").val())+
                    "&subTopicName="+encodeURI($("#subTopicName").val())+
                    "&groupId="+$("#groupId").val()+
                    "&chargePersonName="+encodeURI($("#principalName").val())+
                    "&analystPersonName="+encodeURI($("#analystPersonName").val())+
                    "&spanTypeCode="+ $("#spanTypeName option:selected").val()+
                    "&departmentCode="+$("#departmentCode").val()+
                    "&involveProvinceName=" +$("#involveProvinceName").val()+
                    "&initialFocusRisk=" +encodeURI($("#initialFocusRisk").val())+
                    "&topicType=" +$("#topicType").val()+
                    "&warning="+warning+
                    "&project="+project+
                    "&tongbao="+tongbao+
                    "&wenti="+wenti+
                    "&generateScore="+generateScore+
                    "&scoreValue=" +$("#scoreValue").val()+
                    "&scoreStartTime="+scoreStartTime+
                    "&scoreEndTime="+scoreEndTime+
                    "&fieldCode="+fieldCode
                ;
                layer.msg("正在导出，请稍后。。。", {time: 3000});
                window.location.href = url;
                return false;
            }
            //应用/要素列表数据请求
            window.initAchieveList = function () {
                table.render({
                    elem: '#middleListTable',
                    id: 'middleListTable',
                    url: ctx + '/bdata/remoteDataLeaderHome/selectFengYanResultsList',
                    page: true,
                    even: true,
                    merge: [[0]],
                    height: 'full-110',
                    where: {
                        startTime: startTime,
                        endTime: endTime,
                        topicName: $("#topicName").val().trim(),
                        subTopicName: $("#subTopicName").val().trim(),
                        groupId: groupId,
                        chargePersonName: $("#principalName").val().trim().trim(),
                        analystPersonName: $("#analystPersonName").val().trim().trim(),
                        spanTypeCode: $("#spanTypeName option:selected").val(),
                        departmentCode: $("#departmentCode").val().trim(),
                        involveProvinceName: $('#involveProvinceName').val().trim(),
                        initialFocusRisk: $("#initialFocusRisk").val().trim(),
                        topicType: $("#topicType").val(),
                        warning: warning,//预警
                        project: project, //项目
                        tongbao: tongbao,//通报
                        wenti: wenti, //问题
                        generateScore: generateScore, //是否产生积分
                        scoreValue:  $("#scoreValue").val(), //积分值
                        scoreStartTime:scoreStartTime,
                        scoreEndTime:scoreEndTime,
                        fieldCode:fieldCode


                    },
                    cols: [
                        [
                        {
                            title: '序号',
                            align: 'center',
                            style: 'text-align: center',
                            width: '60',
                            field: 'number',
                            fixed: true,
                            rowspan: 2,
                            templet: function (d) {
                                var html = ''
                                if (d.LAY_TABLE_INDEX == 0) {
                                    html = '<div style="width:100%;text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">合计</div>'
                                } else {
                                    html = '<div  style="width:100%;text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.number + '</div>'
                                }
                                return html
                            }

                        },

                        {
                            title: '虚拟团队',
                            align: 'center',
                            style: 'text-align: center',
                            fixed: true,
                            width: '100',
                            field: 'groupName',
                            rowspan: 2,
                            templet: function (d) {
                                var html = ''
                                if (d.LAY_INDEX == 1) {
                                    html = '<div onclick=openNewTable(this,' + d.LAY_TABLE_INDEX +',"groupName") title="' + d.groupName + '" style="width:100%;text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">-</div>'
                                } else {
                                    html = '<div onclick=openNewTable(this,' + d.LAY_TABLE_INDEX +',"groupName") title="' + d.groupName + '" style="width:100%;text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.groupName + '</div>'
                                }
                                return html

                            }
                        },
                        {
                            title: '专题',
                            align: 'center',
                            style: 'text-align: center',
                            width: '70',
                            field: 'topicName',
                            rowspan: 2,
                            fixed: true,
                            templet: function (d) {
                                var html = ''
                                if (d.LAY_INDEX == 1) {
                                    html = '<div onclick=openNewTable(this,' + d.LAY_TABLE_INDEX +',"topicName") title="' + d.topicName + '" style="width:100%;text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.topicName + '</div>'
                                } else {
                                    html = '<div onclick=openNewTable(this,' + d.LAY_TABLE_INDEX +',"topicName") title="' + d.topicName + '" style="width:100%;text-align:left;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.topicName + '</div>'
                                }
                                return html
                            }
                        }, {
                            title: '子专题',
                            align: 'center',
                            style: 'text-align: center',
                            width: '70',
                            field: 'subTopicName',
                            rowspan: 2,
                            fixed: true,
                            templet: function (d) {
                                var html = ''
                                if (d.LAY_INDEX == 1) {
                                    html = '<div  onclick=openNewTable(this,' + d.LAY_TABLE_INDEX +',"subTopicName") title="' + d.subTopicName + '" style="width:100%;text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.subTopicName + '</div>'
                                } else {
                                    html = '<div  onclick=openNewTable(this,' + d.LAY_TABLE_INDEX +',"subTopicName") title="' + d.subTopicName + '" style="width:100%;text-align:left;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.subTopicName + '</div>'
                                }
                                return html
                            }
                        }, {
                            title: '星级',
                            align: 'center',
                            style: 'text-align: center',
                            width: '70',
                            field: 'stars',
                            rowspan: 2,
                            templet: function (d) {
                                var titles = ''
                                titles += '重要性：' + d.riskImportant
                                titles += '普遍性：' + d.riskUniversal
                                if (d.stars == '1') {
                                    return "<span class='stars' onclick=openNewTableRiskStars(this,'" +d.LAY_TABLE_INDEX +"') data-title1='" + d.riskImportant + "'  data-title2='" + d.riskUniversal + "'><span class='icon iconfont icon-xingxing1'></span> </span>"
                                } else if
                                (d.stars == '2') {
                                    return "<span class='stars' onclick=openNewTableRiskStars(this,'" +d.LAY_TABLE_INDEX +"') data-title1='" + d.riskImportant + "'  data-title2='" + d.riskUniversal + "'><span class='icon iconfont icon-xingxing1'></span><span class='icon iconfont icon-xingxing1'></span></span>"
                                } else
                                if (d.stars == '3') {

                                    return "<span class='stars' onclick=openNewTableRiskStars(this,'" +d.LAY_TABLE_INDEX +"') data-title1='" + d.riskImportant + "'  data-title2='" + d.riskUniversal + "'><span class='icon iconfont icon-xingxing1'></span><span class='icon iconfont icon-xingxing1'></span><span class='icon iconfont icon-xingxing1'></span></span>"
                                } else {
                                    return "<span class='stars' onclick=openNewTableRiskStars(this,'" +d.LAY_TABLE_INDEX +"') data-title1='" + d.riskImportant + "'  data-title2='" + d.riskUniversal + "' >-</span>"
                                }
                            }
                        },  {
                            title: '聚焦风险',
                            align: 'center',
                            style: 'text-align: left',
                            width: '290',
                            field: 'initialFocusRiskText',
                            rowspan: 2,
                            templet: function (d) {
                                if (d.LAY_TABLE_INDEX == 0) {
                                    return '<div>-</div>'
                                } else {
                                    return '<div class="overflowhidden-2"  style="text-align: left;" onclick=initialFocusRisk(this,' + d.LAY_TABLE_INDEX +',"initialFocusRisk")>' + d.initialFocusRiskText + '</div>'
                                }
                            }
                        }, {
                            title: '风险<br/>预警单',
                            align: 'center',
                            style: 'text-align: center',
                            field: 'risk',
                            width: '70',
                            rowspan: 2,
                            templet: function (d) {
                                return '<div onclick=openYjdPreView("' + d.number + '","' + d.risk + '","' + d.id + '","' + d.subTopicCode + '","' + d.topicCode + '")  title="' + d.risk + '" style="color:#c20000;  cursor: pointer; width:100%;text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.risk + '</div>'
                            }
                        }, {
                            title: '触发<br/>项目',
                            align: 'center',
                            style: 'text-align: center',
                            width: '70',
                            field: 'xiangmu',
                            rowspan: 2,
                            templet: function (d) {
                                return '<div onclick=openCfxm("' + d.number + '","' + d.id + '","' + d.subTopicCode + '","'+ d.topicCode + '") title="' + d.xiangmu + '" style="cursor: pointer;color:#c20000; width:100%;text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.xiangmu + '</div>'


                            }
                        }, {
                            title: '审计成果',
                            align: 'center',
                            style: 'text-align: center',
                            width: '10%',
                            colspan: 2
                        }, {
                            title: '积分值',
                            align: 'center',
                            style: 'text-align: center',
                            width: '80',
                            field: 'score',
                            rowspan: 2,
                            templet: function (d) {
                                if(d.researchIds){
                                    return '<div onclick=openScore("","research") title="' + d.projectList + '" style="cursor: pointer;color:#c20000; width:100%;text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.score + '</div>'
                                }else{
                                    return '<div onclick=openScore("' + d.id +  '","research") title="' + d.projectList + '" style="cursor: pointer;color:#c20000; width:100%;text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.score + '</div>'
                                }
                            }
                        }, {
                            title: '来源',
                            align: 'center',
                            style: 'text-align: left',
                            width: '140',
                            templet: function (d) {
                                if (d.LAY_INDEX == 1) {
                                    return '<div style="width:100%;text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">-</div>'
                                } else {
                                    if (d.spanType) {
                                        return '<div title="' + d.remoteYear + '年' + d.spanTypeName + '" style="text-align:left;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.remoteYear + '年' + d.spanTypeName + '</div>'
                                    } else {
                                        return '';
                                    }
                                }

                            },
                            rowspan: 2
                        },  {
                            title: '涉及单位',
                            align: 'center',
                            style: 'text-align: center',
                            width: '100',
                            field: 'involveProvinceName',
                            rowspan: 2,
                            templet: function (d) {
                                if (d.LAY_INDEX == 1) {
                                    return '<div style="text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">-</div>'
                                } else {
                                    return '<div onclick=openNewTable(this,' + d.LAY_TABLE_INDEX +',"involveProvinceName") title="' + d.involveProvinceName + '" style="text-align:left;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.involveProvinceName + '</div>'
                                }

                            }
                        }, {
                            title: '类型',
                            align: 'center',
                            style: 'text-align: center',
                            width: '110',
                            field: 'topicTypeName',
                            rowspan: 2,
                            templet: function (d) {
                                if (d.LAY_INDEX == 1) {
                                    return '<div style="text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">-</div>'
                                } else {
                                    return '<div onclick=openNewTable(this,' + d.LAY_TABLE_INDEX +',"topicTypeName") title="' + d.topicTypeName + '" style="text-align:left;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.topicTypeName + '</div>'
                                }

                            }

                        },  {
                            title: '测试用例',
                            align: 'center',
                            style: 'text-align: center',
                            width: '85',
                            field: 'cases',
                            rowspan: 2,
                            templet: function (d) {

                                if (d.LAY_INDEX == 1) {
                                    return '<div style="text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">-</div>'
                                } else {
                                    return (
                                        '<div style="cursor: pointer;width:100%;text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;"><span style="color:#c20000" onclick=openMyOpu("' +
                                        d.id +
                                        '")  class="icon iconfont icon-chakan pointer color-red" title="查看"></span></div>'
                                    );
                                }

                            }
                        },{
                            title: '新打通数据',
                            align: 'center',
                            style: 'text-align: center',
                            width: '100',
                            field: 'openNewDataContent',
                            rowspan: 2,
                            templet: function (d) {

                                if (d.LAY_INDEX == 1) {
                                    return '<div style="text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">-</div>'
                                } else {
                                    return '<div onclick=openNewTable(this,' + d.LAY_TABLE_INDEX +',"openNewDataContent") title="' + d.openNewDataContent + '" style="text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.openNewDataContent + '</div>'
                                }

                            }
                        }, {
                            title: '负责人',
                            align: 'center',
                            style: 'text-align: center',
                            width: '90',
                            field: 'chargePersonName',
                            rowspan: 2,
                            templet: function (d) {

                                if (d.LAY_INDEX == 1) {
                                    return '<div style="text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">-</div>'
                                } else {
                                    return '<div title="' + d.chargePersonName + '" style="width:100%;text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.chargePersonName + '</div>'
                                }

                            }
                        }, {
                            title: '分析人',
                            align: 'center',
                            style: 'text-align: center',
                            width: '120',
                            field: 'analystPersonName',
                            rowspan: 2,
                            templet: function (d) {

                                if (d.LAY_INDEX == 1) {
                                    return '<div style="text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">-</div>'
                                } else {
                                    return '<div onclick=openNewTable(this,' + d.LAY_TABLE_INDEX +',"analystPersonName") title="' + d.analystPersonName + '" style="width:100%;text-align:left;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.analystPersonName + '</div>'
                                }

                            }
                        }, {
                            title: '对口处室',
                            align: 'center',
                            style: 'text-align: center',
                            width: '100',
                            field: 'counterpartDepartment',
                            rowspan: 2,
                            templet: function (d) {


                                if (d.LAY_INDEX == 1) {
                                    return '<div style="text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">-</div>'
                                } else {
                                    return '<div  onclick=openNewTable(this,' + d.LAY_TABLE_INDEX +',"counterpartDepartment") title="' + d.counterpartDepartment + '" style="text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.counterpartDepartment + '</div>'
                                }

                            }
                        },{
                            title: '业务领域',
                            align: 'center',
                            style: 'text-align: center',
                            width: '100',
                            field: 'fieldName',
                            rowspan: 2,
                            templet: function (d) {
                                var html = ''
                                if (d.LAY_INDEX == 1) {
                                    html = '<div onclick=openNewTable(this,' + d.LAY_TABLE_INDEX +',"fieldName") title="' + d.fieldName + '" style="width:100%;text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.fieldName + '</div>'
                                } else {
                                    html = '<div onclick=openNewTable(this,' + d.LAY_TABLE_INDEX +',"fieldName") title="' + d.fieldName + '" style="width:100%;text-align:left;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.fieldName + '</div>'
                                }
                                return html
                            }
                        },{
                            title: '提交日期',
                            align: 'center',
                            style: 'text-align: center',
                            width: '100',
                            field: 'commitDate',
                            rowspan: 2,
                            templet: function (d) {
                                if (d.LAY_INDEX == 1) {
                                    return '<div style="text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">-</div>'
                                } else {
                                    return '<div title="' + d.commitDate + '" style="text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.commitDate + '</div>'
                                }

                            }
                        },
                            {
                                title: '核查单位',
                                align: 'center',
                                style: 'text-align: left',
                                width: '100',
                                field: 'checkProv',
                                rowspan: 2,
                                templet: function (d) {
                                    if (d.checkProv) {
                                        var provNames = [];
                                        for (var i = 0; i < d.checkProv.length; i++) {
                                            // 检查 status 是否为 1
                                            var color = d.checkProv[i].status == "1" ? "text-red" : "";
                                            provNames.push(`<span class="${color}">${d.checkProv[i].provName}</span>`);
                                        }
                                        return '<div onclick=openNewTable(this,' + d.LAY_TABLE_INDEX +',"checkProv") class="overflowhidden-2">' + provNames.join('、') + '</div>';
                                    } else {
                                        return '<div style="text-align:center;">-</div>';
                                    }
                                }
                            },
                            {
                                title: '推送单位',
                                align: 'center',
                                style: 'text-align: left',
                                width: '100',
                                field: 'pushProv',
                                rowspan: 2,
                                templet: function (d) {
                                    if (d.pushProv) {
                                        var provNames = [];
                                        for (var i = 0; i < d.pushProv.length; i++) {
                                            // 检查 status 是否为 1
                                            var color = d.pushProv[i].status == "1" ? "text-red" : "";
                                            provNames.push(`<span class="${color}">${d.pushProv[i].provName}</span>`);
                                        }
                                        return '<div onclick=openNewTable(this,' + d.LAY_TABLE_INDEX +',"pushProv") class="overflowhidden-2">' + provNames.join('、') + '</div>';
                                    } else {
                                        return '<div style="text-align:center;">-</div>';
                                    }
                                }
                            },
                            {
                                field: "operate",
                                title: "操作",
                                fixed: 'right',
                                align: "center",
                                width: "70",
                                templet: function (d) {
                                        let text = "";
                                        text =
                                            '<div class="table-flex-j-s" style="text-align: center;width: 100%">'
                                    if (d.id) {
                                        text +=  '<span class="icon iconfont text-red cursor table-btn" title="查看" onclick=detailLeaderManager("' + d.id + '","' + d.buttonType + '")>&#xe651;</span>'
                                    }
                                        text += '</div>';
                                        return text;

                                },
                            }], [{
                            title: '审计通报',
                            align: 'center',
                            style: 'text-align: center',
                            width: '85',
                            field: 'tongbao',
                            templet: function (d) {
                                return '<div  onclick=openSjtbTable("' + d.number + '","' + d.tongbao + '","' + d.id + '","' + d.subTopicCode + '","' + d.topicCode + '") title="' + d.tongbao + '" style=" cursor: pointer; color:#c20000; width:100%; text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.tongbao + '</div>'
                            }
                        }, {
                            title: '发现问题',
                            align: 'center',
                            style: 'text-align: center',
                            width: '85',
                            field: 'wenti',
                            templet: function (d) {
                                return '<div onclick=openFxwtTable("' + d.number + '","' + d.wenti + '","' + d.id + '","' + d.subTopicCode + '","' + d.topicCode + '")  title="' + d.wenti + '" style=" cursor: pointer; color:#c20000;  width:100%; text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.wenti + '</div>'
                            }
                        }]
                    ],
                    done: function (res) {
                        tableData = res.data
                        var data = res.data
                        for (var i = 0; i < data.length; i++) {
                                if (data[i].initialFocusRiskProvList.length > 0) {
                                    var doms = $('.layui-table-main>.layui-table>tbody>tr').eq(i).find('td').find('.layui-table-cell')
                                    for (var j = 0; j < 1; j++) {
                                        $(doms[j]).css('height', 48 + 'px')
                                    }

                                    var doms = $('.layui-table-fixed>.layui-table-body>.layui-table>tbody>tr').eq(i).find('td').find('.layui-table-cell')
                                    for (var j = 0; j < 1; j++) {
                                        $(doms[j]).css('height', 48 + 'px')
                                    }

                                    var domss = $('.layui-table-fixed-r>.layui-table-body>.layui-table>tbody>tr').eq(i).find('td').find('.layui-table-cell')
                                    for (var j = 0; j < 1; j++) {
                                        $(domss[j]).css('height', 48 + 'px')
                                    }
                                }else{
                                    var doms = $('.layui-table-main>.layui-table>tbody>tr').eq(i).find('td').find('.layui-table-cell')
                                    $(doms).css('height', '48px')
                                }

                        }
                        for (var i = 0; i < data.length; i++) {
                            if(data[i].number==res.count){
                                $('.layui-table-fixed-r .layui-table-body').css('height', $('.layui-table-body.layui-table-main').height() + 'px')
                                $('.layui-table-fixed .layui-table-body').css('height', $('.layui-table-body.layui-table-main').height() + 'px')
                            }
                        }
                        form.render()
                    },

                });
            }
            window.editAchievements = function(id,num,name){
               var indexTop = layer.confirm(
                    "确认对序号为【"+num+"】的风研成果调度其负责人为【"+name+"】录入阶段性成果吗？",
                    { icon: 3, title: "确认" },
                    function (index) {
                        layer.close(index);
                        var indexZG = top.layer.load(3, {
                            //遮盖层
                            shade: [0.4, "#fff"], //0.4透明度的白色背景
                        });
                        $.ajax({
                            url: ctx + "/bdata/chooseRiskResearch/startProcess",
                            type: "POST",
                            dataType: "JSON",
                            contentType: "application/json;charset=UTF-8",
                            data: JSON.stringify({
                                id:id
                            }),
                            success: function (data) {
                                if (data.httpCode == "200") {
                                    frm.success("发起成功");
                                    top.layer.close(indexZG);
                                    layer.close(indexTop);
                                } else {
                                    frm.error(data.msg);
                                }
                            },
                        });
                    }
                );
            }
             //详情
            window.detailLeaderManager = function (id,buttonType) {
                var indexs = top.layer.open({
                    title: '详情',
                    content:
                        "views/audit/bigdata/remoteDataKanban/riskResearchInput/wind-research-achievements-detail-new.html" +
                        "?id=" + id+'&buttonType='+buttonType,
                    type: 2,
                    area: ["80%", "80%"],
                    fixed: true,
                    maxmin: false,
                    resize: false,
                    yes: function (index, layero) {

                    },
                    btn2: function (index, layero) {

                    },
                    success: function (layero, index) {
                    },
                });
            }

            window.reactFun = function () {
                $('.one-row-table').css('border-color', '#D9D9D9')
                $('.one-open-grid').removeClass('one-open-grid-checked')
                $('.top-title').text('')
                $('.top-title').hide()
                $('.top-close').hide()
                $('.top-one-name').attr('data-value', '')

                startTime = date.getFullYear();
                if (getUrlParam('startTime')) {
                    startTime = getUrlParam('startTime');
                }
                endTime = date.getFullYear();
                if (getUrlParam('endTime')) {
                    endTime = getUrlParam('endTime');
                }
                $("#payment").val(startTime + ' - ' + endTime);
                $("#groupId").val("");
                $("#topicName").val("");
                $("#analystPersonName").val("");
                $("#subTopicName").val("");
                var getTpl = $("#select-sub-tpl").html();


                $('#involveProvinceName').val('');
                $("#principalName").val("");
                $("#spanTypeName").val("");

                $("#topicType").val('');
                $("#departmentCode").val('');

                $('#initialFocusRisk').val('')
                $(".checkboxs").each(function () { $(this).prop('checked', false); });

                //$('#involveProvinceName').val('')
                involveProvince = []
                formSelects.data('involveProvince', 'local', { arr: [] });


                warning = false //预警
                project = false //项目
                tongbao = false //通报
                wenti = false //问题
                generateScore = 0 //是否产生积分

                $("#scoreValue").val('');
                $("#scoreTime").val('');
                $("#fieldCode").val('');
                scoreStartTime = '';
                scoreEndTime = '';
                fieldCode = ''
                groupId = ''
                topicName = ''
                form.render()
                initAchieveList()
            }


            window.openMyOpu = function (id) {
                var msg = "";
                // 根据\n将返回文字换行
                if (tableData.length > 0) {
                    for (var i = 0; i < tableData.length; i++) {
                        if (id == tableData[i].id) {
                            msg = tableData[i].cases.replace(/\n/g, "<br/>");
                        }
                    }
                }
                var html = '<div style="line-height:35px">';
                str = msg
                html +=
                    '<div class="mag-header-titles" style="font-size:16px;overflow-y: auto;">' +
                    str +
                    "</div>";

                html += "</div>";
                top.layer.alert(html, { area: ["550px", "500px"] }, function (idx) {
                    layer.close(idx);
                    top.layer.close(idx);
                    layer.closeAll();
                });
            };

            window.openSearchC = function () {
                $('#searchbox2').hide()
                $('#searchbox3').hide()
                $('#closeBtn').hide()
                $('#openBtn').show()
                // $('#searchBtn').hide()
                $('#reastBtn').hide()
            }

            window.openSearchO = function () {
                $('#searchbox2').show()
                $('#searchbox3').show()
                $('#closeBtn').show()
                $('#openBtn').hide()
                // $('#searchBtn').show()
                $('#reastBtn').show()
            }


            //触发项目
            window.openCfxm = function (index, id, subTopicCode,topicCode) {
                if (index == 0) {
                    top.layer.open({
                        type: 2,
                        title: '触发项目',
                        content: 'views/audit/colligate/morder/baseinfo/cfxm-table.html?startTime=' + startTime+'&scoreStartTime='+scoreStartTime+ '&endTime=' + endTime   + '&topicCode=' + topicCode + '&subTopicCode=' + subTopicCode + '&groupId=' +  $("#groupId option:selected").val() + '&chargePersonName=' + $("#principalName").val() + '&spanTypeCode=' + $("#spanTypeName option:selected").val() + '&departmentCode=' + $("#departmentCode").val() + '&involveProvinceName=' + $('#involveProvinceName').val() + '&initialFocusRisk=' +  $("#initialFocusRisk").val() + '&topicType=' + $("#topicType").val() + '&warning=' + warning + '&project=' + project + '&tongbao=' + tongbao + '&wenti=' + wenti + '&types=2'
                        +'&topicName='+ $("#topicName").val()+'&subTopicName='+ $("#subTopicName").val(),
                        area: ['90%', '80%'],
                        success: function (layero, index) {
                        },
                        end: function () {
                        }
                    });
                } else if (index > 0) {
                    top.layer.open({
                        type: 2,
                        title: '触发项目',
                        content: 'views/audit/colligate/morder/baseinfo/cfxm-table.html?id=' + id +'&startTime='+ startTime+'&scoreStartTime='+scoreStartTime+ '&endTime=' + endTime+ '&types=2',
                        area: ['90%', '80%'],
                        success: function (layero, index) {
                        },
                        end: function () {
                        }
                    });
                } else {
                    top.layer.open({
                        type: 2,
                        title: '触发项目',
                        content: 'views/audit/colligate/morder/baseinfo/cfxm-table.html?startTime=' + startTime +'&scoreStartTime='+scoreStartTime+ '&endTime=' + endTime + '&types=1',
                        area: ['90%', '80%'],
                        success: function (layero, index) {
                        },
                        end: function () {
                        }
                    });
                }

            }

            //风研积分攥取
            window.openScore = function(researchIds,scoreType){
                var paramsData = JSON.stringify({
                    // startTime:startTime, 积分攥取不查询积分来源的时间范围 20250422
                    // endTime:endTime,
                    topicName: topicName,
                    subTopicName: $("#subTopicName").val(),
                    groupId: groupId,
                    chargePersonName: $("#principalName").val(),
                    spanTypeCode: $("#spanTypeName option:selected").val(),
                    departmentCode: $("#departmentCode").val(),
                    involveProvinceName: $('#involveProvinceName').val(),
                    initialFocusRisk: $("#initialFocusRisk").val(),
                    topicType: $("#topicType").val(),
                    warning: warning,//预警
                    project: project, //项目
                    tongbao: tongbao,//通报
                    wenti: wenti, //问题
                    generateScore: generateScore, //是否产生积分
                    scoreValue:  $("#scoreValue").val(), //积分值
                    scoreStartTime:scoreStartTime,
                    scoreEndTime:scoreEndTime,
                    fieldCode:fieldCode
                })
                var title = '风研积分列表';
                var target =
                    '/views/audit/bigdata/remoteDataKanban/integralDetail.html?true&startTime=' +
                    scoreStartTime + "&endTime=" + scoreEndTime
                    +"&researchIds="+researchIds
                    +"&paramsData="+encodeURI(paramsData)
                    +"&scoreType="+scoreType;
                $("#tableMenu").data("title", title).data("url", target).click();
            }
            //通报问题

            window.openSjtb = function () {
                top.layer.open({
                    type: 2,
                    title: '审计通报',
                    content: 'views/audit/colligate/morder/baseinfo/sjtb-table.html?startTime=' + startTime +'&scoreStartTime='+scoreStartTime + '&endTime=' + endTime + '&types=1',
                    area: ['90%', '80%'],
                    success: function (layero, index) {
                    },
                    end: function () {
                    }
                });
            }

            //查看聚焦风险
            window.initialFocusRisk = function (obj,index,indexobj) {
                $('.layui-layer-content-my').show()
                $('#lastRisk').empty()
                var titles = tableData[index].initialFocusRisk
                $('#lastRisk').append(titles)
                $('.layui-layer-content-my').css('left',$(obj).offset().left).css('top',$(obj).offset().top)

            }

            window.openSjtbTable = function (index, num, id, subTopicCode,topicCode) {
                if (index == 0) {
                    // if (num > 1) {
                        top.layer.open({
                            type: 2,
                            title: '审计通报',
                            content: 'views/audit/colligate/morder/baseinfo/sjtb-table.html?startTime=' +  startTime+'&scoreStartTime='+scoreStartTime + '&endTime=' + endTime + '&topicCode=' + topicCode + '&subTopicCode=' + subTopicCode + '&groupId=' +  groupId + '&chargePersonName=' + $("#principalName").val() + '&spanTypeCode=' + $("#spanTypeName option:selected").val() + '&departmentCode=' + $("#departmentCode").val() + '&involveProvinceName=' + $('#involveProvinceName').val() + '&initialFocusRisk=' +  $("#initialFocusRisk").val() + '&topicType=' + $("#topicType").val() + '&warning=' + warning + '&project=' + project + '&tongbao=' + tongbao + '&wenti=' + wenti + '&types=2'
                                +'&topicName='+ $("#topicName").val()+'&subTopicName='+ $("#subTopicName").val(),
                            area: ['90%', '80%'],
                            success: function (layero, index) {
                            },
                            end: function () {
                            }
                        });
                    // }
                    /*if (num == 1) {
                        $.ajax({
                            type: 'post',
                            url: ctx + '/bdata/leaderHomeDetail/queryTongBaoDetail',
                            dataType: "json",
                            data: JSON.stringify({
                                startTime: startTime,
                                endTime: endTime,
                                topicCode: topicCode,
                                subTopicCode: subTopicCode,
                                groupId: groupId,
                                chargePersonName: $("#principalName").val(),
                                spanTypeCode: $("#spanTypeName option:selected").val(),
                                departmentCode: $("#departmentCode").val(),
                                involveProvinceName: $('#involveProvinceName').val(),
                                initialFocusRisk: $("#initialFocusRisk").val(),
                                topicType: $("#topicType").val(),
                                warning: warning,//预警
                                project: project, //项目
                                tongbao: tongbao,//通报
                                wenti: wenti, //问题
                                type: 2
                            }),
                            contentType: "application/json;charset=UTF-8",
                            success: function (ret) {
                                if (ret.httpCode === 200) {
                                    var data = ret.data;
                                    openPreViewName(data[0].ATTACHMENT_ID, htmlEncodeByRegExp(data[0].ATTACHMENT_NAME))
                                }
                            }
                        });
                    }*/
                }
                if (index > 0) {
                    // if (num > 1) {
                        top.layer.open({
                            type: 2,
                            title: '审计通报',
                            content: 'views/audit/colligate/morder/baseinfo/sjtb-table.html?id=' +  id  +'&startTime=' +  startTime+'&scoreStartTime='+scoreStartTime + '&endTime=' + endTime + '&types=2',
                            area: ['90%', '80%'],
                            success: function (layero, index) {
                            },
                            end: function () {
                            }
                        });
                    // }
                    /*if (num == 1) {
                        $.ajax({
                            type: 'post',
                            url: ctx + '/bdata/leaderHomeDetail/queryTongBaoDetail',
                            dataType: "json",
                            data: JSON.stringify({
                                id:id,
                                type: 2
                            }),
                            contentType: "application/json;charset=UTF-8",
                            success: function (ret) {
                                if (ret.httpCode === 200) {
                                    var data = ret.data;
                                    openPreViewName(data[0].ATTACHMENT_ID, htmlEncodeByRegExp(data[0].ATTACHMENT_NAME))
                                }
                            }
                        });
                    }*/
                }

            }



            window.openFxwtTable = function (index, num, id, subTopicCode,topicCode) {
                if (index == 0) {
                    if (num > 1) {
                        top.layer.open({
                            type: 2,
                            title: '发现问题',
                            content: 'views/audit/colligate/morder/baseinfo/fxwt-table.html?startTime=' +  startTime +'&scoreStartTime='+scoreStartTime+ '&endTime=' + endTime + '&topicCode=' + topicCode + '&subTopicCode=' + subTopicCode + '&groupId=' +  groupId + '&chargePersonName=' + $("#principalName").val() + '&spanTypeCode=' + $("#spanTypeName option:selected").val() + '&departmentCode=' + $("#departmentCode").val() + '&involveProvinceCode=' + formSelects.value('involveProvince', 'val') + '&initialFocusRisk=' +  $("#initialFocusRisk").val() + '&topicType=' + $("#topicType").val() + '&warning=' + warning + '&project=' + project + '&tongbao=' + tongbao + '&wenti=' + wenti + '&types=2'
                                +'&topicName='+ $("#topicName").val()+'&subTopicName='+ $("#subTopicName").val(),
                            area: ['90%', '80%'],
                            success: function (layero, index) {
                            },
                            end: function () {
                            }
                        });
                    }
                    if (num == 1) {
                        $.ajax({
                            type: 'post',
                            url: ctx + '/bdata/leaderHomeDetail/queryWenTiDetail',
                            dataType: "json",
                            data: JSON.stringify({
                                startTime: startTime,
                                scoreStartTime:scoreStartTime,
                        endTime: endTime,
                        topicCode:topicCode,
                        subTopicCode: subTopicCode,
                        groupId: groupId,
                        chargePersonName: $("#principalName").val(),
                        spanTypeCode: $("#spanTypeName option:selected").val(),
                        departmentCode: $("#departmentCode").val(),
                        involveProvinceName: $('#involveProvinceName').val(),
                        initialFocusRisk: $("#initialFocusRisk").val(),
                        topicType: $("#topicType").val(),
                        warning: warning,//预警
                        project: project, //项目
                        tongbao: tongbao,//通报
                        wenti: wenti, //问题
                                type: 2
                            }),
                            contentType: "application/json;charset=UTF-8",
                            success: function (ret) {
                                if (ret.httpCode === 200) {
                                    var data = ret.data;
                                    openFxwtPreView(data[0].ACHIEVEMENT_ID)
                                }
                            }
                        });
                    }
                }

                if (index > 0) {
                    if (num > 1) {
                        top.layer.open({
                            type: 2,
                            title: '发现问题',
                            content: 'views/audit/colligate/morder/baseinfo/fxwt-table.html?id=' + id + '&startTime=' + startTime +"&scoreStartTime=" +scoreStartTime +  '&endTime=' + endTime + '&types=2',
                            area: ['90%', '80%'],
                            success: function (layero, index) {
                            },
                            end: function () {
                            }
                        });
                    }
                    if (num == 1) {
                        $.ajax({
                            type: 'post',
                            url: ctx + '/bdata/leaderHomeDetail/queryWenTiDetail',
                            dataType: "json",
                            data: JSON.stringify({
                                startTime: startTime,
                                scoreStartTime: scoreStartTime,
                                endTime: endTime,
                                id:id,
                                type: 2
                            }),
                            contentType: "application/json;charset=UTF-8",
                            success: function (ret) {
                                if (ret.httpCode === 200) {
                                    var data = ret.data;
                                    openFxwtPreView(data[0].ACHIEVEMENT_ID)
                                }
                            }
                        });
                    }
                }

            }

            window.openFxwtPreView = function (problemId) {
                var flag = '2'
                top.layer.open({
                    type: 2,
                    title: '问题详情',
                    content: "views/audit/pro/reform/reformreadrecord/reformTodoTaskBaseQuery.html?problemId=" + problemId + "&flag=" + flag,
                    area: ['90%', '80%'],
                    success: function (layero, index) {
                    },
                    end: function () {
                    }
                });
            }


            //预警单
            window.openYjdPreView = function (index, num, id, subTopicCode,topicCode) {
                var sendData = ''
                if (index == 0) {
                    sendData = JSON.stringify({
                        startTime: startTime,
                        endTime: endTime,
                        scoreStartTime:scoreStartTime,
                        topicCode: topicCode,
                        subTopicCode: subTopicCode,
                        groupId: groupId,
                        chargePersonName: $("#principalName").val(),
                        spanTypeCode: $("#spanTypeName option:selected").val(),
                        departmentCode: $("#departmentCode").val(),
                        involveProvinceName: $('#involveProvinceName').val(),
                        initialFocusRisk: $("#initialFocusRisk").val(),
                        topicType: $("#topicType").val(),
                        warning: warning,//预警
                        project: project, //项目
                        tongbao: tongbao,//通报
                        wenti: wenti, //问题
                        topicName:$("#topicName").val(),
                        subTopicName:$("#subTopicName").val()
                    })


                    if (num == 1) {
                        $.ajax({
                            type: 'post',
                            url:  ctx + '/bdata/leaderHomeDetail/selectRiskData',
                            dataType: "json",
                            data:sendData,
                            contentType: "application/json;charset=UTF-8",
                            success: function (ret) {
                                if (ret.httpCode === 200) {
                                    var data = ret.data;
                                    opennYjdPreView(data[0].ID)

                                }
                            }
                        });
                    }
                    if (num > 1) {
                        top.layer.open({
                            type: 2,
                            title: '风险预警单',
                            content: 'views/audit/colligate/morder/baseinfo/selectRiskData.html?startTime=' + startTime +'&scoreStartTime='+scoreStartTime+ '&endTime=' + endTime + '&topicCode=' + topicCode + '&subTopicCode=' + subTopicCode + '&groupId=' +  groupId + '&chargePersonName=' + $("#principalName").val() + '&spanTypeCode=' + $("#spanTypeName option:selected").val() + '&departmentCode=' + $("#departmentCode").val() + '&involveProvinceName=' + encodeURI($('#involveProvinceName').val()) + '&initialFocusRisk=' +  $("#initialFocusRisk").val() + '&topicType=' + $("#topicType").val() + '&warning=' + warning + '&project=' + project + '&tongbao=' + tongbao + '&wenti=' + wenti
                                +'&topicName='+ $("#topicName").val()+'&subTopicName='+ $("#subTopicName").val(),
                            area: ['90%', '80%'],
                            success: function (layero, index) {
                            },
                            end: function () {
                            }
                        });
                    }
                }
                if (index > 0) {
                    sendData = JSON.stringify({
                        id: id,
                        startTime: startTime,
                        endTime: endTime,
                        scoreStartTime:scoreStartTime,
                    })

                    if (num == 1) {
                        $.ajax({
                            type: 'post',
                            url:  ctx + '/bdata/leaderHomeDetail/selectRiskData',
                            dataType: "json",
                            data:sendData,
                            contentType: "application/json;charset=UTF-8",
                            success: function (ret) {
                                if (ret.httpCode === 200) {
                                    var data = ret.data;
                                    opennYjdPreView(data[0].ID)

                                }
                            }
                        });
                    }
                    if (num > 1) {
                        top.layer.open({
                            type: 2,
                            title: '风险预警单',
                            content: 'views/audit/colligate/morder/baseinfo/selectRiskData.html?id=' + id+'&startTime='+ startTime +"&scoreStartTime=" +scoreStartTime + '&endTime=' + endTime  ,
                            area: ['90%', '80%'],
                            success: function (layero, index) {
                            },
                            end: function () {
                            }
                        });
                    }
                }



            }

            //展示全部数据
            window.openNewTable = function(obj,index,indexobj){
                $('.layui-layer-content-my').show()
                $('#lastRisk').empty()
                var html = ''

                if(tableData[index][indexobj]){
                    if(indexobj=='checkProv'){
                        var provNames = [];
                        for (var i = 0; i < tableData[index][indexobj].length; i++) {
                            // 检查 status 是否为 1
                            var color = tableData[index][indexobj][i].status == "1" ? "text-red" : "";
                            provNames.push(`<span class="${color}">${tableData[index][indexobj][i].provName}</span>`);
                        }
                        html =  '<div>' + provNames.join('、') + '</div>';
                    }else if(indexobj=='pushProv'){
                        var provNames = [];
                        for (var i = 0; i < tableData[index][indexobj].length; i++) {
                            // 检查 status 是否为 1
                            var color = tableData[index][indexobj][i].status == "1" ? "text-red" : "";
                            provNames.push(`<span class="${color}">${tableData[index][indexobj][i].provName}</span>`);
                        }
                        html =  '<div>' + provNames.join('、') + '</div>';

                    }else{
                        html =  tableData[index][indexobj]
                    }
                }
                console.log(html)
                $('#lastRisk').append(html)
                console.log($(obj).offset())


                $('.layui-layer-content-my').css('left',$(obj).offset().left).css('top',$(obj).offset().top)
            }


            window.opennYjdPreView = function (researchInfoId) {
                top.layer.open({
                    type: 2,
                    title: '风险预警单详情',
                    content: "views/audit/bigdata/remoteDataKanban/apply/detailWarningForm.html?researchInfoId=" + researchInfoId,
                    area: ['90%', '80%'],
                    success: function (layero, index) {
                    },
                    end: function () {
                    }
                });
            }

            window.openNewTableRisk = function(obj,index,indexobj){
              $('.layui-layer-content-my').show()
              $('#lastRisk').empty()
              var html = ''

              if(tableData[index].initialFocusRiskProvList.length>0){
                  html =  tableData[index].initialFocusRiskProvList[indexobj].INITIAL_FOCUS_RISK
                // tableData[index].initialFocusRiskList.forEach(function(item){
                //     html += '<span>' + item + '</span></br>'
                // })
              }
              console.log(html)
              $('#lastRisk').append(html)
              console.log($(obj).offset())


              $('.layui-layer-content-my').css('left',$(obj).offset().left).css('top',$(obj).offset().top)
            }

            // 星级展示
            window.openNewTableRiskStars = function(obj,index){
                $('.layui-layer-content-my').show()
                $('#lastRisk').empty()
                var riskImportant =  tableData[index].riskImportant
                var riskUniversal =  tableData[index].riskUniversal
                var titles = ''
                titles += '<span>' + '重要性：' + riskImportant + '</span></br>'
                titles += '<span>' + '普遍性：' + riskUniversal+ '</span>'

                $('#lastRisk').append(titles)


                $('.layui-layer-content-my').css('left',$(obj).offset().left).css('top',$(obj).offset().top)
            }

            window.closeRisk = function(){
                $('.layui-layer-content-my').hide()
            }

            //查询业务领域下拉
            window.queryBusiFieldList = function(){
                $.ajax({
                    url:ctx+"/pro/auditbulletin/queryBusiFieldList"
                    ,type:"POST"
                    ,dataType:"JSON"
                    ,success:function(res){
                        if(res.httpCode == 200){
                            //业务领域
                            lp(tpl, res.data, $("#select-dic-tpl").html(), $('#fieldCode'));
                            $("#fieldCode").val(fieldCode);
                            form.render();
                        }else{
                            frm.error("初始查询有误，请联系管理员");
                        }
                    }
                })
            }

            /**
             * 监听业务领域下拉列表
             */
            form.on('select(fieldCode)', function (data) {
                fieldCode = data.value;
            });



            queryBusiFieldList();
            initAchieveList();
        })
</script>
