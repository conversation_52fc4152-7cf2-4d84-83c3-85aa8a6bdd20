<!--查看整体追责-->
<!--#include virtual ="include/header.html"-->
<style>
    .layui-table tr td .layui-table-cell.laytable-cell-2-accountabilityFlag,
    .layui-table tr td .layui-table-cell.laytable-cell-2-punishLevel,
    .layui-table tr td .layui-table-cell.laytable-cell-2-blackFlag {
        overflow: visible;
    }

    .layui-table tr [data-field="accountabilityFlag"] .layui-table-cell,
    .layui-table tr [data-field="punishLevel"] .layui-table-cell,
    .layui-table tr [data-field="blackFlag"] .layui-table-cell {
        overflow: visible;
    }

    .new-style .layui-form-item.layui-form-item-sm .layui-unselect {
        height: 26px !important;
    }

    .layui-form-select dl dd {
        line-height: 22px;
    }

    .layui-form-selected dl {
        top: 0px;
        padding: 0px;
    }

    .layui-table-cell {
        height: auto;
        padding: 0 8px;
        line-height: 20px;
        position: relative;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        box-sizing: border-box;
    }

    .layui-form-pane .layui-form-label {
        background: transparent;
    }

    .form-conmon-more .layui-form-item {
        margin-bottom: 12px;
    }

    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 20px 0 15px 0;
    }

    .section-title {
        display: flex;
        align-items: center;
        margin: 0;
        font-size: 16px;
        font-weight: bold;
        color: #333;
    }

    .section-title::before {
        content: '';
        width: 4px;
        height: 18px;
        background-color: #ff4444;
        margin-right: 8px;
        border-radius: 2px;
    }

    .section-buttons {
        display: flex;
        gap: 8px;
    }

    /* 生成整体追责按钮特殊样式 */
    .btn-generate-accountability {
        background-color: #009688 !important;
        border-color: #009688 !important;
        color: #fff !important;
    }

    .btn-generate-accountability:hover {
        background-color: #00796b !important;
        border-color: #00796b !important;
    }

    .btn-generate-accountability:active {
        background-color: #004d40 !important;
        border-color: #004d40 !important;
    }

    /* 禁用状态的生成整体追责按钮 */
    .btn-generate-accountability.layui-btn-disabled {
        background-color: #c2c2c2 !important;
        border-color: #c2c2c2 !important;
        color: #fff !important;
        opacity: 0.6;
        cursor: not-allowed !important;
    }

    .btn-generate-accountability.layui-btn-disabled:hover {
        background-color: #c2c2c2 !important;
        border-color: #c2c2c2 !important;
    }

    /* 上传附件按钮样式 */
    #uploadAttachmentBtnMain.layui-btn-disabled {
        opacity: 0.6;
        cursor: not-allowed !important;
        background-color: #c2c2c2 !important;
        border-color: #c2c2c2 !important;
    }

    #uploadAttachmentBtnMain.layui-btn-disabled:hover {
        background-color: #c2c2c2 !important;
        border-color: #c2c2c2 !important;
    }

    /* 附件类型选择框样式 */
    #attachmentType {
        transition: border-color 0.3s ease;
    }

    #attachmentType:focus {
        border-color: #1e9fff !important;
        outline: none;
    }

    /* 附件操作按钮样式 */
    .table-btn {
        margin-right: 8px;
        padding: 4px 8px;
        border-radius: 3px;
        transition: all 0.3s ease;
    }

    .table-btn:hover {
        background-color: #f5f5f5;
    }

    .table-btn:last-child {
        margin-right: 0;
    }

    /* 附件表格样式优化 */
    #attachmentTable .layui-table-cell {
        height: auto;
        line-height: 1.4;
    }

    /* 批量删除附件按钮样式 */
    #batchDeleteAttachmentBtn.layui-btn-disabled {
        opacity: 0.6;
        cursor: not-allowed !important;
        background-color: #c2c2c2 !important;
        border-color: #c2c2c2 !important;
    }

    #batchDeleteAttachmentBtn.layui-btn-disabled:hover {
        background-color: #c2c2c2 !important;
        border-color: #c2c2c2 !important;
    }

    /* 必填字段红星样式 */
    .layui-form-label i[style*="color: #c20000"] {
        font-weight: bold;
        font-size: 12px;
        line-height: 1;
        padding-right:2px;
    }

    /* 必填字段标签样式优化 */
    .layui-form-label {
        position: relative;
        padding-left: 8px;
    }

    /* 项目筛选按钮样式 */
    #projectFilterBtn {
        background-color: #1e9fff !important;
        border-color: #1e9fff !important;
        color: #fff !important;
    }

    #projectFilterBtn:hover {
        background-color: #0078d4 !important;
        border-color: #0078d4 !important;
    }

    #projectFilterBtn:active {
        background-color: #005a9e !important;
        border-color: #005a9e !important;
    }

    /* 禁用状态的项目筛选按钮 */
    #projectFilterBtn.layui-btn-disabled {
        background-color: #c2c2c2 !important;
        border-color: #c2c2c2 !important;
        color: #fff !important;
        opacity: 0.6;
        cursor: not-allowed !important;
    }

    #projectFilterBtn.layui-btn-disabled:hover {
        background-color: #c2c2c2 !important;
        border-color: #c2c2c2 !important;
    }

    /* 追责中状态的只读字段样式 */
    .readonly-field {
        background-color: #f5f5f5 !important;
        color: #999 !important;
        cursor: not-allowed !important;
        border-color: #e6e6e6 !important;
    }

    /* 状态提示样式 */
    .status-notice {
        font-size: 14px;
        line-height: 1.5;
    }

    .status-notice i {
        margin-right: 8px;
    }

    /* 查看模式样式 */
    .view-mode-header {
        background: #e8f4fd;
        border: 1px solid #91d5ff;
        padding: 12px 16px;
        margin-bottom: 16px;
        border-radius: 4px;
        color: #0050b3;
        font-size: 14px;
        display: flex;
        align-items: center;
    }

    .view-mode-header i {
        margin-right: 8px;
        font-size: 16px;
    }

    /* 查看模式下的表格样式 */
    .layui-table tbody tr:hover {
        background-color: #f5f5f5 !important;
    }
</style>

<body>
    <div class="layui-fluid larry-wrapper">
        <section class="panel panel-padding">
            <!-- 查看模式提示 -->
            <div class="view-mode-header">
                <i class="iconfont">&#xe6b6;</i>
                当前为查看模式，所有信息仅供查看，不可编辑
            </div>
            <div class="form-bg-box" style="
    background: #f5f7fa;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    border: 1px solid #e4e7ed;
    padding: 24px 16px 8px 16px;
    margin-bottom: 16px;">
                <form class="layui-form layui-form-pane form-conmon form-conmon-more" data-params='{bind:true }'
                    id="fillForm">
                    <input type="hidden" id="accountabilityId" name="accountabilityId" />
                    <div class="layui-row layui-col-space10 transition-500ms">
                        <div class="layui-col-md4 layui-col-sm4">
                            <div class="layui-form-item layui-form-item-sm">
                                <label class="layui-form-label">立项单位</label>
                                <div class="layui-input-block">
                                    <input class="layui-input readonly-field" id="sendProvName" name="sendProvName"
                                        type="text" readonly />
                                    <input type="hidden" id="sendProvCode" name="sendProvCode" />
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4 layui-col-sm4">
                            <div class="layui-form-item layui-form-item-sm">
                                <label class="layui-form-label">再审计期间</label>
                                <div class="layui-input-block">
                                    <input class="layui-input readonly-field" id="checkMonth" name="checkMonth"
                                        readonly="readonly">
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4 layui-col-sm4">
                            <div class="layui-form-item layui-form-item-sm">
                                <label class="layui-form-label">再审计名称</label>
                                <div class="layui-input-block">
                                    <input class="layui-input readonly-field" id="checkAuditName" name="checkAuditName"
                                        type="text" readonly />
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4 layui-col-sm4">
                            <div class="layui-form-item layui-form-item-sm">
                                <label class="layui-form-label">审计时间区间</label>
                                <div class="layui-input-block">
                                    <input class="layui-input readonly-field" id="auditPeriod" name="auditPeriod"
                                        readonly="readonly">
                                </div>
                            </div>
                        </div>

                        <div class="layui-col-md4 layui-col-sm4">
                            <div class="layui-form-item layui-form-item-sm">
                                <label class="layui-form-label">是否对下检查</label>
                                <div class="layui-input-block">
                                    <input class="layui-input readonly-field" id="isSubCheckDisplay" name="isSubCheckDisplay"
                                        type="text" readonly />
                                    <input type="hidden" id="isSubCheck" name="isSubCheck" />
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4 layui-col-sm4">
                            <div class="layui-form-item layui-form-item-sm">
                                <label class="layui-form-label">再审计小组成员</label>
                                <div class="layui-input-block">
                                    <textarea class="layui-textarea readonly-field" id="auditTeam" name="auditTeam"
                                        readonly rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4 layui-col-sm4">
                            <div class="layui-form-item layui-form-item-sm">
                                <label class="layui-form-label">组织方式</label>
                                <div class="layui-input-block">
                                    <input class="layui-input readonly-field" id="auditTypeName" name="auditTypeName" type="text"
                                        readonly />
                                    <input type="hidden" id="auditType" name="auditType" />
                                </div>
                            </div>
                        </div>
                        <!-- 整改状态字段，只在追责中状态时显示 -->
                        <div class="layui-col-md4 layui-col-sm4" id="reformStatusDiv">
                            <div class="layui-form-item layui-form-item-sm">
                                <label class="layui-form-label">整改状态</label>
                                <div class="layui-input-block">
                                    <input class="layui-input readonly-field" id="reformStatusDisplay" name="reformStatusDisplay"
                                        type="text" readonly />
                                    <input type="hidden" id="reformStatus" name="reformStatus" />
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="layui-form" id="checkProjectSection" style="margin-top:10px; display: none;">
                <div class="section-header">
                    <div class="section-title">抽查项目列表</div>
                    <div class="section-buttons">
                        <button class="layui-btn layui-btn-sm" onclick="exportProject()">
                            <i class="iconfont search-icon">&#xe61d; </i> 导出抽查项目
                        </button>
                    </div>
                </div>
                <table class="layui-table jq-even" id="checkLedgersTable" lay-filter="checkLedgersTable"></table>
            </div>

            <div class="layui-form" style="margin-top:10px">
                <div class="section-header">
                    <div class="section-title">项目台账</div>
                </div>

                <table class="layui-table jq-even" id="accountabilityTable" lay-filter="accountabilityTable"></table>

                <div class="layui-form" style="margin-top:20px">
                    <div class="section-header">
                        <div class="section-title">附件列表</div>
                    </div>

                    <!-- 附件管理区域 - 按百分比分布（查看模式） -->
                    <div class="layui-row layui-col-space15">
                        <!-- 左侧：附件信息区域 (30%) -->
                        <div class="layui-col-md4">
                            <div class="attachment-info-panel" style="
                                background: #f8f9fa;
                                border: 1px solid #e9ecef;
                                border-radius: 6px;
                                padding: 20px;
                                height: 100%;
                                min-height: 200px;
                            ">
                                <h4 style="margin: 0 0 15px 0; color: #333; font-size: 14px; font-weight: bold;">
                                    <i class="iconfont" style="margin-right: 5px;">&#xe6b6;</i>附件信息
                                </h4>

                                <!-- 附件统计信息 -->
                                <div class="attachment-stats" style="
                                    padding: 15px;
                                    background: #fff;
                                    border-radius: 4px;
                                    border: 1px solid #e6e6e6;
                                ">
                                    <h5 style="margin: 0 0 10px 0; color: #666; font-size: 12px;">附件统计</h5>
                                    <div style="font-size: 12px; color: #999; line-height: 1.6;">
                                        <div>总数量：<span id="attachmentTotalCount" style="color: #1e9fff; font-weight: bold;">0</span> 个</div>
                                        <div>总大小：<span id="attachmentTotalSize" style="color: #1e9fff; font-weight: bold;">0</span> MB</div>
                                    </div>
                                </div>

                                <!-- 查看模式提示 -->
                                <div style="
                                    margin-top: 15px;
                                    padding: 10px;
                                    background: #e8f4fd;
                                    border: 1px solid #91d5ff;
                                    border-radius: 4px;
                                    font-size: 12px;
                                    color: #0050b3;
                                ">
                                    <i class="iconfont" style="margin-right: 5px;">&#xe63a;</i>
                                    查看模式：仅可下载附件，不可编辑
                                </div>
                            </div>
                        </div>

                        <!-- 右侧：附件列表表格 (70%) -->
                        <div class="layui-col-md8">
                            <div class="attachment-table-panel" style="
                                background: #fff;
                                border: 1px solid #e9ecef;
                                border-radius: 6px;
                                padding: 15px;
                                min-height: 200px;
                            ">
                                <table class="layui-table jq-even" id="attachmentTable" lay-filter="attachmentTable"></table>
                            </div>
                        </div>
                    </div>
                </div>
        </section>
    </div>
</body>
<!--#include virtual ="include/version.html"-->
<!--查看模式：不需要编辑模板-->

<!--附件操作模板-->
<script id="attachmentBar" type="text/html">
    <a class="table-btn" type="button" title="下载" onclick="downloadAttachment('{{d.attachmentId}}', '{{d.fileName}}')">
        <i class="iconfont" style="color: #1e9fff;">&#xe60c;</i>
    </a>
</script>

<!--项目台账查看操作模板-->
<script id="accountabilityViewBar" type="text/html">
    <a class="table-btn" type="button" title="查看" onclick="showCheckLedgersList('{{d.id}}')">
        <i class="iconfont">&#xe6b6;</i>
    </a>
</script>

<!--抽查项目查看操作模板-->
<script id="checkLedgersViewBar" type="text/html">
    <a class="table-btn" type="button" title="查看" onclick="showCheckLedgersDetail('{{d.endAccountAttrId}}')">
        <i class="iconfont">&#xe6b6;</i>
    </a>
</script>

<!--查看模式：不需要整改状态选项模板-->
<script type="text/javascript">
    layui.use(['jqdate', 'jqform', 'jqfrm', 'jqbind', 'table', 'layer', 'jquery', 'upload'], function () {
        var $ = layui.jquery;
        var frm = layui.jqfrm;
        var form = layui.jqform;
        var jqbind = layui.jqbind;
        var layer = layui.layer;
        var table = layui.table;
        var ctx = top.global.ctx;
        var laydate = layui.laydate;
        var tpl = layui.laytpl;
        var element = layui.element;
        var upload = layui.upload;

        // 追责项目表格数据缓存
        var accountabilityTableData = [];

        // 格式化数字（包含null值处理）
        window.formatNumContainNull = function (num) {
            if (num == null || num == undefined || num == '') {
                return '';
            }
            return formatNum(num);
        };

        //主表主键
        var accountabilityId = getUrlParam("accountabilityId");
        //追责状态
        var accountabilityStatus = getUrlParam("accountabilityStatus");

        // 查询追责信息
        window.queryAccountabilityInfo = function () {
            var indexZG = layer.load(1, {
                shade: [0.4, '#fff']
            });

            // 判断是新增还是编辑
            if (!accountabilityId || accountabilityId == '' || accountabilityId == 'undefined') {
                // 新增模式：调用新增接口获取基本信息并生成追责主键
                queryAddAccountabilityInfo(indexZG);
            } else {
                // 编辑模式：调用原有接口
                queryExistingAccountabilityInfo(indexZG);
            }
        }

        // 新增时查询基本信息并生成追责主键
        window.queryAddAccountabilityInfo = function (indexZG) {
            $.ajax({
                url: ctx + "/prj/overallAccountabilitynew/queryAddAccountabilityInfo",
                type: "POST",
                dataType: "json",
                data: JSON.stringify({}),
                contentType: "application/json;charset=UTF-8",
                success: function (ret) {
                    if (200 == ret.httpCode) {
                        var data = ret.data;

                        // 设置生成的追责主键
                        if (data.accountabilityId) {
                            accountabilityId = data.accountabilityId;
                        }

                        // 处理立项单位：默认显示当前登录人所在省分，集团默认显示"集团"，不可修改
                        if (data.sendProvCode && data.sendProvName) {
                            $("#sendProvCode").val(data.sendProvCode);
                            $("#sendProvName").val(data.sendProvName);
                        }

                        // 填充表单数据
                        form.val('fillForm', data);

                        // 处理日期字段格式化
                        if (data.selectStartMonth && data.selectEndMonth) {
                            $("#checkMonth").val(data.selectStartMonth + " - " + data.selectEndMonth);
                        }
                        if (data.auditDateStart && data.auditDateEnd) {
                            $("#auditPeriod").val(data.auditDateStart + " - " + data.auditDateEnd);
                        }

                        // 处理是否对下检查显示
                        $("#isSubCheck").val(data.checkBelow);
                        $("#isSubCheckDisplay").val(data.checkBelow == '1' ? '是' : '否');

                        // 处理组织方式回显
                        if (data.auditType) {
                            setAuditTypeName(data.auditType);
                        }

                        // 重新渲染表单
                        form.render();

                        // 根据是否对下检查状态控制抽查项目列表显示
                        queryCheckLedgersTableInfo();
                        // 加载项目台账数据
                        queryAccountabilityTable();

                        layer.close(indexZG);
                    } else {
                        layer.close(indexZG);
                        frm.error(ret.msg || "获取基本信息失败");
                    }
                },
                error: function (e) {
                    layer.close(indexZG);
                    frm.error("网络异常，获取基本信息失败");
                }
            });
        }

        // 编辑时查询已有追责信息并判断权限
        window.queryExistingAccountabilityInfo = function (indexZG) {
            $.ajax({
                url: ctx + "/prj/overallAccountabilitynew/queryShowAccountabilityInfo",
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ accountabilityId: accountabilityId }),
                contentType: "application/json;charset=UTF-8",
                success: function (ret) {
                    if (200 == ret.httpCode) {
                        var data = ret.data;

                        // 检查是否有权限错误信息
                        if (data.errorMsg) {
                            layer.close(indexZG);
                            frm.error(data.errorMsg);
                            return;
                        }

                        // 处理立项单位：显示接口返回的省分信息
                        if (data.sendProvCode && data.sendProvName) {
                            $("#sendProvCode").val(data.sendProvCode);
                            $("#sendProvName").val(data.sendProvName);
                        }

                        // 处理日期字段格式化
                        if (data.selectStartMonth && data.selectEndMonth) {
                            $("#checkMonth").val(data.selectStartMonth + " - " + data.selectEndMonth);
                        }
                        if (data.auditDateStart && data.auditDateEnd) {
                            $("#auditPeriod").val(data.auditDateStart + " - " + data.auditDateEnd);
                        }

                        // 处理是否对下检查显示
                        $("#isSubCheck").val(data.checkBelow);
                        $("#isSubCheckDisplay").val(data.checkBelow == '1' ? '是' : '否');

                        //再审计名称
                        $("#checkAuditName").val(data.checkAuditName);
                        //再审计小组成员
                        $("#auditTeam").val(data.auditUserNames);

                        // 处理组织方式回显
                        if (data.auditType) {
                            setAuditTypeName(data.auditType);
                        }

                        // 处理整改状态显示
                        if (data.reformStatus !== undefined && data.reformStatus !== null) {
                            $("#reformStatus").val(data.reformStatus);
                            var reformStatusText = '';
                            if (data.reformStatus == '1') {
                                reformStatusText = '整改中';
                            } else if (data.reformStatus == '2') {
                                reformStatusText = '整改完成';
                            }
                            $("#reformStatusDisplay").val(reformStatusText);
                        }

                        // 重新渲染表单
                        form.render();

                        // 根据是否对下检查状态控制抽查项目列表显示
                        queryCheckLedgersTableInfo();

                        // 加载项目台账数据
                        queryAccountabilityTable();

                        layer.close(indexZG);
                    } else {
                        layer.close(indexZG);
                        frm.error(ret.msg || "获取追责信息失败");
                    }
                },
                error: function (e) {
                    layer.close(indexZG);
                    frm.error("网络异常，获取追责信息失败");
                }
            });
        }



        window.queryAccountabilityTable = function (data) {
            // 如果传入了数据，直接渲染（用于生成整体追责、保存等操作）
            if (data && data.length > 0) {
                renderAccountabilityTable(data);
                return;
            }

            // 否则从接口获取数据
            var indexZG = layer.load(1, {
                shade: [0.4, '#fff']
            });

            // 获取再审计期间参数
            var checkMonth = $("#checkMonth").val();
            var params = {
                accountabilityId: accountabilityId,
                checkBelow: $("#isSubCheck").prop("checked") ? "1" : "0"
            };

            if (checkMonth) {
                params.selectStartMonth = checkMonth.substr(0, 6);
                params.selectEndMonth = checkMonth.substr(9);
            }

            $.ajax({
                url: ctx + "/prj/overallAccountabilitynew/queryAccountabilityDetailList",
                type: "POST",
                dataType: "json",
                data: JSON.stringify(params),
                contentType: "application/json;charset=UTF-8",
                success: function (ret) {
                    layer.close(indexZG);
                    if (ret.httpCode == 200 && ret.data) {
                        renderAccountabilityTable(ret.data);
                    } else {
                        renderAccountabilityTable([]);
                    }
                },
                error: function (e) {
                    layer.close(indexZG);
                    renderAccountabilityTable([]);
                    frm.error("网络异常，获取项目台账数据失败");
                }
            });
        };

        // 渲染项目台账表格
        window.renderAccountabilityTable = function (data) {
            table.render({
                elem: '#accountabilityTable',
                id: 'accountabilityTable',
                data: data || [],
                height: 'full-300',
                page: false,
                even: true, //开启隔行背景
                cols: [[
                    { type: 'numbers', title: '序号', width: 50, align: 'center', rowspan: 3 },
                    { title: '整体处罚', width: 760, align: 'center', style: 'text-align: center', colspan: 6 },
                    { field: 'sendProvName', title: '报审省分', width: 200, align: 'center', style: 'text-align: center', rowspan: 3 },
                    { field: 'checkAuditName', title: '再审计名称', width: 200, align: 'center', style: 'text-align: left', rowspan: 3 },
                    { field: 'checkStartEndMonth', title: '再审计期间', width: 150, align: 'center', style: 'text-align: center', rowspan: 3 },
                    { field: 'agencyName', title: '原审计机构名称', width: 300, align: 'center', style: 'text-align: left', rowspan: 3 },
                    { field: 'checkProNum', title: '再审计<br />项目数量', width: 100, align: 'center', style: 'text-align: center', rowspan: 3 },
                    { field: 'professionOneName', title: '涉及A级专业', width: 140, align: 'center', style: 'text-align: left', rowspan: 3 },
                    { title: '项目送审金额合计(元)', width: 720, align: 'center', style: 'text-align: center', colspan: 6 },
                    { title: '初次审减金额合计（元）', width: 720, align: 'center', style: 'text-align: center', colspan: 6 },
                    { title: '再审计审减金额合计(元)', width: 720, align: 'center', style: 'text-align: center', colspan: 6 },
                    { title: '整体评价标准', width: 420, align: 'center', style: 'text-align: center', colspan: 3 },
                ], [
                    { field: 'accountabilityFlag', title: '是否追责', width: 120, align: 'center', style: 'text-align: center', rowspan: 2, templet: function(d) {
                        return d.accountabilityFlag == '1' ? '是' : (d.accountabilityFlag == '0' ? '否' : '');
                    }},
                    { field: 'punishLevel', title: '处罚档次', width: 120, align: 'center', style: 'text-align: center', rowspan: 2, templet: function(d) {
                        return d.punishLevel == '0' ? '一般' : (d.punishLevel == '1' ? '严重' : (d.punishLevel == '2' ? '重大' : ''));
                    }},
                    { field: 'payScaleInterval', title: '赔偿全部同类项目<br>审计费比例（%）', width: 140, align: 'center', style: 'text-align: center', rowspan: 2 },
                    { title: '处罚措施', width: 380, align: 'center', style: 'text-align: center', colspan: 3 },
                    {
                        field: 'sendAmountExecutionCost', title: '施工费', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.sendAmountExecutionCost);
                        }
                    },
                    {
                        field: 'sendAmountMaterialsCostB', title: '其中:乙供材料费', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.sendAmountMaterialsCostB);
                        }
                    },
                    {
                        field: 'sendAmountMaterialsCostA', title: '甲供材料费', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.sendAmountMaterialsCostA);
                        }
                    },
                    {
                        field: 'sendAmountEquit', title: '甲供设备费', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.sendAmountEquit);
                        }
                    },
                    {
                        field: 'sendAmountOther', title: '其它', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.sendAmountOther);
                        }
                    },
                    {
                        field: 'sendAmount', title: '合计', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.sendAmount);
                        }
                    },
                    {
                        field: 'cutAmountExecutionCost', title: '施工费', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.cutAmountExecutionCost);
                        }
                    },
                    {
                        field: 'cutAmountMaterialsCostB', title: '其中:乙供材料费', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.cutAmountMaterialsCostB);
                        }
                    },
                    {
                        field: 'cutAmountMaterialsCostA', title: '甲供材料费', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.cutAmountMaterialsCostA);
                        }
                    },
                    {
                        field: 'cutAmountEquit', title: '甲供设备费', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.cutAmountEquit);
                        }
                    },
                    {
                        field: 'cutAmountOther', title: '其它', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.cutAmountOther);
                        }
                    },
                    {
                        field: 'cutAmount', title: '合计', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.cutAmount);
                        }
                    },
                    {
                        field: 'checkCutAmountExecutionCost', title: '施工费', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.checkCutAmountExecutionCost);
                        }
                    },
                    {
                        field: 'checkCutAmountMaterialsCostB', title: '其中:乙供材料费', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.checkCutAmountMaterialsCostB);
                        }
                    },
                    {
                        field: 'checkCutAmountMaterialsCostA', title: '甲供材料费', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.checkCutAmountMaterialsCostA);
                        }
                    },
                    {
                        field: 'checkCutAmountEquit', title: '甲供设备费', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.checkCutAmountEquit);
                        }
                    },
                    {
                        field: 'checkCutAmountOther', title: '其它', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.checkCutAmountOther);
                        }
                    },
                    {
                        field: 'checkCutAmount', title: '合计', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.checkCutAmount);
                        }
                    },
                    { field: 'averageCheckCutRate', title: '全部抽查项目<br>平均再审减率', width: 140, align: 'center', style: 'text-align: center', rowspan: 2 },
                    { field: 'checkCutAmountExecutionCostAverage', title: '本省同类工程<br>施工费平均审减率', width: 140, align: 'center', style: 'text-align: center', rowspan: 2 },
                    { field: 'averageDeviationDegree', title: '全部抽查项目<br>平均再审减偏离度', width: 140, align: 'center', style: 'text-align: center', rowspan: 2 },
                ], [
                    { field: 'reduceShare', title: '调减份额', width: 120, align: 'center', style: 'text-align: center' },
                    { field: 'payAuditAmount', title: '或赔偿全部同类<br>项目审计费（元）', width: 140, align: 'center', style: 'text-align: right', templet: function(d) {
                        return formatNumContainNull(d.payAuditAmount);
                    }},
                    { field: 'blackFlag', title: '终止合作并<br>列入黑名单', width: 120, align: 'center', style: 'text-align: center', templet: function(d) {
                        return d.blackFlag == '1' ? '是' : (d.blackFlag == '0' ? '否' : '');
                    }},
                    { title: '操作', width: 80, align: 'center', templet: '#accountabilityViewBar', fixed: 'right' }
                ]
                ],
                done: function (res) {
                    accountabilityTableData = res.data || [];
                }
            });
        }

        /*****************************查看模式：移除所有编辑事件处理***********************************/

        /*****************************查看模式：移除日期选择器***********************************/

        //获取参数
        window.getParams = function () {
            var params = transformToJson($('#fillForm').serializeArray());

            // 追责表主键
            params.accountabilityId = accountabilityId;

            // 处理再审计期间
            var checkMonth = $("#checkMonth").val();
            if (checkMonth) {
                params.checkStartMonth = checkMonth.substr(0, 6);
                params.checkEndMonth = checkMonth.substr(9);
                // 同时设置选择账期字段
                params.selectStartMonth = checkMonth.substr(0, 6);
                params.selectEndMonth = checkMonth.substr(9);
            }

            // 处理审计时间区间
            var auditPeriod = $("#auditPeriod").val();
            if (auditPeriod) {
                params.auditStartMonth = auditPeriod.substr(0, 6);
                params.auditEndMonth = auditPeriod.substr(9);
                // 同时设置审计时间区间字段
                params.auditDateStart = auditPeriod.substr(0, 6);
                params.auditDateEnd = auditPeriod.substr(9);
            }

            // 处理是否对下检查
            params.checkBelow = $("#isSubCheck").prop("checked") ? "1" : "0";

            // 设置整体追责状态（默认为草稿中）
            if (!params.accountabilityStatus) {
                params.accountabilityStatus = "0";
            }

            // 处理整改状态（只在追责中状态时有效）
            if (accountabilityStatus == '2') {
                params.reformStatus = $("#reformStatus").val() || "";
            }

            return params;
        }

        /*****************************查看模式：移除选择用户功能***********************************/

        window.queryCheckLedgersTableInfo = function () {
            // 只有当"是否对下检查"为"是"时才加载抽查项目列表
            var checkBelow = $("#isSubCheck").prop("checked");
            if (!checkBelow) {
                $("#checkProjectSection").hide();
                return;
            }

            $("#checkProjectSection").show();

            table.render({
                elem: '#checkLedgersTable',
                id: 'checkLedgersTable',
                url: ctx + '/prj/accountabilityfilter/queryFilterProject',
                where: { accountabilityId: accountabilityId },
                page: true,
                even: true, //开启隔行背景
                limits: [10, 30, 60, 120, 300],
                limit: 10,
                //height: 'full-180',
                cols: [[
                    { type: 'numbers', title: '序号', width: 60, align: 'center', rowspan: 3 },
                    {
                        field: '', title: '合同/订单编号', width: 200, align: 'center', style: 'text-decoration:underline;text-align:left;cursor:pointer;color:#c20000;', rowspan: 3, event: 'showContractOrder',
                        templet: function (d) {
                            return d.orderId || d.contractCode || '';
                        }
                    },
                    { field: 'firstAuditMonth', title: '台账账期', width: 120, align: 'center', rowspan: 3 },
                    { field: 'auditMonth', title: '台账账期', width: 120, align: 'center', style: 'text-align: center', rowspan: 3 },
                    { field: 'ledgersTypeName', title: '台账类型', width: 120, align: 'center', rowspan: 3 },
                    { field: 'ledgersFlagName', title: '生成方式', width: 120, align: 'center', rowspan: 3 },
                    { field: 'sendProvName', title: '报审省分', width: 120, align: 'center', rowspan: 3 },
                    { field: 'sendAreaName', title: '报审地市', width: 120, align: 'center', rowspan: 3 },
                    { field: 'projectName', title: '项目名称', width: 340, align: 'center', style: 'text-align: left', rowspan: 3 },
                    { field: 'projectCodeErp', title: '项目ERP编码', width: 150, align: 'center', style: 'text-align: left', rowspan: 3 },
                    { field: 'professionOneName', title: 'A级专业名称', width: 120, align: 'center', style: 'text-align: left', rowspan: 3 },
                    { field: 'professionTwoName', title: 'B级专业名称', width: 120, align: 'center', style: 'text-align: left', rowspan: 3 },
                    { field: 'professionThreeName', title: 'C级专业名称', width: 120, align: 'center', style: 'text-align: left', rowspan: 3 },
                    { field: 'designCode', title: '设计批复文号', width: 200, align: 'center', style: 'text-align: left', rowspan: 3 },
                    {
                        field: 'designInvest', title: '批复概算投资额', width: 120, align: 'center', style: 'text-align: right', rowspan: 3
                        , templet: function (d) {
                            return formatNum(d.designInvest);
                        }
                    },
                    {
                        field: 'designBuildInvest', title: '其中：建安投资额', width: 120, align: 'center', style: 'text-align: right', rowspan: 3
                        , templet: function (d) {
                            return formatNum(d.designBuildInvest);
                        }
                    },
                    { field: 'auditTypeName', title: '项目差异化审计类型', width: 180, align: 'center', style: 'text-align: center', rowspan: 3 },
                    { field: 'constructionUnit', title: '施工单位', width: 180, align: 'center', style: 'text-align: left', rowspan: 3 },
                    { title: '原结算审计信息', align: 'center', style: 'text-align: center', colspan: 48 },
                    { field: 'isReAuditCheckName', title: '是否再审计复核', width: 120, align: 'center', style: 'text-align: center', rowspan: 3 },
                    { field: 'isReportProvAuditCheckName', title: '复核审计<br>是否上报省分', width: 120, align: 'center', style: 'text-align: center', rowspan: 3 },
                    { field: 'isKeyCheckName', title: '是否重点核查', width: 120, align: 'center', style: 'text-align: center', rowspan: 3 },
                    { title: '复核结算审计信息', align: 'center', style: 'text-align: center', colspan: 35 },
                    { field: 'reportFinalUnitName', title: '出具审计报告单位名称', width: 350, align: 'center', style: 'text-align: left', rowspan: 3 },
                    { field: 'auditReportCode', title: '审计报告编号', width: 160, align: 'center', style: 'text-align: left', rowspan: 3 },
                    { field: 'auditReportName', title: '审计报告名称', width: 160, align: 'center', style: 'text-align: left', rowspan: 3 },
                    {
                        field: 'cutRateExecheckCutionCost', title: '施工费再审减率=<br>再审减金额/原始送审金额', width: 170, align: 'center', style: 'text-align: right', rowspan: 3
                        , templet: function (d) {
                            return formatNum(d.cutRateExecheckCutionCost);
                        }
                    },
                    {
                        field: 'deviationDegree', title: '施工费再审减偏离度=<br>再审减金额/原审减金额', width: 170, align: 'center', style: 'text-align: right', rowspan: 3
                        , templet: function (d) {
                            if (d.deviationDegree) {
                                return d.deviationDegree;
                            } else {
                                return '∞';
                            }
                        }
                    },
                    {
                        field: 'relativeCutRate', title: '相对再审减率=施工费<br>再审减率/本省上年度同类<br>工程施工费平均审减率', width: 170, align: 'center', style: 'text-align: right', rowspan: 3
                        , templet: function (d) {
                            if (d.relativeCutRate) {
                                return d.relativeCutRate;
                            } else {
                                return '∞';
                            }
                        }
                    },
                    { title: '追责信息', align: 'center', style: 'text-align: center', colspan: 7 },
                    { title: '操作', width: 100, align: 'center', templet: '#configBar', fixed: 'right', rowspan: 3 }
                ], [
                    { field: 'contractOrderName', title: '合同/订单名称', width: 250, align: 'center', style: 'text-align: left', rowspan: 2 },
                    {
                        field: 'addedTaxN', title: '合同/订单<br>金额（不含税）', width: 120, align: 'center', style: 'text-align: right', rowspan: 2
                        , templet: function (d) {
                            return formatNum(d.addedTaxN);
                        }
                    },
                    { field: 'sendOrgName', title: '报审部门', width: 200, align: 'center', style: 'text-align: left', rowspan: 2 },
                    { field: 'sendUserName', title: '报审人', width: 100, align: 'center', style: 'text-align: center', rowspan: 2 },
                    { field: 'endAccountCode', title: '审计工单号/批次号', width: 130, align: 'center', style: 'text-align: center', rowspan: 2 },
                    { title: '送审金额（元）', align: 'center', style: 'text-align: center', colspan: 6 },
                    { title: '审定金额（元）', align: 'center', style: 'text-align: center', colspan: 6 },
                    { title: '审减金额（元）', align: 'center', style: 'text-align: center', colspan: 6 },
                    { title: '审减率（%）', align: 'center', style: 'text-align: center', colspan: 6 },
                    { title: '审计费（元）', align: 'center', style: 'text-align: center', colspan: 7 },
                    { field: 'auditStartDate', title: '审计开始时间', width: 150, align: 'center', style: 'text-align: center', rowspan: 2 },
                    { field: 'auditSubmitDate', title: '审计结果提交时间', width: 150, align: 'center', style: 'text-align: center', rowspan: 2 },
                    { field: 'receiveUserName', title: '接审人', width: 100, align: 'center', style: 'text-align: center', rowspan: 2 },
                    { field: 'auditUserName', title: '审计人员', width: 100, align: 'center', style: 'text-align: center', rowspan: 2 },
                    { field: 'entrustUserName', title: '委托联系人', width: 100, align: 'center', style: 'text-align: center', rowspan: 2 },
                    { field: 'auditTypeIdName', title: '审计方式', width: 100, align: 'center', style: 'text-align: center', rowspan: 2 },
                    { field: 'auditUnit', title: '审计中介机构名称', width: 300, align: 'center', style: 'text-align: left', rowspan: 2 },
                    { field: 'auditLiableUserName', title: '审计组负责人', width: 120, align: 'center', style: 'text-align: center', rowspan: 2 },
                    { field: 'auditLeaderUser', title: '审计组组长', width: 120, align: 'center', style: 'text-align: center', rowspan: 2 },
                    { field: 'auditCrewUser', title: '审计组组员', width: 120, align: 'center', style: 'text-align: center', rowspan: 2 },
                    { field: 'checkAuditTypeName', title: '复核审计方式', width: 100, align: 'center', style: 'text-align: center', rowspan: 2 },
                    { field: 'checkAuditUnit', title: '复核审计单位', width: 300, align: 'center', style: 'text-align: left', rowspan: 2 },
                    { field: 'reAuditStartTime', title: '复核审计开始时间（再审计单<br>位接口人指派并接收之日）', width: 200, align: 'center', style: 'text-align: center', rowspan: 2 },
                    { field: 'reAuditEndTime', title: '复核审计完成时间（审计部<br>部门经理确认再审计结果之日）', width: 200, align: 'center', style: 'text-align: center', rowspan: 2 },
                    { title: '复核审定金额（元）', align: 'center', style: 'text-align: center', colspan: 6 },
                    { title: '复核审减金额（元）', align: 'center', style: 'text-align: center', colspan: 6 },
                    { title: '复核审减率（%）', align: 'center', style: 'text-align: center', colspan: 6 },
                    { title: '复核审计费（元）', align: 'center', style: 'text-align: center', colspan: 7 },
                    { title: '复核审计发现的主要问题', align: 'center', style: 'text-align: center', colspan: 3 },
                    { title: '复核审计单位信息', align: 'center', style: 'text-align: center', colspan: 3 },
                    { field: 'evaluateStandardName', title: '评价标准（再审减偏离度<br>、相对再审减率孰高）', width: 180, align: 'center', style: 'text-align: center', rowspan: 2 },
                    { field: 'accountabilityFlagName', title: '是否追责', width: 100, align: 'center', style: 'text-align: center', rowspan: 2 },
                    { field: 'accountabilityLevelName', title: '追责档次', width: 100, align: 'center', style: 'text-align: center', rowspan: 2 },
                    { field: 'proportionStartEnd', title: '赔偿复核审减金<br>额的比例区间', width: 120, align: 'center', style: 'text-align: center', rowspan: 2 },
                    {
                        field: 'proportion', title: '实际赔偿复核审<br>减金额的比例(%)', width: 120, align: 'center', style: 'text-align: right', rowspan: 2
                        , templet: function (d) {
                            if (d.proportion) {
                                return formatNum(d.proportion);
                            } else {
                                return '';
                            }

                        }
                    },
                    {
                        field: 'accountabilityAmount', title: '退还审计<br>费（元）', width: 120, align: 'center', style: 'text-align: right', rowspan: 2
                        , templet: function (d) {
                            if (d.accountabilityAmount) {
                                return formatNum(d.accountabilityAmount);
                            } else {
                                return ''
                            }

                        }
                    },
                    {
                        field: 'realityCheckCutAmount', title: '赔偿损失<br>金额（元）', width: 120, align: 'center', style: 'text-align: right', rowspan: 2
                        , templet: function (d) {
                            if (d.realityCheckCutAmount) {
                                return formatNum(d.realityCheckCutAmount);
                            } else {
                                return '';
                            }
                        }
                    },
                ], [
                    //初审送审
                    {
                        field: 'sendAmountExecutionCost', title: '施工费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.sendAmountExecutionCost);
                        }
                    },
                    {
                        field: 'sendAmountMaterialsCostB', title: '其中:乙供材料费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.sendAmountMaterialsCostB);
                        }
                    },
                    {
                        field: 'sendAmountMaterialsCostA', title: '甲供材料费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.sendAmountMaterialsCostA);
                        }
                    },
                    {
                        field: 'sendAmountEquit', title: '甲供设备费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.sendAmountEquit);
                        }
                    },
                    {
                        field: 'sendAmountOther', title: '其它', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.sendAmountOther);
                        }
                    },
                    {
                        field: 'sendAmount', title: '合计', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.sendAmount);
                        }
                    },
                    //初审审定
                    {
                        field: 'fixAmountExecutionCost', title: '施工费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.fixAmountExecutionCost);
                        }
                    },
                    {
                        field: 'fixAmountMaterialsCostB', title: '其中:乙供材料费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.fixAmountMaterialsCostB);
                        }
                    },
                    {
                        field: 'fixAmountMaterialsCostA', title: '甲供材料费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.fixAmountMaterialsCostA);
                        }
                    },
                    {
                        field: 'fixAmountEquit', title: '甲供设备费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.fixAmountEquit);
                        }
                    },
                    {
                        field: 'fixAmountOther', title: '其它', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.fixAmountOther);
                        }
                    },
                    {
                        field: 'fixAmount', title: '合计', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.fixAmount);
                        }
                    },
                    //初审审减
                    {
                        field: 'cutAmountExecutionCost', title: '施工费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.cutAmountExecutionCost);
                        }
                    },
                    {
                        field: 'cutAmountMaterialsCostB', title: '其中:乙供材料费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.cutAmountMaterialsCostB);
                        }
                    },
                    {
                        field: 'cutAmountMaterialsCostA', title: '甲供材料费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.cutAmountMaterialsCostA);
                        }
                    },
                    {
                        field: 'cutAmountEquit', title: '甲供设备费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.cutAmountEquit);
                        }
                    },
                    {
                        field: 'cutAmountOther', title: '其它', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.cutAmountOther);
                        }
                    },
                    {
                        field: 'cutAmount', title: '合计', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.cutAmount);
                        }
                    },
                    //初审审减率
                    {
                        field: 'cutRateExecutionCost', title: '施工费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.cutRateExecutionCost);
                        }
                    },
                    {
                        field: 'cutRateMaterialsCostB', title: '其中:乙供材料费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.cutRateMaterialsCostB);
                        }
                    },
                    {
                        field: 'cutRateMaterialsCostA', title: '甲供材料费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.cutRateMaterialsCostA);
                        }
                    },
                    {
                        field: 'cutRateEquit', title: '甲供设备费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.cutRateEquit);
                        }
                    },
                    {
                        field: 'cutRateOther', title: '其它', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.cutRateOther);
                        }
                    },
                    {
                        field: 'cutRate', title: '合计', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.cutRate);
                        }
                    },
                    //初审审计费
                    {
                        field: 'baseCost', title: '基本收费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.baseCost);
                        }
                    },
                    {
                        field: 'benefitCost', title: '效益收费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.benefitCost);
                        }
                    },
                    {
                        field: 'costCheckChange', title: '审计调整金额', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.costCheckChange);
                        }
                    },
                    {
                        field: 'costWithTax', title: '含税', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.costWithTax);
                        }
                    },
                    {
                        field: 'costOutTax', title: '不含税计', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.costOutTax);
                        }
                    },
                    {
                        field: 'costTaxAmount', title: '税金', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.costTaxAmount);
                        }
                    },
                    {
                        field: 'costRate', title: '税率（%）', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.costRate);
                        }
                    },
                    //复核审定
                    {
                        field: 'checkFixAmountExecutionCost', title: '施工费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.checkFixAmountExecutionCost);
                        }
                    },
                    {
                        field: 'checkFixAmountMaterialsCostB', title: '其中:乙供材料费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.checkFixAmountMaterialsCostB);
                        }
                    },
                    {
                        field: 'checkFixAmountMaterialsCostA', title: '甲供材料费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.checkFixAmountMaterialsCostA);
                        }
                    },
                    {
                        field: 'checkFixAmountEquit', title: '甲供设备费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.checkFixAmountEquit);
                        }
                    },
                    {
                        field: 'checkFixAmountOther', title: '其它', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.checkFixAmountOther);
                        }
                    },
                    {
                        field: 'checkFixAmount', title: '合计', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.checkFixAmount);
                        }
                    },
                    //复核审减
                    {
                        field: 'checkCutAmountExecutionCost', title: '施工费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.checkCutAmountExecutionCost);
                        }
                    },
                    {
                        field: 'checkCutAmountMaterialsCostB', title: '其中:乙供材料费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.checkCutAmountMaterialsCostB);
                        }
                    },
                    {
                        field: 'checkCutAmountMaterialsCostA', title: '甲供材料费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.checkCutAmountMaterialsCostA);
                        }
                    },
                    {
                        field: 'checkCutAmountEquit', title: '甲供设备费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.checkCutAmountEquit);
                        }
                    },
                    {
                        field: 'checkCutAmountOther', title: '其它', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.checkCutAmountOther);
                        }
                    },
                    {
                        field: 'checkCutAmount', title: '合计', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.checkCutAmount);
                        }
                    },
                    //复核审减率
                    {
                        field: 'checkCutRateExecutionCost', title: '施工费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.checkCutRateExecutionCost);
                        }
                    },
                    {
                        field: 'checkCutRateMaterialsCostB', title: '其中:乙供材料费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.checkCutRateMaterialsCostB);
                        }
                    },
                    {
                        field: 'checkCutRateMaterialsCostA', title: '甲供材料费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.checkCutRateMaterialsCostA);
                        }
                    },
                    {
                        field: 'checkCutRateEquit', title: '甲供设备费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.checkCutRateEquit);
                        }
                    },
                    {
                        field: 'checkCutRateOther', title: '其它', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.checkCutRateOther);
                        }
                    },
                    {
                        field: 'checkCutRate', title: '合计', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.checkCutRate);
                        }
                    },
                    //复核审计费（元）
                    {
                        field: 'checkBaseCost', title: '基本收费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.checkBaseCost);
                        }
                    },
                    {
                        field: 'checkBenefitCost', title: '效益收费', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.checkBenefitCost);
                        }
                    },
                    {
                        field: 'checkCostCheckChange', title: '审计调整金额', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.checkCostCheckChange);
                        }
                    },
                    {
                        field: 'checkCostWithTax', title: '含税', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.checkCostWithTax);
                        }
                    },
                    {
                        field: 'checkCostOutTax', title: '不含税', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.checkCostOutTax);
                        }
                    },
                    {
                        field: 'checkCostTaxAmount', title: '税金', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.checkCostTaxAmount);
                        }
                    },
                    {
                        field: 'checkCostRate', title: '税率（%）', width: 120, align: 'center', style: 'text-align: right'
                        , templet: function (d) {
                            return formatNum(d.checkCostRate);
                        }
                    },
                    //复核审计发现的主要问题
                    { field: 'isAuditNormativeProblemName', title: '审计过程的规范性<br>是否存在问题', width: 120, align: 'center', style: 'text-align: center' },
                    { field: 'isAuditFullProblemName', title: '审计依据的充分性<br>是否存在问题', width: 120, align: 'center', style: 'text-align: center' },
                    { field: 'isAuditAccuracyProblemName', title: '审计结果的准确性<br>是否存在问题', width: 120, align: 'center', style: 'text-align: center' },
                    //复核审计单位信息
                    { field: 'checkAuditLiableUserName', title: '审计组负责人', width: 100, align: 'center', style: 'text-align: center' },
                    { field: 'checkAuditLeaderUser', title: '审计组组长', width: 100, align: 'center', style: 'text-align: center' },
                    { field: 'checkAuditCrewUser', title: '审计组组员', width: 100, align: 'center', style: 'text-align: center' },
                    { title: '操作', width: 80, align: 'center', templet: '#checkLedgersViewBar', fixed: 'right' }
                ]
                ],
                done: function () {

                }
            });
        }

        // 保存再审计小组成员信息
        window.saveAuditUserList = function (userList, callback) {
            var indexZG = layer.load(1, {
                shade: [0.4, '#fff']
            });

            $.ajax({
                url: ctx + "/prj/overallAccountabilitynew/saveAuditUserList",
                type: "POST",
                dataType: "json",
                data: JSON.stringify({
                    accountabilityId: accountabilityId,
                    userList: userList
                }),
                contentType: "application/json;charset=UTF-8",
                success: function (ret) {
                    layer.close(indexZG);
                    if (ret.httpCode == 200) {
                        frm.alertMsg("保存再审计小组成员成功");
                        // 调用回调函数，传递成功状态和auditType
                        if (callback) {
                            callback(true, ret.auditType);
                        }
                    } else {
                        frm.error(ret.msg || "保存再审计小组成员失败");
                        // 调用回调函数，传递失败状态
                        if (callback) {
                            callback(false);
                        }
                    }
                },
                error: function (e) {
                    layer.close(indexZG);
                    frm.error("网络异常，保存再审计小组成员失败");
                    // 调用回调函数，传递失败状态
                    if (callback) {
                        callback(false);
                    }
                }
            });
        };

        /*****************************生成整体追责***********************************/
        /**
         * 生成整体追责按钮
         */
        window.createOverallAccountabilityInfo = function () {
            // 检查按钮是否被禁用
            var $btn = $('button[onclick="createOverallAccountabilityInfo()"]');
            if ($btn.hasClass('layui-btn-disabled')) {
                layer.msg('请先完善必要的参数信息！', { icon: 2 });
                return;
            }

            // 参数验证
            var params = getParams();
            if (!params.accountabilityId) {
                layer.msg('缺少追责主表主键参数！', { icon: 2 });
                return;
            }

            // 验证必要的时间参数
            var checkMonth = $("#checkMonth").val();
            if (!checkMonth) {
                layer.msg('请先选择再审计期间！', { icon: 2 });
                return;
            }

            // 验证审计时间区间
            var auditPeriod = $("#auditPeriod").val();
            if (!auditPeriod) {
                layer.msg('请先选择审计时间区间！', { icon: 2 });
                return;
            }

            // 确认对话框
            layer.confirm('确定要生成整体追责吗？<br><span style="color: #ff5722;">注意：此操作将重新生成追责数据！</span><br><span style="color: #666;">再审计期间：' + checkMonth + '</span><br><span style="color: #666;">审计时间区间：' + auditPeriod + '</span>', {
                icon: 3,
                title: '确认生成整体追责',
                area: ['450px', '220px']
            }, function (index) {
                // 显示加载遮盖层
                var indexZG = layer.load(1, {
                    shade: [0.4, '#fff'] //0.4透明度的白色背景
                });

                $.ajax({
                    url: ctx + "/prj/overallAccountability/reCreateAccountOverallAccountabilityInfo",
                    type: 'POST',
                    dataType: "JSON",
                    data: JSON.stringify(params),
                    contentType: "application/json;charset=UTF-8",
                    success: function (res) {
                        layer.close(indexZG);

                        if (res.httpCode == 200) {
                            if (res.data) {
                                var successMsg = '生成整体追责成功！';
                                if (res.data.length !== undefined) {
                                    successMsg += '共生成 ' + res.data.length + ' 条追责记录。';
                                }
                                layer.msg(successMsg, { icon: 1, time: 3000 });

                                //加载数据表格
                                queryAccountabilityTable(res.data);
                            } else {
                                layer.msg("该再审计期间暂无数据", { icon: 0 });
                            }
                        } else {
                            layer.msg(res.msg || '生成整体追责失败！', { icon: 2 });
                            $("#checkMonth").val("");
                        }
                    },
                    error: function (xhr, status, error) {
                        layer.close(indexZG);

                        var errorMsg = '网络异常，生成整体追责失败！';
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (response && response.msg) {
                                errorMsg = response.msg;
                            }
                        } catch (e) {
                            // 解析失败，使用默认错误信息
                        }

                        layer.msg(errorMsg, { icon: 2 });
                    }
                });

                // 关闭确认框
                layer.close(index);
            });
        };

        // 更新生成整体追责按钮状态
        function updateGenerateAccountabilityButtonStatus() {
            var checkMonth = $("#checkMonth").val();
            var auditPeriod = $("#auditPeriod").val();
            var $btn = $('button[onclick="createOverallAccountabilityInfo()"]');

            if (checkMonth && auditPeriod && accountabilityId) {
                $btn.removeClass('layui-btn-disabled').removeAttr('disabled');
            } else {
                $btn.addClass('layui-btn-disabled').attr('disabled', 'disabled');
            }
        }

        // 整体追责查看台账明细
        window.showCheckLedgersList = function (detailId) {
            var index = window.parent.layer.open({
                type: 2,
                maxmin: true,
                content: 'views/audit/prj/endaccount/endreauditstatic/checkLedgersList.html?accountabilityDetailId=' + detailId
                    + '&type=accountability',
                title: [
                    '再审计台账', 'font-size:14px;background-color: #f2f2f2;'
                ],
                area: ['95%', '98%'],
                fixed: true,
                success: function (layero, index) {
                },
                end: function () {
                }
            });
        };

        // 抽查项目查看台账明细
        window.showCheckLedgersDetail = function (endAccountAttrId) {
            var index = window.parent.layer.open({
                type: 2,
                maxmin: true,
                content: 'views/audit/prj/endaccount/endreauditstatic/checkLedgersList.html?accountabilityId=' + accountabilityId
                    + '&endAccountAttrId=' + endAccountAttrId + '&type=filter',
                title: [
                    '再审计台账', 'font-size:14px;background-color: #f2f2f2;'
                ],
                area: ['95%', '98%'],
                fixed: true,
                success: function (layero, index) {
                },
                end: function () {
                }
            });
        };

        /*****************************生成整体追责***********************************/

        /*****************************追责项目保存和提交***********************************/

        /**
         * 验证必填字段
         */
        function validateRequiredFields() {
            var errors = [];

            // 验证立项单位
            var sendProvName = $("#sendProvName").val();
            if (!sendProvName || sendProvName.trim() === '') {
                errors.push('立项单位不能为空');
            }

            // 验证再审计期间
            var checkMonth = $("#checkMonth").val();
            if (!checkMonth || checkMonth.trim() === '') {
                errors.push('再审计期间不能为空');
            }

            // 验证再审计名称
            var checkAuditName = $("#checkAuditName").val();
            if (!checkAuditName || checkAuditName.trim() === '') {
                errors.push('再审计名称不能为空');
            }

            // 验证审计时间区间
            var auditPeriod = $("#auditPeriod").val();
            if (!auditPeriod || auditPeriod.trim() === '') {
                errors.push('审计时间区间不能为空');
            }

            // 验证是否对下检查（这个字段是复选框，默认有值，但我们可以检查是否已设置）
            // 复选框默认是有值的，所以这里不需要特别验证

            // 验证再审计小组成员
            var auditTeam = $("#auditTeam").val();
            if (!auditTeam || auditTeam.trim() === '') {
                errors.push('再审计小组成员不能为空');
            }

            return errors;
        }

        /**
         * 添加必填字段实时验证
         */
        function addRequiredFieldValidation() {
            // 再审计名称实时验证
            $("#checkAuditName").on('blur', function () {
                var value = $(this).val();
                if (!value || value.trim() === '') {
                    $(this).css('border-color', '#ff5722');
                    layer.tips('再审计名称不能为空', this, {
                        tips: [1, '#ff5722'],
                        time: 2000
                    });
                } else {
                    $(this).css('border-color', '#e6e6e6');
                }
            });

            // 再审计小组成员实时验证
            $("#auditTeam").on('blur', function () {
                var value = $(this).val();
                if (!value || value.trim() === '') {
                    $(this).css('border-color', '#ff5722');
                    layer.tips('再审计小组成员不能为空', this, {
                        tips: [1, '#ff5722'],
                        time: 2000
                    });
                } else {
                    $(this).css('border-color', '#e6e6e6');
                }
            });

            // 输入时恢复正常边框颜色
            $("#checkAuditName, #auditTeam").on('input', function () {
                $(this).css('border-color', '#e6e6e6');
            });
        }
        /**
         * 保存追责项目信息
         */
        window.saveAccountabilityBtn = function () {
            // 验证必填字段
            var errors = validateRequiredFields();
            if (errors.length > 0) {
                layer.alert('请完善以下必填信息：<br>' + errors.join('<br>'), {
                    icon: 2,
                    title: '验证失败',
                    area: ['400px', 'auto']
                });
                return;
            }

            var indexZG = layer.load(1, {
                shade: [0.4, '#fff']
            });
            var params = getParams();
            params.overallAccountabilityList = table.cache['accountabilityTable'];
            // 添加追责主表主键
            params.accountabilityId = accountabilityId;

            // 验证追责主表主键
            if (!params.accountabilityId) {
                layer.msg('缺少追责主表主键参数！', {icon: 2});
                return;
            }

            console.log('保存参数：', {
                accountabilityId: params.accountabilityId,
                overallAccountabilityListCount: params.overallAccountabilityList ? params.overallAccountabilityList.length : 0
            });

            $.ajax({
                url: ctx + "/prj/overallAccountability/saveOverallAccountabilityInfo",
                type: 'POST',
                dataType: "JSON",
                data: JSON.stringify(params),
                contentType: "application/json;charset=UTF-8",
                success: function (res) {
                    if (res.httpCode == 200) {
                        layer.msg('保存成功！');
                        for (var i = 0; i < params.overallAccountabilityList.length; i++) {
                            params.overallAccountabilityList[i].checkAuditName = params.checkAuditName;
                        }
                        // 重新加载表格
                        queryAccountabilityTable(params.overallAccountabilityList);
                        layer.close(indexZG);
                    } else {
                        layer.close(indexZG);
                        frm.error(res.msg);
                    }
                }
            });
        };

        /**
         * 提交追责项目信息
         */
        window.submitAccountabilityBtn = function () {
            // 验证必填字段
            var errors = validateRequiredFields();
            if (errors.length > 0) {
                layer.alert('请完善以下必填信息：<br>' + errors.join('<br>'), {
                    icon: 2,
                    title: '验证失败',
                    area: ['400px', 'auto']
                });
                return;
            }

            frm.confirm("提交后无法进行修改，请确认", function () {
                var indexZG = layer.load(1, {
                    shade: [0.4, '#fff']
                });
                var params = getParams();
                params.overallAccountabilityList = table.cache['accountabilityTable'];
                // 添加再审计名称
                params.checkAuditName = $("#checkAuditName").val();
                // 添加整体追责主表主键
                params.accountabilityId = accountabilityId;

                // 验证再审计名称
                if (!params.checkAuditName || params.checkAuditName.trim() === '') {
                    layer.close(indexZG);
                    layer.msg('再审计名称不能为空！', {icon: 2});
                    return;
                }

                // 验证整体追责主表主键
                if (!params.accountabilityId) {
                    layer.close(indexZG);
                    layer.msg('缺少整体追责主表主键参数！', {icon: 2});
                    return;
                }

                console.log('提交参数：', {
                    checkAuditName: params.checkAuditName,
                    accountabilityId: params.accountabilityId,
                    overallAccountabilityListCount: params.overallAccountabilityList ? params.overallAccountabilityList.length : 0
                });

                $.ajax({
                    url: ctx + "/prj/overallAccountability/submitAccountabilityInfo",
                    type: 'POST',
                    dataType: "JSON",
                    data: JSON.stringify(params),
                    contentType: "application/json;charset=UTF-8",
                    success: function (res) {
                        layer.close(indexZG);
                        if (res.httpCode == 200) {
                            var msg1 = res.msg1;
                            var msg2 = res.msg2;
                            if (!msg1 && !msg2) {
                                layer.msg('提交成功！', { icon: 1, time: '1200' }, function () {
                                    // 可以在这里添加提交成功后的操作
                                });
                            } else if (msg2) {
                                frm.alertMsg(msg2, '2');
                            } else if (msg1) {
                                frm.confirm(msg1, function () {
                                    submitUnValidate(params);
                                });
                            }
                        } else {
                            frm.error(res.msg);
                        }
                    }
                });
            });
        };

        /**
         * 无校验的提交（前面的校验已通过）
         */
        window.submitUnValidate = function (params) {
            $.ajax({
                url: ctx + "/prj/overallAccountability/submitUnValidate",
                type: 'POST',
                dataType: "JSON",
                data: JSON.stringify(params),
                contentType: "application/json;charset=UTF-8",
                success: function (res) {
                    if (res.httpCode == 200) {
                        layer.msg('提交成功！', { icon: 1, time: '2000' }, function () {
                            // 可以在这里添加提交成功后的操作
                        });
                    } else {
                        frm.error(res.msg);
                    }
                }
            });
        };
        /*****************************追责项目保存和提交***********************************/

        /*****************************项目筛选***********************************/
        /**
         * 项目筛选
         */
        window.projectFilter = function () {
            // 检查按钮是否被禁用
            if ($('#projectFilterBtn').hasClass('layui-btn-disabled')) {
                layer.msg('请先完善基本信息后再进行项目筛选！', {icon: 2});
                return;
            }

            // 验证accountabilityId是否存在
            if (!accountabilityId) {
                layer.msg('缺少追责主表主键参数！', {icon: 2});
                return;
            }

            var index = layer.open({
                title: '项目筛选',
                content: 'views/audit/prj/endaccount/account-book/choose.html?accountabilityId=' + accountabilityId,
                type: 2,
                area: ['95%', '95%'],
                fixed: true,
                maxmin: true,
                resize: true,
                closeBtn: 1,
                shadeClose: false,
                btn: ['确认筛选', '取消'],
                yes: function (index, layero) {
                    // 获取子页面的筛选结果
                    var iframeWin = $(layero).find("iframe")[0].contentWindow;
                    if (iframeWin.getFilterResult) {
                        var filterResult = iframeWin.getFilterResult();
                        if (filterResult && filterResult.length > 0) {
                            // 刷新抽查项目表格
                            queryCheckLedgersTableInfo();
                            layer.close(index);
                            layer.msg("项目筛选完成，共筛选出 " + filterResult.length + " 个项目", {icon: 1});
                        } else {
                            layer.msg("请选择需要筛选的项目", {icon: 2});
                        }
                    } else {
                        layer.msg("筛选功能未就绪，请稍后重试", {icon: 2});
                    }
                },
                success: function (layero, index) {
                    // 页面加载完成后的回调
                }
            });
        };

        /**
         * 更新项目筛选按钮状态
         */
        function updateProjectFilterButtonStatus() {
            var checkMonth = $("#checkMonth").val();
            var auditPeriod = $("#auditPeriod").val();
            var $btn = $("#projectFilterBtn");

            // 需要再审计期间和审计时间区间都有值才能进行项目筛选
            if (checkMonth && auditPeriod && accountabilityId) {
                $btn.removeClass('layui-btn-disabled').removeAttr('disabled');
            } else {
                $btn.addClass('layui-btn-disabled').attr('disabled', 'disabled');
            }
        }

        /*****************************项目筛选***********************************/

        /*****************************导出抽查项目***********************************/
        /**
         * 导出抽查项目
         */
        window.exportProject = function () {
            var exportParams = "";
            var checkMonth = $("#checkMonth").val();
            var auditPeriod = $("#auditPeriod").val();
            var checkAuditName = $("#checkAuditName").val();
            var auditTeam = $("#auditTeam").val();
            var isSubCheck = $("#isSubCheck").prop("checked") ? "1" : "0";

            exportParams += "accountabilityId=" + accountabilityId;
            if (checkMonth) {
                var checkStartMonth = checkMonth.substr(0, 6);
                var checkEndMonth = checkMonth.substr(9);
                exportParams += "&checkStartMonth=" + checkStartMonth + "&checkEndMonth=" + checkEndMonth;
            }
            if (auditPeriod) {
                var auditStartMonth = auditPeriod.substr(0, 6);
                var auditEndMonth = auditPeriod.substr(9);
                exportParams += "&auditStartMonth=" + auditStartMonth + "&auditEndMonth=" + auditEndMonth;
            }
            if (checkAuditName) {
                exportParams += "&checkAuditName=" + encodeURIComponent(checkAuditName);
            }
            if (auditTeam) {
                exportParams += "&auditTeam=" + encodeURIComponent(auditTeam);
            }
            exportParams += "&isSubCheck=" + isSubCheck;

            var url = ctx + '/prj/endCheckAmount/exportCheckProjectInfo?' + exportParams;
            window.location.href = url;
            return false;
        };
        /*****************************导出抽查项目***********************************/

        /*****************************导入抽查项目***********************************/
        //导入上传组件
        upload.render({
            elem: '#uploadProjectBtn',
            url: ctx + '/prj/endCheckAmount/importCheckProjectInfo',
            accept: 'file',
            exts: 'xlsx',
            auto: true,
            data: {
                accountabilityId: accountabilityId
            },
            done: function (res, index, upload) {
                if (res.httpCode == 200) {
                    frm.alertMsg(res.data);
                    setTimeout(function () {
                        //刷新抽查项目表格
                        queryCheckLedgersTableInfo();
                    }, 1200);
                } else {
                    layer.alert(res.msg, { icon: 2, title: '提示' });
                }
            }
        });

        /**
         * 导入抽查项目
         */
        window.importProject = function () {
            frm.confirm("请确保抽查项目信息的准确性，导入后将覆盖现有数据，是否确认导入？", function () {
                $("#uploadProjectBtn").click();
            });
        };
        /*****************************导入抽查项目***********************************/

        /*****************************附件管理***********************************/
        /**
         * 查询附件列表
         */
        window.queryAttachmentTable = function () {
            $.ajax({
                url: ctx + "/prj/overallAccountabilitynew/queryAccountFileList",
                type: "POST",
                dataType: "json",
                data: JSON.stringify({
                    accountabilityId: accountabilityId
                }),
                contentType: "application/json;charset=UTF-8",
                success: function (ret) {
                    if (ret.httpCode == 200) {
                        renderAttachmentTable(ret.data || []);
                    } else {
                        frm.error(ret.msg || "查询附件列表失败");
                        renderAttachmentTable([]);
                    }
                },
                error: function (e) {
                    frm.error("网络异常，查询附件列表失败");
                    renderAttachmentTable([]);
                }
            });
        };

        /**
         * 渲染附件列表表格
         */
        window.renderAttachmentTable = function (data) {
            // 更新附件统计信息
            updateAttachmentStats(data);

            table.render({
                elem: '#attachmentTable',
                id: 'attachmentTable',
                data: data,
                page: false,
                even: true,
                cols: [[
                    { type: 'numbers', title: '序号', width: 60, align: 'center' },
                    { field: 'fileName', title: '附件名称', width: 200, align: 'left' },
                    {
                        field: 'fileType', title: '附件类型', width: 100, align: 'center', templet: function (d) {
                            return getAttachmentTypeName(d.fileType);
                        }
                    },
                    {
                        field: 'fileSize', title: '文件大小', width: 100, align: 'center', templet: function (d) {
                            return formatFileSize(d.fileSize || 0);
                        }
                    },
                    {
                        field: 'uploadDate', title: '上传时间', width: 140, align: 'center', templet: function (d) {
                            return formatDate(d.uploadDate);
                        }
                    },
                    { field: 'createUser', title: '上传人', width: 80, align: 'center' },
                    {
                        field: 'submitFlag', title: '是否提交', width: 80, align: 'center', templet: function (d) {
                            return d.submitFlag == '1' ? '是' : '否';
                        }
                    },
                    { title: '操作', width: 100, align: 'center', templet: '#attachmentBar', fixed: 'right' }
                ]],
                done: function () {
                    // 表格渲染完成后的回调
                    updateBatchDeleteAttachmentButtonStatus();
                }
            });
        };

        /**
         * 格式化文件大小
         */
        window.formatFileSize = function (bytes) {
            if (bytes === 0) return '0 Bytes';
            var k = 1024;
            var sizes = ['Bytes', 'KB', 'MB', 'GB'];
            var i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        };

        /**
         * 格式化日期
         */
        window.formatDate = function (dateStr) {
            if (!dateStr) return '';
            var date = new Date(dateStr);
            if (isNaN(date.getTime())) return dateStr;

            var year = date.getFullYear();
            var month = String(date.getMonth() + 1).padStart(2, '0');
            var day = String(date.getDate()).padStart(2, '0');
            var hours = String(date.getHours()).padStart(2, '0');
            var minutes = String(date.getMinutes()).padStart(2, '0');
            var seconds = String(date.getSeconds()).padStart(2, '0');

            return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
        };

        /**
         * 更新附件统计信息
         */
        window.updateAttachmentStats = function (data) {
            var totalCount = data ? data.length : 0;
            var totalSize = 0;

            if (data && data.length > 0) {
                data.forEach(function(item) {
                    if (item.fileSize) {
                        totalSize += parseInt(item.fileSize) || 0;
                    }
                });
            }

            // 转换为MB
            var totalSizeMB = (totalSize / (1024 * 1024)).toFixed(2);

            // 更新显示
            $("#attachmentTotalCount").text(totalCount);
            $("#attachmentTotalSize").text(totalSizeMB);
        };

        /**
         * 获取附件类型名称（从字典 accountbility_file_type 获取）
         */
        window.getAttachmentTypeName = function (type) {
            // 这里可以从字典中获取，暂时使用硬编码
            var typeMap = {
                '1': '审计报告',
                '2': '审计底稿',
                '3': '相关证明材料',
                '4': '整改报告',
                '5': '其他材料'
            };
            return typeMap[type] || '未知类型';
        };

        /**
         * 上传附件
         */
        window.uploadAttachment = function () {
            // 检查按钮是否被禁用
            var $btn = $("#uploadAttachmentBtnMain");
            if ($btn.hasClass('layui-btn-disabled')) {
                layer.msg('请先选择附件类型！', { icon: 2 });
                return;
            }

            // 验证追责主键
            if (!accountabilityId) {
                layer.msg('缺少追责主表主键参数！', { icon: 2 });
                return;
            }

            // 验证附件类型
            var attachmentType = $("#attachmentType").val();
            if (!attachmentType) {
                layer.msg("请先选择附件类型！", { icon: 2 });
                return;
            }

            // 触发文件选择
            $("#uploadAttachmentBtn").click();
        };

        //附件上传组件
        upload.render({
            elem: '#uploadAttachmentBtn',
            url: ctx + '/prj/accountabilityfile/uploadAccountabilityFile',
            accept: 'file',
            exts: 'doc|docx|pdf|xls|xlsx|ppt|pptx|txt|jpg|jpeg|png|gif|zip|rar|7z',
            size: 50 * 1024, // 限制文件大小为50MB
            auto: true,
            data: function () {
                return {
                    businessKey: accountabilityId,
                    fileType: $("#attachmentType").val()
                };
            },
            choose: function (obj) {
                // 文件选择后的回调
                var files = obj.pushFile();

                // 遍历选择的文件
                obj.preview(function (index, file, result) {
                    var fileName = file.name;
                    var fileSize = file.size;
                    var fileType = $("#attachmentType").val();

                    // 验证文件大小（50MB）
                    if (fileSize > 50 * 1024 * 1024) {
                        layer.msg('文件大小不能超过50MB！', { icon: 2 });
                        delete files[index];
                        return;
                    }

                    // 确认上传
                    layer.confirm('确定要上传文件"' + fileName + '"吗？<br><span style="color: #666;">文件大小：' + (fileSize / 1024 / 1024).toFixed(2) + ' MB</span><br><span style="color: #666;">附件类型：' + getAttachmentTypeName(fileType) + '</span>', {
                        icon: 3,
                        title: '确认上传',
                        area: ['400px', '200px']
                    }, function (confirmIndex) {
                        // 确认上传，显示进度
                        var loadingIndex = layer.msg('正在上传文件，请稍候...', {
                            icon: 16,
                            shade: 0.3,
                            time: 0 // 不自动关闭
                        });
                        layer.close(confirmIndex);
                    }, function () {
                        // 取消上传
                        delete files[index];
                    });
                });
            },
            before: function (obj) {
                // 上传前的回调
                var fileType = $("#attachmentType").val();
                if (!fileType) {
                    layer.msg('请先选择附件类型！', { icon: 2 });
                    return false;
                }
            },
            done: function (res, index, upload) {
                // 关闭加载层
                layer.closeAll('loading');
                layer.closeAll();

                if (res.httpCode == 200) {
                    var successMsg = "附件上传成功！";
                    if (res.data) {
                        if (res.data.fileName) {
                            successMsg += "<br>文件名：" + res.data.fileName;
                        }
                        if (res.data.fileSize) {
                            successMsg += "<br>文件大小：" + (res.data.fileSize / 1024 / 1024).toFixed(2) + " MB";
                        }
                    }

                    layer.alert(successMsg, {
                        icon: 1,
                        title: '上传成功',
                        area: ['400px', 'auto']
                    });

                    // 清空附件类型选择
                    $("#attachmentType").val("");
                    // 更新按钮状态
                    updateUploadAttachmentButtonStatus();
                    // 刷新附件列表
                    queryAttachmentTable();
                } else {
                    layer.alert(res.msg || '附件上传失败！', {
                        icon: 2,
                        title: '上传失败',
                        area: ['400px', 'auto']
                    });
                }
            },
            error: function (index, upload) {
                // 关闭加载层
                layer.closeAll('loading');
                layer.msg('网络异常，附件上传失败！', { icon: 2 });
            }
        });

        /**
         * 下载附件
         */
        window.downloadAttachment = function (attachmentId, fileName) {
            if (!attachmentId) {
                layer.msg('缺少附件ID参数！', { icon: 2 });
                return;
            }

            // 显示下载确认
            var confirmMsg = '确定要下载该附件吗？';
            if (fileName) {
                confirmMsg = '确定要下载附件"' + fileName + '"吗？';
            }

            layer.confirm(confirmMsg, {
                icon: 3,
                title: '确认下载',
                area: ['350px', '150px']
            }, function (index) {
                // 显示下载提示
                var loadingIndex = layer.msg('正在准备下载，请稍候...', {
                    icon: 16,
                    shade: 0.3,
                    time: 0
                });

                // 使用新的下载接口
                var downloadUrl = ctx + '/files/downLoad/' + attachmentId;

                // 先检查文件是否存在
                $.ajax({
                    url: downloadUrl,
                    type: 'HEAD',
                    success: function () {
                        // 文件存在，开始下载
                        var link = document.createElement('a');
                        link.href = downloadUrl;
                        link.download = fileName || '附件文件';
                        link.style.display = 'none';

                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);

                        layer.close(loadingIndex);
                        layer.msg('下载已开始！', { icon: 1 });
                    },
                    error: function (xhr) {
                        layer.close(loadingIndex);

                        if (xhr.status === 404) {
                            layer.msg('文件不存在或已被删除！', { icon: 2 });
                        } else {
                            layer.msg('下载失败，请稍后重试！', { icon: 2 });
                        }
                    }
                });

                // 关闭确认框
                layer.close(index);
            });
        };

        /**
         * 删除附件
         */
        window.deleteAttachment = function (attachmentId, fileName) {
            if (!attachmentId) {
                layer.msg('缺少附件ID参数！', { icon: 2 });
                return;
            }

            var confirmMsg = '确定要删除该附件吗？';
            if (fileName) {
                confirmMsg = '确定要删除附件"' + fileName + '"吗？';
            }

            layer.confirm(confirmMsg + '<br><span style="color: #ff5722;">注意：删除后无法恢复！</span>', {
                icon: 3,
                title: '确认删除',
                area: ['400px', '180px']
            }, function (index) {
                // 显示删除进度
                var loadingIndex = layer.msg('正在删除，请稍候...', {
                    icon: 16,
                    shade: 0.3,
                    time: 0
                });

                $.ajax({
                    url: ctx + "/prj/accountabilityfile/deleteAccountabilityFile/" + attachmentId,
                    type: "DELETE",
                    dataType: "json",
                    success: function (ret) {
                        layer.close(loadingIndex);

                        if (ret.httpCode == 200) {
                            layer.msg("附件删除成功！", { icon: 1 });
                            // 刷新附件列表
                            queryAttachmentTable();
                        } else {
                            layer.msg(ret.msg || '删除失败！', { icon: 2 });
                        }
                    },
                    error: function (xhr, status, error) {
                        layer.close(loadingIndex);

                        var errorMsg = '网络异常，删除失败！';
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (response && response.msg) {
                                errorMsg = response.msg;
                            }
                        } catch (e) {
                            // 解析失败，使用默认错误信息
                        }

                        layer.msg(errorMsg, { icon: 2 });
                    }
                });

                // 关闭确认框
                layer.close(index);
            });
        };

        /**
         * 批量删除附件
         */
        window.batchDeleteAttachments = function () {
            // 获取选中的行数据
            var checkStatus = table.checkStatus('attachmentTable');
            var data = checkStatus.data;

            if (data.length === 0) {
                layer.msg('请至少选择一个附件！', { icon: 2 });
                return;
            }

            // 获取选中的附件ID列表
            var attachmentIds = [];
            var fileNames = [];
            $.each(data, function (index, item) {
                if (item.attachmentId) {
                    attachmentIds.push(item.attachmentId);
                    fileNames.push(item.fileName || '未知文件');
                }
            });

            if (attachmentIds.length === 0) {
                layer.msg('获取选中附件ID失败！', { icon: 2 });
                return;
            }

            var confirmMsg = '确定要删除选中的 ' + data.length + ' 个附件吗？';
            if (fileNames.length > 0) {
                confirmMsg += '<br><span style="color: #666;">包括：' + fileNames.slice(0, 3).join('、');
                if (fileNames.length > 3) {
                    confirmMsg += ' 等';
                }
                confirmMsg += '</span>';
            }
            confirmMsg += '<br><span style="color: #ff5722;">注意：删除后无法恢复！</span>';

            layer.confirm(confirmMsg, {
                icon: 3,
                title: '确认批量删除',
                area: ['450px', '220px']
            }, function (index) {
                // 显示删除进度
                var loadingIndex = layer.msg('正在批量删除，请稍候...', {
                    icon: 16,
                    shade: 0.3,
                    time: 0
                });

                // 批量删除（逐个调用删除接口）
                var deletePromises = [];
                $.each(attachmentIds, function (i, attachmentId) {
                    var promise = $.ajax({
                        url: ctx + "/prj/accountabilityfile/deleteAccountabilityFile/" + attachmentId,
                        type: "DELETE",
                        dataType: "json"
                    });
                    deletePromises.push(promise);
                });

                // 等待所有删除操作完成
                $.when.apply($, deletePromises).done(function () {
                    layer.close(loadingIndex);
                    layer.msg('批量删除成功！', { icon: 1 });
                    // 刷新附件列表
                    queryAttachmentTable();
                }).fail(function () {
                    layer.close(loadingIndex);
                    layer.msg('部分附件删除失败，请检查后重试！', { icon: 2 });
                    // 刷新附件列表
                    queryAttachmentTable();
                });

                // 关闭确认框
                layer.close(index);
            });
        };

        /**
         * 更新批量删除附件按钮状态
         */
        function updateBatchDeleteAttachmentButtonStatus() {
            // 监听复选框变化
            table.on('checkbox(attachmentTable)', function (obj) {
                var checkStatus = table.checkStatus('attachmentTable');
                var data = checkStatus.data;

                if (data.length > 0) {
                    $('#batchDeleteAttachmentBtn').removeClass('layui-btn-disabled').removeAttr('disabled');
                } else {
                    $('#batchDeleteAttachmentBtn').addClass('layui-btn-disabled').attr('disabled', 'disabled');
                }
            });

            // 初始状态设置按钮为禁用
            $('#batchDeleteAttachmentBtn').addClass('layui-btn-disabled').attr('disabled', 'disabled');
        }

        /**
         * 更新上传附件按钮状态
         */
        function updateUploadAttachmentButtonStatus() {
            var attachmentType = $("#attachmentType").val();
            var $btn = $("#uploadAttachmentBtnMain");

            if (attachmentType && accountabilityId) {
                $btn.removeClass('layui-btn-disabled').removeAttr('disabled');
            } else {
                $btn.addClass('layui-btn-disabled').attr('disabled', 'disabled');
            }
        }

        /**
         * 根据追责状态控制字段的可编辑性
         */
        function controlFieldsByStatus() {
            console.log('当前追责状态:', accountabilityStatus);

            if (accountabilityStatus == '2') {
                // 追责中状态：显示整改状态字段，基本信息字段不可编辑
                $("#reformStatusDiv").show();

                // 设置基本信息字段为只读
                $("#checkAuditName").attr('readonly', true).addClass('readonly-field');
                $("#auditPeriod").attr('readonly', true).addClass('readonly-field');
                $("#isSubCheck").attr('disabled', true);
                $("#auditTeam").attr('readonly', true).addClass('readonly-field');

                // 隐藏选择按钮
                $("button[onclick='queryUser()']").hide();

                // 修改页面标题
                $(".form-bg-box").prepend('<div class="status-notice" style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin-bottom: 15px; border-radius: 4px; color: #856404;"><i class="iconfont" style="color: #f39c12;">&#xe63a;</i> 当前状态：追责中，基本信息不可修改，只能更新整改状态</div>');

            } else if (accountabilityStatus == '0' || !accountabilityStatus) {
                // 草稿状态或新增：隐藏整改状态字段，所有字段可编辑
                $("#reformStatusDiv").hide();

                // 确保基本信息字段可编辑（除了本来就只读的字段）
                $("#checkAuditName").removeAttr('readonly').removeClass('readonly-field');
                $("#auditTeam").removeAttr('readonly').removeClass('readonly-field');
                $("#isSubCheck").removeAttr('disabled');

                // 显示选择按钮
                $("button[onclick='queryUser()']").show();
            }
        }

        // 监听附件类型选择变化
        $(document).ready(function () {
            $("#attachmentType").on('change', function () {
                updateUploadAttachmentButtonStatus();
            });

            // 根据追责状态控制字段可编辑性
            controlFieldsByStatus();

            // 初始化按钮状态
            setTimeout(function () {
                updateUploadAttachmentButtonStatus();
                updateBatchDeleteAttachmentButtonStatus();
            }, 100);
        });

        /*****************************附件管理***********************************/

        /*****************************项目台账***********************************/

        /*****************************项目台账***********************************/

        // 存储组织方式字典数据
        var auditTypeDictionary = {};

        // 存储整改状态字典数据
        var reformStatusDictionary = {};

        // 加载组织方式字典数据
        window.loadAuditTypeDictionary = function () {
            $.ajax({
                url: ctx + "/index/querySysDicByType",
                dataType: "JSON",
                type: "POST",
                data: {
                    type: "auditType"
                },
                success: function (ret) {
                    if (ret && ret.data) {
                        // 将字典数据存储到全局变量中，用于回显
                        $.each(ret.data, function (index, item) {
                            auditTypeDictionary[item.code] = item.codeText;
                        });
                    }
                },
                error: function (e) {
                    console.info("加载组织方式字典失败:", e);
                }
            });
        };

        /*****************************查看模式：不需要整改状态字典加载***********************************/

        // 根据auditType值设置组织方式显示名称
        window.setAuditTypeName = function (auditTypeValue) {
            if (auditTypeValue && auditTypeDictionary[auditTypeValue]) {
                $("#auditType").val(auditTypeValue);
                $("#auditTypeName").val(auditTypeDictionary[auditTypeValue]);
            }
        };

        //查询初始化数据
        queryAccountabilityInfo();
        //加载附件列表
        queryAttachmentTable();
        //加载组织方式字典（用于回显）
        loadAuditTypeDictionary();
        // 注意：抽查项目表格和项目台账的加载由queryAccountabilityInfo中控制

        /*****************************查看模式：移除复选框变化监听***********************************/

        // 初始化按钮状态
        setTimeout(function () {
            updateGenerateAccountabilityButtonStatus();
            updateProjectFilterButtonStatus();
        }, 100);

        // 添加必填字段实时验证
        addRequiredFieldValidation();

        // 监听日期字段变化（由于使用了laydate，需要在change回调中更新状态）
        // 这些监听器已经在laydate.render的change回调中处理
    });



</script>

</html>
