<!--ICT诊断仪 项目详情-->
<!--#include virtual ="include/header.html"-->
<link href="resource/css/audit/online/tagmodel/tagIndex.css?v=6.5" rel="stylesheet" type="text/css">
<link href="resource/css/audit/online/tagmodel/tagCommon.css?v=6.5" rel="stylesheet" type="text/css">
<link href="resource/css/number-risk-diagnosis/index.css?v=6.5" rel="stylesheet" type="text/css"/>
<style>

    .new-table .layui-table-view .layui-table td, .new-table .layui-table-view .layui-table th {
        border-left: 1px solid #ddd;
        border-right: 1px solid #ddd;
    }

    .layui-common-body {
        background: #fff;
    }

    .layui-form-item {
        margin-bottom: 10px;
    }

    .tab_2 .new-style .layui-input-block {
        margin-left: 177px;
    }

    .tab_2 .new-style .layui-form-label {
        width: 175px;
    }

    .new-style .layui-input-block {
        margin-left: 112px;
    }

    .new-style .layui-form-label {
        width: 110px;
    }

    .layui-search-new.new-style .layui-form-label {
        padding: 0 6px 0 0;
    }

    .ict-header-list {
        display: inline-block;
        width: 100%;
        text-align: left;
    }

    .ict-header-li {
        padding: 0 26px;
        box-sizing: border-box;
        display: inline-block;
        cursor: pointer;
    }

    .ict-header-title {
        font-family: PingFangSC, PingFang SC;
        font-size: 15px;
        color: #333333;
        font-style: normal;
        border-bottom: 3px solid #fff;
        height: 42px;
    }

    .ict-header-li.active .ict-header-title {
        color: #C20000;
        border-bottom: 3px solid #C20000;
    }

    .ict-search-box {
        width: 100%;
        margin-top: 8px;
        background: #F7F9FA;
        padding: 10px 10px 0 10px;
        box-sizing: border-box;
    }

    .ict-search-form {
        width: 100%;
        float: left;
    }

    .ict-search-btn {
        width: 100px;
        float: right;
    }

    .model-btn-submit {
        margin-bottom: 10px;
    }

    .search-input input {
        height: 42px;
        width: 450px;
    }

    .search-input {
        margin-top: 15px;
    }

    .search-input .search-btn-right {
        width: 100px;
        height: 42px;
        border-radius: 0 4px 4px 0;
        background: #C20000;
    }

    .model-btn.model-btn-submit {
        background-color: #C20000;
        border: 1px solid #C20000;
    }

    .search-input .search-btn-right.search-btn-right-2 {
        margin-left: 20px;
        width: 120px;
        border-radius: 4px;
    }

    .search-input .search-btn-right.search-btn-right-2 .search-btn-right-text {
        padding: 0;
    }

    .new-style .layui-input-block .input-p {
        padding: 4px 0;
        display: inline-block;
        box-sizing: border-box;
    }

    .formButton {
        text-align: right;
    }

    .formButton button {
        height: 32px;
        line-height: 32px;
    }

    .layui-input[disabled="disabled"] {
        background: #eee !important;
    }

    .layui-laydate .laydate-btns-clear {
        display: inline-block;
    }

    .goClick {
        color: #C20000;
        cursor: pointer;
    }

    .dw-tip {
        text-align: right;
        color: #333333;
    }

    .model-btn.model-btn-export {
        min-width: 88px;
        padding: 0 12px;
        box-sizing: border-box;
        background-color: #ffffff;
        border: 1px solid #c20000;
        border-radius: 2px;
    }

    .model-btn.model-btn-export span {
        color: #c20000;
    }

    .model-btn.model-btn-export i {
        color: #c20000;
    }

    .dw-text {
        text-align: right;
        font-weight: 600;
        font-size: 16px;
        padding-top: 10px;
    }

</style>

<body>

<div class="layui-common-body layui-row">

    <div class="tab-menu">
        <a data-parent="true" href="javascript:" id="tableMenu" style="color: #c00;text-decoration:underline;">
            <i class="iconfont" data-icon=""></i>
        </a>
    </div>

    <div class="layui-row layui-col-md12 layui-col-sm12 layui-col-lg12">
        <div class="layui-row">
            <div class="layui-common-box">
                <div class="layui-row layui-common-card">
                    <div class="layui-card-body main-list-body layui-row" style="padding-top: 4px;min-height: 740px;">
                        <div class="ict-search-box layui-row">
                            <div class="ict-search-form layui-form new-style layui-row">
                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="layui-form-item layui-form-item-sm">
                                        <label class="layui-form-label color">供应商名称</label>
                                        <div class="layui-input-block">
                                            <input class="layui-input" id="comName" type="text">
                                        </div>
                                    </div>
                                </div>
                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="layui-form-item layui-form-item-sm">
                                        <label class="layui-form-label color">项目编号</label>
                                        <div class="layui-input-block">
                                            <input class="layui-input" id="projectNumber" type="text">
                                        </div>
                                    </div>
                                </div>
                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="layui-form-item layui-form-item-sm">
                                        <label class="layui-form-label color">项目名称</label>
                                        <div class="layui-input-block">
                                            <input class="layui-input" id="projectName" type="text">
                                        </div>
                                    </div>
                                </div>
                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="layui-form-item layui-form-item-sm">
                                        <label class="layui-form-label color">合同名称</label>
                                        <div class="layui-input-block">
                                            <input class="layui-input" id="contractName" type="text">
                                        </div>
                                    </div>
                                </div>

                                <div class="float-left layui-col-md12 layui-col-sm12 layui-col-lg12"
                                     style="text-align: right;">
                                    <div class="model-btn model-btn-submit" onclick="reloadForm()" title="查询">
                                        <i class="iconfont search-icon">&#xe60b;</i>
                                        <span>查询</span>
                                    </div>
                                    <div class="model-btn model-btn-reset" onclick="restFun()" title="重置">
                                        <i class="iconfont search-icon">&#xe63a;</i>
                                        <span>重置</span>
                                    </div>

                                    <div class="model-btn model-btn-export" onclick="exportBtn()" title="导出">
                                        <i class="iconfont search-icon">&#xe60c;</i>
                                        <span>导出</span>
                                    </div>


                                </div>

                            </div>
                        </div>


                        <div class="tab_1 tab_box">
                            <div class="layui-card-body main-list-body">
                                <div class="layui-form new-table">
                                    <table class="layui-table jq-even" id="risk_profile_table"
                                           lay-filter="risk_profile_table"></table>
                                </div>
                            </div>
                        </div>


                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--#include virtual ="include/version.html"-->
<script src="resource/js/jquery/jquery.js" type="text/javascript"></script>
<script id="toolbar" type="text/html">
    <a class="table-btn" lay-event="detail" title="详情">
        <i class="iconfont">&#xe651;</i>
    </a>
</script>
<script>
    var comName = decodeURI(getUrlParam("comName"));
    var page = 1;
    var limit = 10;
    layui.use(['jqform', 'table', 'laytpl', 'jquery', 'laydate', 'layer', 'jqbind', 'jqfrm'], function () {

        var $ = layui.jquery,
            ctx = top.global.ctx,
            layer = layui.layer,
            laydate = layui.laydate,
            form = layui.jqform,
            frm = layui.jqfrm,
            tpl = layui.laytpl,
            table = layui.table,
            jqbind = layui.jqbind;
        $('#comName').val(comName)
        // 重置
        window.restFun = function () {
            $("#comName").val("");
            $("#projectNumber").val("");
            $("#projectName").val("");
            $("#contractName").val("");

            form.render();
            riskProfileTable()
        };

        //reloadForm 查询
        window.reloadForm = function (type) {
            riskProfileTable()
        }


        // 项目详情
        window.riskProfileTable = function () {

            var colZ = [
                {
                    field: 'projectNumber', title: 'ERP项目编号', align: 'center', width: '150'
                },
                {
                    field: 'projectName', title: 'ERP项目名称', align: 'center', width: '200', style: 'text-align: left;'
                },
                {
                    field: 'classifyName', title: '合同分类名称', align: 'center', width: '260', style: 'text-align: left;'
                },
                {
                    field: 'proName', title: '省份名称', align: 'center', width: '80'
                },
                {
                    field: 'comName', title: '公司名称', align: 'center', width: '180', style: 'text-align: left;'
                },
                {
                    field: 'deptName', title: '部门名称', align: 'center', width: '150', style: 'text-align: left;'
                },
                {
                    field: 'contractGlobalsn', title: '合同编号', align: 'center', width: '190'
                },
                {
                    field: 'contractName', title: '合同名称', align: 'center', width: '300', style: 'text-align: left;'
                },
                {
                    field: 'weSignName', title: '我方主体名称', align: 'center', width: '300', style: 'text-align: left;'
                },
                {
                    field: 'otherSignName', title: '对方主体名称', align: 'center', width: '200', style: 'text-align: left;'
                },
                {
                    field: 'businessRegistrationPlace', title: '企业工商注册地', align: 'center', width: '200', style: 'text-align: left;'
                },
                {
                    field: 'rmbMoney', title: '含增值税合同本位币金额', align: 'center', width: '185', style: 'text-align: right;', templet: function (d) { return thousands(d.rmbMoney); }
                },
                {
                    field: 'rmbunvatamount', title: '不含增值税合同本位币金额', align: 'center', width: '198', style: 'text-align: right;', templet: function (d) { return thousands(d.rmbunvatamount); }
                },
                {
                    field: 'vatrate', title: '增值税税率', align: 'center', width: '100'
                },
                {
                    field: 'arrairStatusName', title: '业务状态名称', align: 'center', width: '110'
                },
                {
                    field: 'contractStatusName', title: '合同状态名称', align: 'center', width: '110'
                },
                {
                    field: 'sendApprovalDate', title: '送审日期', align: 'center', width: '150'
                },
                {
                    field: 'approveEndTime', title: '送审流程结束日期', align: 'center', width: '150'
                },
                {
                    field: 'finalSignTime', title: '最终签章日期', align: 'center', width: '150'
                },
                {
                    field: 'precondition', title: '合同启动依据', align: 'center', width: '150', style: 'text-align: left;'
                },
                {
                    field: 'lastApproverName', title: '最终审批人', align: 'center', width: '95'
                },
                {
                    field: 'selectModelName', title: '对方选择方式名称', align: 'center', width: '150'
                },
                {
                    field: 'changeTypeName', title: '合同性质名称', align: 'center', width: '110'
                },
                {
                    field: 'performTimeLimitName', title: '合同履行期限名称', align: 'center', width: '135'
                },
                {
                    field: 'takerName', title: '承办人名称', align: 'center', width: '95'
                },
                {
                    field: 'mobilePhone', title: '承办人电话', align: 'center', width: '118'
                },
                {
                    field: 'moneyTypeName', title: '合同币种名称', align: 'center', width: '110'
                },
                {
                    field: 'exchangeRate', title: '汇率', align: 'center', width: '70'
                },
                {
                    field: 'rn', title: '含增值税合同金额', align: 'center', width: '135', style: 'text-align: right;', templet: function (d) { return thousands(d.rn); }
                },
                {toolbar: '#toolbar', width: 80, title: "操作", align: 'center', fixed: 'right'}
            ];

            table.render({
                elem: '#risk_profile_table',
                id: '#risk_profile_table',
                url: ctx + '/contract-array/getContractPage',
                where: {
                    comName: $('#comName').val(),
                    projectNumber: $('#projectNumber').val(),
                    projectName: $('#projectName').val(),
                    contractName: $('#contractName').val()
                },
                page: true,
                limit: 10,
                cols: [colZ],
                done: function (res, curr, count) {
                    page = curr;
                    limit = res.limit;
                }
            });
        }
        table.on('tool(risk_profile_table)', function(obj){
            var data = obj.data;
            if (obj.event === 'detail') {
                layer.open({
                    title: '附件列表',
                    content:
                        "/jtaudithtml/views/audit/bigdata/ictDiagnosticEquipment/supplierFileList.html?contractGlobalsn="+data.contractGlobalsn,
                    type: 2,
                    area: ["70%", "80%"],
                    fixed: true
                });
            }
        });

        /**
         * 导出
         */
        window.exportBtn = function () {
            var comName1 = encodeURI($('#comName').val());
            var projectNumber = encodeURI($('#projectNumber').val());
            var projectName = encodeURI($('#projectName').val());
            var contractName = encodeURI($('#contractName').val());

            var url = ctx + "/contract-array/exportContractArray?comName=" + comName1 + '&projectNumber=' + projectNumber +
                '&projectName=' +projectName + '&contractName=' +contractName+ '&page=' +page+ '&limit=' +limit;
            window.location.href = url;
            return false;
        };

        riskProfileTable()
    })

</script>

</body>
