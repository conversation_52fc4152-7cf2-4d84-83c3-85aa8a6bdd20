<!--整体追责列表-->
<!--#include virtual ="include/header.html"-->
<body>
    <style>
        /* 表格单元格基础样式 */

        /* 表格数据为空时的样式 */
        .layui-table tbody .layui-none {
            background-color: #fafafa !important;
            color: #999 !important;
            text-align: center !important;
            padding: 40px 0 !important;
        }
    </style>
    <div class="layui-fluid larry-wrapper">
        <section class="panel panel-padding">
            <form class="layui-form layui-form-pane form-conmon form-conmon-more" data-params='{bind:true }'
                  id="accountabilityForm">
                <div class="layui-row layui-col-space10 transition-500ms search-condition">
                    <div class="layui-col-md3 layui-col-sm3">
                        <div class="layui-form-item layui-form-item-sm">
                            <label class="layui-form-label">立项单位</label>
                            <div class="layui-input-block">
                                <select id="sendProvCode" lay-filter="sendProvCode" lay-search="" name="sendProvCode"></select>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md3 layui-col-sm3">
                        <div class="layui-form-item layui-form-item-sm">
                            <label class="layui-form-label">再审计名称</label>
                            <div class="layui-input-block">
                                <input autocomplete="off" class="layui-input" id="checkAuditName" name="checkAuditName" placeholder="再审计名称" type="text"/>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md3 layui-col-sm3">
                        <div class="layui-form-item layui-form-item-sm">
                            <label class="layui-form-label">再审计期间</label>
                            <div class="layui-input-block">
                                <input class="layui-input" id="checkMonth" name="checkMonth" placeholder="再审计期间" readonly="readonly">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md3 layui-col-sm3">
                        <div class="layui-form-item layui-form-item-sm">
                            <label class="layui-form-label">整体追责状态</label>
                            <div class="layui-input-block">
                                <select class="layui-select" id="accountabilityStatus" lay-filter="accountabilityStatus" name="accountabilityStatus">
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md3 layui-col-sm3">
                        <div class="layui-form-item layui-form-item-sm">
                            <label class="layui-form-label">整改状态</label>
                            <div class="layui-input-block">
                                <select class="layui-select" id="rectifyStatus" name="rectifyStatus">
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="search-btn" style="position:absolute;right:0px;top:0px;">
                    <div class="layui-form-item" style="display:inline-block;">
                        <a class="layui-btn layui-btn-sm" href="javascript:" onclick="reloadTable()">
                            <i class="iconfont search-icon">&#xe60b;</i> 查询</a>
                    </div>
                    <div class="layui-form-item" style="display:inline-block;">
                        <button class="layui-btn btn-outline layui-btn-normal layui-btn-sm btn" type="reset">
                            <i class="iconfont search-icon">&#xe63a;</i> 重置
                        </button>
                    </div>
                </div>
            </form>
            <div class="layui-form" style="margin-top:10px">
                <div style="float: right">
                    <!-- style = "display: none;" -->
                    <a class="layui-btn btn-outline layui-btn-normal layui-btn-sm"  id = "fillBtn" onclick="fillingBtn('', '0')">
                        <i class="iconfont search-icon">&#xe635;</i> 填报整体追责
                    </a>
                    <a class="layui-btn btn-outline layui-btn-normal layui-btn-sm" onclick="exportBtn()">
                        <i class="iconfont search-icon">&#xe60c;</i> 导出
                    </a>
                </div>

                <table class="layui-table jq-even" id="accountabilityTable" lay-filter="accountabilityTable"></table>
            </div>
        </section>
    </div>
    </body>
    <!--#include virtual ="include/version.html"-->
    <!--列表查看-->
    <script id="configBar" type="text/html">
        <!-- 编辑按钮：非已完成状态且有权限时显示 -->
        {{# if(d.accountabilityStatus != '1' && (window.globalShowTbBtn == '1' || window.globalShowTbBtn == 1)){ }}
        <a class="table-btn" type="button" title="编辑" onclick="fillingBtn('{{d.accountabilityId}}', '{{d.accountabilityStatus}}')">
            <i class="iconfont" style="color: #c20000;">&#xe62e;</i></a>
        {{# } }}

        <!-- 删除按钮：只有草稿状态且有权限时显示 -->
        {{# if(d.accountabilityStatus == '0' && (window.globalShowTbBtn == '1' || window.globalShowTbBtn == 1)){ }}
        <a class="table-btn" type="button" title="删除" onclick="delDetailBen('{{d.accountabilityId}}','{{d.id}}')">
            <i class="iconfont" style="color: #c20000;">&#xe6e1;</i></a>
        {{# } }}
        <!-- 查看按钮：始终显示 -->
        <a class="table-btn" type="button" title="查看" onclick="viewBtn('{{d.accountabilityId}}')">
            <i class="iconfont" style="color: #c20000;">&#xe6b6;</i></a>
    </script>
    <script id="select-tpl-prov" type="text/html">
        {{# if(d.length>1){ }}
        <option value="">--请选择--</option>
        {{# } }}
        {{# layui.each(d, function(index, item){ }}
        <option value="{{item.provCode}}" > {{item.provName}}</option>
        {{# }); }}
    </script>

    <!-- 整体追责状态模板 -->
    <script id="select-tpl-accountability-status" type="text/html">
        <option value="">--请选择--</option>
        {{# layui.each(d, function(index, item){ }}
        <option value="{{item.statusCode}}" > {{item.statusName}}</option>
        {{# }); }}
    </script>

    <!-- 整改状态模板 -->
    <script id="select-tpl-reform-status" type="text/html">
        <option value="">--请选择--</option>
        {{# layui.each(d, function(index, item){ }}
        <option value="{{item.statusCode}}" > {{item.statusName}}</option>
        {{# }); }}
    </script>
    <script type="text/javascript">
        layui.use(['jqdate', 'jqform', 'jqfrm', 'jqbind', 'table', 'layer', 'jquery'], function () {
                var $ = layui.jquery;
                var frm = layui.jqfrm;
                var form = layui.jqform;
                var jqbind = layui.jqbind;
                var layer = layui.layer;
                var table = layui.table;
                var ctx = top.global.ctx;
                var laydate = layui.laydate;
                var tpl = layui.laytpl;
                var element = layui.element;
            /*****************************查询表格数据***********************************/
            //获取查询参数
            window.getParams = function(){
                var params = {};

                // 立项单位
                var sendProvCode = $("#sendProvCode").val();
                if(sendProvCode) {
                    params.sendProvCode = sendProvCode;
                    // 获取选中的省份名称
                    params.sendProvName = $("#sendProvCode option:selected").text();
                }

                // 再审计名称
                var checkAuditName = $("#checkAuditName").val();
                if(checkAuditName) {
                    params.checkAuditName = checkAuditName;
                }

                // 再审计期间
                var checkMonth = $("#checkMonth").val();
                if(checkMonth){
                    params.checkStartMonth = checkMonth.substr(0,6);
                    params.checkEndMonth = checkMonth.substr(9);
                }

                // 整体追责状态
                var accountabilityStatus = $("#accountabilityStatus").val();
                if(accountabilityStatus) {
                    params.accountabilityStatus = accountabilityStatus;
                }

                // 整改状态
                var rectifyStatus = $("#rectifyStatus").val();
                if(rectifyStatus) {
                    params.reformStatus = rectifyStatus;
                }

                return params;
            }
            //查询数据
            window.queryOverallAccountabilityTableInfo = function() {
                table.render({
                    elem: '#accountabilityTable',
                    id: 'accountabilityTable',
                    url: ctx + '/prj/overallAccountabilitynew/queryAccountabilityList',
                    where: getParams(),
                    page: true,
                    even: true,
                    limits: [10, 30, 60, 120, 300],
                    limit: 10,
                    cols: [[
                        {type: 'numbers', title: '序号', width: '8%', align: 'center'},
                        {field: 'sendProvName', title: '立项单位', width: '14%', align: 'center'},
                        {field: 'checkAuditName', title: '再审计名称', width: '30%', align: 'left'},
                        {field: 'checkStartEndMonth', title: '再审计期间', width: '14%', align: 'center'},
                        {field:'accountabilityStatusName',title: '整体追责状态', width: '12%', align: 'center'},
                        {field:'reformStatusName',title: '整改状态', width: '12%', align: 'center'},
                        {title: '操作',width: '10%',align: 'center',templet: '#configBar',fixed: 'right'}
                    ]],
                })
            }
            /*****************************查询表格数据***********************************/
            /*********************初始化查询参数***************************/
            window.initParamsList = function(){
                $.ajax({
                    url:ctx+'/prj/overallAccountability/initParamsList'
                    ,type:'POST'
                    ,dataType:'JSON'
                    ,contentType:'application/json;charset=UTF-8'
                    ,success:function(res){
                        if(res.httpCode == 200 && res.data) {
                            // 处理立项单位下拉列表
                            if(res.data.sendProvList && res.data.sendProvList.length > 0) {
                                var getTpl1 = $('#select-tpl-prov').html();
                                tpl(getTpl1).render(res.data.sendProvList, function (html) {
                                    $("#sendProvCode").html(html);
                                });
                            }

                            // 处理整体追责状态下拉列表使用默认数据
                            var defaultAccountabilityStatus = [
                                {statusCode: '0', statusName: '草稿中'},
                                {statusCode: '2', statusName: '追责中'},
                                {statusCode: '1', statusName: '已完成'}
                            ];
                            var getTpl2 = $('#select-tpl-accountability-status').html();
                            tpl(getTpl2).render(defaultAccountabilityStatus, function (html) {
                                $("#accountabilityStatus").html(html);
                            });

                            // 处理整改状态下拉列表使用默认数据
                            var defaultReformStatus = [
                                {statusCode: '0', statusName: '未整改'},
                                {statusCode: '1', statusName: '整改中'},
                                {statusCode: '2', statusName: '已完成'}
                            ];
                            var getTpl3 = $('#select-tpl-reform-status').html();
                            tpl(getTpl3).render(defaultReformStatus, function (html) {
                                $("#rectifyStatus").html(html);
                            });

                            // 根据showTbBtn字段控制填报整体追责按钮显示
                            var showTbBtn = res.data.showTbBtn;
                            // 保存到全局变量，供表格使用
                            window.globalShowTbBtn = showTbBtn;

                            if(showTbBtn == '1' || showTbBtn == 1) {
                                $("#fillBtn").show();
                            } else {
                              //  $("#fillBtn").hide();
                            }

                            form.render();

                            //查询数据列表
                            queryOverallAccountabilityTableInfo();
                        } else {
                            frm.error(res.msg || "初始化参数失败");
                        }
                    },
                    error: function() {
                        frm.error("网络异常，初始化参数失败");
                    }
                })
            }
            //执行一个laydate实例
            laydate.render({
                elem: '#checkMonth', //指定元素
                type : 'month',
                range: true,
                format: 'yyyyMM',
                isInitValue: false,
                showBottom:true,
                change: function(value, date, endDate){
                    $("#checkMonth").val(value);

                },
            });
            //查询
            window.reloadTable = function(){
                //查询数据列表
                queryOverallAccountabilityTableInfo();
            }

            initParamsList();
            /*********************初始化查询参数***************************/




            /*****************************导出再审计台账***********************************/
            /**
             * 导出
             */
            window.exportBtn = function () {
                var params = getParams();
                var exportParams = [];

                // 构建导出参数
                if(params.sendProvCode) {
                    exportParams.push('sendProvCode=' + encodeURIComponent(params.sendProvCode));
                }
                if(params.sendProvName) {
                    exportParams.push('sendProvName=' + encodeURIComponent(params.sendProvName));
                }
                if(params.checkAuditName) {
                    exportParams.push('checkAuditName=' + encodeURIComponent(params.checkAuditName));
                }
                if(params.checkStartMonth) {
                    exportParams.push('checkStartMonth=' + params.checkStartMonth);
                }
                if(params.checkEndMonth) {
                    exportParams.push('checkEndMonth=' + params.checkEndMonth);
                }
                if(params.accountabilityStatus) {
                    exportParams.push('accountabilityStatus=' + params.accountabilityStatus);
                }
                if(params.reformStatus) {
                    exportParams.push('reformStatus=' + params.reformStatus);
                }

                var url = ctx + '/prj/overallAccountability/exportOverAllAccountability?' + exportParams.join('&');
                window.location.href = url;
                return false;
            };
            /*****************************导出再审计台账***********************************/


            /*****************************查看整体追责***********************************/
            window.viewBtn = function(accountabilityId){
                var index = top.layer.open({
                    type: 2,
                    maxmin: true,
                    content: 'views/audit/prj/endaccount/account-book/viewL.html?accountabilityId='+accountabilityId,
                    type: 2,
                area: ['95%', '85%'],
                title: [
                        '查看整体追责', 'font-size:14px;background-color: #f2f2f2;'
                    ],
                fixed: true,
                maxmin: true,
                resize: true,
                closeBtn: 1,
                shadeClose: false,
                    btn: ['关闭'],
                    btn0: function(index, layero) {
                        // 关闭按钮点击事件
                        layer.close(index);
                        return false;
                    },
                    success: function(layero, index) {
                        // 页面加载完成后的回调
                    }
                });
            };

            /*****************************填报整体追责***********************************/
            window.fillingBtn = function(accountabilityId, accountabilityStatus){
                var url = 'views/audit/prj/endaccount/account-book/edit.html?accountabilityId='+accountabilityId;
                if(accountabilityStatus) {
                    url += '&accountabilityStatus=' + accountabilityStatus;
                }

                // 根据追责状态决定显示的按钮
                var buttons = [];
                var title = '填报整体追责';

                if (accountabilityStatus == '2') {
                    // 追责中状态：只显示提交按钮
                    buttons = ['提交'];
                    title = '编辑';
                } else {
                    // 草稿状态或其他状态：显示保存和提交按钮
                    buttons = ['保存', '提交'];
                }

                var index = top.layer.open({
                    type: 2,
                    maxmin: true,
                    content: url,
                    title: [
                        title, 'font-size:14px;background-color: #f2f2f2;'
                    ],
                    area: ['95%', '98%'],
                    fixed: true,
                    closeBtn: 1, // 显示关闭按钮
                    shadeClose: false, // 点击遮罩不关闭
                    btn: buttons,
                    yes: function(index, layero) {
                        var iframeWin = $(layero).find("iframe")[0].contentWindow;

                        if (accountabilityStatus == '2') {
                            // 追责中状态：第一个按钮是提交
                            if (iframeWin.submitAccountabilityBtn) {
                                // 调用子页面的提交方法
                                iframeWin.submitAccountabilityBtn();
                            } else {
                                layer.msg('提交方法未找到', {icon: 2});
                            }
                        } else {
                            // 非追责中状态：第一个按钮是保存
                            if (iframeWin.saveAccountabilityBtn) {
                                // 调用子页面的保存方法
                                iframeWin.saveAccountabilityBtn();
                            } else {
                                layer.msg('保存方法未找到', {icon: 2});
                            }
                        }
                        return false; // 阻止默认关闭
                    },
                    btn2: function(index, layero) {
                        // 第二个按钮点击事件（只在非追责中状态存在）
                        if (accountabilityStatus != '2') {
                            var iframeWin = $(layero).find("iframe")[0].contentWindow;
                            if (iframeWin.submitAccountabilityBtn) {
                                // 调用子页面的提交方法
                                iframeWin.submitAccountabilityBtn();
                            } else {
                                layer.msg('提交方法未找到', {icon: 2});
                            }
                        }
                        return false; // 阻止默认关闭
                    },
                    success: function (layero, index) {
                    },
                    end:function(){
                        //加载表格
                        queryOverallAccountabilityTableInfo();
                    }
                });
            }
            /*****************************填报整体追责***********************************/

            //整体追责查看台账明细
            window.showCheckLedgersList = function(detailId){
                var index = window.parent.layer.open({
                    type: 2,
                    maxmin: true,
                    content: 'views/audit/prj/endaccount/endreauditstatic/checkLedgersList.html?accountabilityDetailId='+detailId
                    +'&type=accountability',
                    title: [
                        '再审计台账', 'font-size:14px;background-color: #f2f2f2;'
                    ],
                    area: ['95%', '98%'],
                    fixed: true,
                    success: function (layero, index) {
                    },
                    end:function(){
                    }
                });
            }
            //删除整体追责明细数据
            window.delDetailBen = function(accountabilityId, detailId){
                frm.confirm("是否确认删除该数据？",function(){
                    $.ajax({
                        url: ctx + '/prj/overallAccountability/delDetailInfo',
                        type: 'POST',
                        dataType: 'JSON',
                        contentType: 'application/json;charset=UTF-8',
                        data: JSON.stringify({
                            accountabilityId: accountabilityId
                        }),
                        success: function(res){
                            if(res.httpCode == 200) {
                                frm.alertMsg("删除成功");
                                //查询数据列表
                                queryOverallAccountabilityTableInfo();
                            } else {
                                frm.error(res.msg || "删除失败");
                            }
                        },
                        error: function() {
                            frm.error("网络异常，删除失败");
                        }
                    })
                })
            }
        });
    </script>
    </html>
