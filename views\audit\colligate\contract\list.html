<!--处室接口人审核风研成果-->
<!--#include virtual ="include/header.html"-->
<link href="resource/css/audit/online/tagmodel/tagIndex.css?v=6.5" rel="stylesheet" type="text/css">
<link href="resource/css/audit/online/tagmodel/tagCommon.css?v=6.5" rel="stylesheet" type="text/css">
<style>


    .layui-table-view .layui-table {
        min-width: 100%;
    }
    .layui-laydate .laydate-btns-clear{
        display: inline-block;
    }
    .new-table .layui-table-view .layui-table td, .new-table .layui-table-view .layui-table th {
        border-left: 1px solid #ddd;
        border-right: 1px solid #ddd;
    }

    .new-table .layui-table-cell {
        min-width: 100%;
    }

    .layui-common-body {
        background: #fff;
    }

    .layui-form-item {
        margin-bottom: 10px;
    }

    .tab_2 .new-style .layui-input-block {
        margin-left: 177px;
    }

    .tab_2 .new-style .layui-form-label {
        width: 175px;
    }

    .new-style .layui-input-block {
        margin-left: 112px;
    }

    .new-style .layui-form-label {
        width: 110px;
    }

    .layui-search-new.new-style .layui-form-label {
        padding: 0 6px 0 0;
    }

    .ict-header-list {
        display: inline-block;
        width: 100%;
        text-align: left;
    }

    .ict-header-li {
        padding: 0 26px;
        box-sizing: border-box;
        display: inline-block;
        cursor: pointer;
    }

    .ict-header-title {
        font-family: PingFangSC, PingFang SC;
        font-size: 15px;
        color: #333333;
        font-style: normal;
        border-bottom: 3px solid #fff;
        height: 42px;
    }

    .ict-header-li.active .ict-header-title {
        color: #C20000;
        border-bottom: 3px solid #C20000;
    }

    .ict-search-box {
        width: 100%;
        margin-top: 8px;
        background: #F7F9FA;
        padding: 10px 10px 0 10px;
        box-sizing: border-box;
    }

    .ict-search-form {
        width: 100%;
        float: left;
    }

    .ict-search-btn {
        width: 100px;
        float: right;
    }

    .model-btn-submit {
        margin-bottom: 10px;
    }

    .search-input input {
        height: 42px;
        width: 450px;
    }

    .search-input {
        margin-top: 15px;
    }

    .search-input .search-btn-right {
        width: 100px;
        height: 42px;
        border-radius: 0 4px 4px 0;
        background: #C20000;
    }

    .model-btn.model-btn-submit {
        background-color: #C20000;
        border: 1px solid #C20000;
    }

    .search-input .search-btn-right.search-btn-right-2 {
        margin-left: 20px;
        width: 120px;
        border-radius: 4px;
    }

    .search-input .search-btn-right.search-btn-right-2 .search-btn-right-text {
        padding: 0;
    }

    .new-style .layui-input-block .input-p {
        padding: 4px 0;
        display: inline-block;
        box-sizing: border-box;
    }

    .formButton {
        text-align: right;
    }

    .formButton button {
        height: 32px;
        line-height: 32px;
    }

    .layui-input[disabled="disabled"] {
        background: #eee !important;
    }


    .goClick {
        color: #C20000;
        cursor: pointer;
    }

    .dw-tip {
        text-align: right;
        color: #333333;
    }

    .model-btn.model-btn-export {
        min-width: 88px;
        padding: 0 12px;
        box-sizing: border-box;
        background-color: #ffffff;
        border: 1px solid #c20000;
        border-radius: 2px;
    }

    .model-btn.model-btn-export span {
        color: #c20000;
    }

    .model-btn.model-btn-export i {
        color: #c20000;
    }

    .layui-card-header-select.active:before {
        width: 100%;
        height: 2px;
        left: 0;
    }

    .layui-card-header-select {
        font-weight: inherit;
    }

</style>
<style>
    .layui-col-space10 > * {
        padding: 0;
    }

    .table-btn {
        margin: 0;
        width: 20px;
    }

    .layui-layer-content-my {
        background: none;
        padding: 0;
        box-shadow: 0 1px 6px rgba(0, 0, 0, .1);
        position: relative;
        line-height: 22px;
        min-width: 12px;
        font-size: 12px;
        _float: left;
        border-radius: 4px;
        color: #fff;
        position: absolute;
    }

    .layui-table-tips-main-my {
        max-height: 150px;
        padding: 8px 15px;
        padding-right: 18px;
        font-size: 14px;
        overflow-y: auto;
        background-color: #fff;
        color: #333;
        border-color: #dcdcdc;
        border-width: 1px;
        border-style: solid;
        margin-top: -10px;
    }

    .layui-table-tips-main-my .icon-guanbi1 {
        position: absolute;
        right: 10px;
        top: -8px;
        font-size: 24px;
        cursor: pointer
    }

    .layui-table-tips-main-my .icon-guanbishixin {
        position: absolute;
        right: -6px;
        top: -14px;
        color: #666;
        font-size: 24px;
        cursor: pointer
    }
</style>
<body>
<div class="layui-common-body layui-row">
    <div class="layui-row layui-col-md12 layui-col-sm12 layui-col-lg12">
        <div class="layui-row">
            <div class="layui-common-box">
                <div class="layui-row layui-common-card">
                    <div class="layui-card-body main-list-body layui-row" style="padding-top: 4px;">
                        <div class="ict-search-box layui-row">
                            <div class="ict-search-form layui-form new-style layui-row">
                                <div class="float-left layui-row" style="width:calc(100% - 120px)">
                                    <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3">
                                        <div class="layui-form-item layui-form-item-sm">
                                            <label class="layui-form-label ">合同名称</label>
                                            <div class="layui-input-block">
                                                <input class="layui-input" id="contractName" lay-filter="contractName"
                                                       name="contractName"
                                                       placeholder=" 请输入"/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="float-left  layui-col-md3 layui-col-sm3 layui-col-lg3">
                                        <div class="layui-form-item layui-form-item-sm">
                                            <label class="layui-form-label">合同编号</label>
                                            <div class="layui-input-block">
                                                <input class="layui-input" id="globalsn" lay-filter="globalsn"
                                                       name="globalsn"
                                                       placeholder="请输入"/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="float-left  layui-col-md3 layui-col-sm3 layui-col-lg3">
                                        <div class="layui-form-item layui-form-item-sm">
                                            <label class="layui-form-label">我方签约主体</label>
                                            <div class="layui-input-block">
                                                <input class="layui-input" id="weSignName" lay-filter="weSignName"
                                                       name="weSignName"
                                                       placeholder="请输入"/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="float-left  layui-col-md3 layui-col-sm3 layui-col-lg3">
                                        <div class="layui-form-item layui-form-item-sm">
                                            <label class="layui-form-label">对方签约主体</label>
                                            <div class="layui-input-block">
                                                <input class="layui-input" id="otherSignName" lay-filter="otherSignName"
                                                       name="otherSignName"
                                                       placeholder="请输入"/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="float-left  layui-col-md3 layui-col-sm3 layui-col-lg3">
                                        <div class="layui-form-item layui-form-item-sm">
                                            <label class="layui-form-label">承办单位</label>
                                            <div class="layui-input-block">
                                                <input class="layui-input" id="comName" lay-filter="comName"
                                                       name="comName"
                                                       placeholder="请输入">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="float-left  layui-col-md3 layui-col-sm3 layui-col-lg3">
                                        <div class="layui-form-item layui-form-item-sm">
                                            <label class="layui-form-label">承办部门</label>
                                            <div class="layui-input-block">
                                                <input class="layui-input" id="deptName" lay-filter="deptName"
                                                       name="deptName"
                                                       placeholder="请输入"/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="float-left  layui-col-md3 layui-col-sm3 layui-col-lg3">
                                        <div class="layui-form-item layui-form-item-sm">
                                            <label class="layui-form-label">合同状态</label>
                                            <div class="layui-input-block new-unselect" id="contractStatusCodes"
                                                 lay-filter="contractStatusCodes">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="float-left  layui-col-md3 layui-col-sm3 layui-col-lg3">
                                        <div class="layui-form-item layui-form-item-sm">
                                            <label class="layui-form-label">签订盖章日期</label>
                                            <div class="layui-input-block">
                                                <input class="layui-input" id="finalSignTime" lay-filter="finalSignTime"
                                                       name="finalSignTime" readonly
                                                       placeholder="请选择"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>


                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3"
                                     style="text-align: right;float: right;width: 120px;">
                                    <div class="model-btn model-btn-submit" onclick="tabelIns1()" title="查询">
                                        <i class="iconfont search-icon">&#xe60b;</i>
                                        <span>查询</span>
                                    </div>
                                    <div class="model-btn model-btn-reset" id="resetBtn" style="margin-bottom:10px;" title="重置">
                                        <i class="iconfont search-icon">&#xe63a;</i>
                                        <span>重置</span>
                                    </div>
                                    <div class="model-btn  model-btn-export" onclick="exportData()" title="导出">
                                        <i class="iconfont search-icon">&#xe60c;</i>
                                        <span>导出</span>
                                    </div>

                                </div>

                            </div>
                        </div>
                        <div class="layui-form new-table" style="margin-top:10px;text-align: right">
                            <table class="layui-table" id="table_1" lay-filter="table_1"></table>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>
</div>
</body>

<!--#include virtual ="include/version.html"-->
<!--#include virtual ="include/tpl/select-cat.html"-->
<script id="toolBar" type="text/html">
    <a class="table-btn" lay-event="detail" title="查看">
        <i class="iconfont">&#xe651;</i>
    </a>
</script>

<script>
    layui.use(
        [
            "jqform",
            "table",
            "laytpl",
            "jquery",
            "laydate",
            "layer",
            "jqbind",
            "upload",
            "jqztree",
            "laypage",
            "laydate",
            "selectPlus"
        ],
        function () {
            var $ = layui.jquery,
                ctx = top.global.ctx,
                $ZTree = layui.jqztree,
                layer = layui.layer,
                form = layui.jqform,
                tpl = layui.laytpl,
                selectPlus = layui.selectPlus,
                table = layui.table,
                upload = layui.upload,
                laypage = layui.laypage,
                laydate = layui.laydate,
                jqbind = layui.jqbind;
            jqbind.init();

            var selected = new Object();//多选下拉使用
            var values = []//多选下拉值
            var globalCurr, globalRes;


            //区间日期
            laydate.render({
                elem: '#finalSignTime',
                type: 'date',
                format: 'yyyy-MM-dd',
                range: true,
                done: function (value, date) {
                    console.log(value)
                }
            });

            var dictList = []

            // 合同状态编辑 加載
            function initTaskStatus(type, data) {
                dictList = data
                var html = '<option  value="">请选择</option>';

                selected = selectPlus.render({
                    el: '#contractStatusCodes',
                    data: dictList,
                    values: values,
                    valueName: "codeText",
                    label: ['codeText'],
                    valueSeparator: ","
                });
                form.render();
            }

            // BULLETIN_STATUS 加載
            function initTaskStatus2(type, data) {
                var getTpl = $('#select-tpl-list').html();
                tpl(getTpl).render(data, function (html) {
                    $("#contractCateL1Code").html(html);
                });
                form.render();
            }

            //获取合同状态字典
            getDicList('CONTRACT_STATUS_NAME', initTaskStatus);
            //获取合同类型字典
            getDicList('CONTRACT_CATE_L1_CODE', initTaskStatus2);


            // 重置
            $('#resetBtn').click(function () {
                $("#contractName").val('');
                $("#globalsn").val('');
                $("#weSignName").val('');
                $("#otherSignName").val('');
                $("#comName").val('');
                $("#deptName").val('');
                // $("#contractCateL1Code").val('');
                $("#finalSignTime").val('');
                selected.reset();
                //#relateDept 将元素销毁
                $('#contractStatusCodes').html('');
                // 将checkbox name=selectPluscheckbox1 设置为不选中
                selected = selectPlus.render({
                    el: '#contractStatusCodes',
                    data: dictList,
                    values: [],
                    valueName: "codeText",
                    label: ['codeText'],
                    valueSeparator: ","
                });
                form.render();
            });

            $(function () {
                //更多查询
                $('.btnMore').on('click', function () {
                    var me = $(this), childDwon = me.children('.slide-down'), childUp = me.children('.slide-up');
                    if (childDwon.hasClass('none')) {
                        childDwon.removeClass('none');
                        childUp.addClass('none');
                        me.find('span').text("搜索更多");
                        $('.search-more').stop().hide();
                    } else {
                        childDwon.addClass('none');
                        childUp.removeClass('none');
                        me.find('span').text("收起更多");
                        $('.search-more').stop().show();
                    }
                });
            });

            // 查询条件
            function conditionShow() {
                var finalSignTime = $('#finalSignTime').val();
                var startFinalSignTime = ''
                var endFinalSignTime = ''
                if (finalSignTime) {
                    startFinalSignTime = finalSignTime.split(' - ')[0]
                    endFinalSignTime = finalSignTime.split(' - ')[1]
                }

                var specialObj = selected.getChecked();
                var relateDept = specialObj.data;
                var contractStatusCodes = [];
                for (var i = 0; i < relateDept.length; i++) {
                    contractStatusCodes.push(relateDept[i].code)
                }
                return {
                    // monthId: $('#monthId').val(),
                    contractName: $('#contractName').val(),
                    globalsn: $('#globalsn').val(),
                    weSignName: $('#weSignName').val(),
                    otherSignName: $('#otherSignName').val(),
                    comName: $('#comName').val(),
                    deptName: $('#deptName').val(),
                    contractStatusCodes: contractStatusCodes, // 合同状态
                    // contractCateL1Code: $('#contractCateL1Code').val(), // 合同性质编码
                    startFinalSignTime: startFinalSignTime, //最终签约时间 - 开始
                    endFinalSignTime: endFinalSignTime, //最终签约时间 - 结束
                }
            }

            window.tabelIns1 = function () {
                var data = conditionShow();
                table.render({
                    elem: "#table_1",
                    id: "table_1",
                    even: true,
                    height: 'full-190',
                    url: ctx + '/colligate/contractInfo/getContractPage',
                    where: data,
                    isClient: false,
                    page: true,
                    limit: 10,
                    cols: [
                        [
                            {type: "numbers", title: "序号", align: "center", width: 70},
                            {
                                field: "globalsn",
                                title: "合同编号",
                                align: "center",
                                width: 180,
                            },
                            {
                                field: "contractName",
                                title: "合同名称",
                                align: "center",
                                width: 350,
                                style: "text-align:left",
                            },
                            {
                                field: "contractLocalsn",
                                title: "合同流水号",
                                align: "center",
                                width: 136,
                            },
                            {
                                field: "weSignName",
                                title: "我方签约主体",
                                align: "center",
                                width: 280,
                                style: "text-align:left",
                            },
                            {
                                field: "otherSignName",
                                title: "对方签约主体",
                                align: "center",
                                width: 230,
                                style: "text-align:left",
                            },
                            {
                                field: "contractStatusName",
                                title: "合同状态",
                                align: "center",
                                width: 95,
                            },
                            // {
                            //     field: "fixedAmount",
                            //     title: "是否固定金额",
                            //     align: "center",
                            //     width: "16%",
                            // },
                            {
                                field: "comName",
                                title: "承办单位",
                                align: "center",
                                width: 176,
                                style: "text-align:left",
                            },
                            {
                                field: "deptName",
                                title: "承办部门",
                                align: "center",
                                width: 176,
                                style: "text-align:left",
                            },
                            {
                                field: "takerName",
                                title: "承办人名称",
                                align: "center",
                                width: 100,
                            },
                            {
                                field: "performTimeLimitName",
                                title: "合同履行期限名称",
                                align: "center",
                                width: 138,
                            },
                            {
                                field: "contractSum",
                                title: "含税合同金额",
                                align: "center",
                                width: 110,
                                style: "text-align:right",
                                templet: function (d) {
                                    return formatNumContainNull(d.contractSum)
                                }
                            },
                            {
                                field: "moneyTypeName",
                                title: "合同币种",
                                align: "center",
                                width: 85,
                            },
                            {
                                field: "approveEndTime",
                                title: "审批通过时间",
                                align: "center",
                                width: 156,
                            },
                            {
                                field: "finalSignTime",
                                title: "签订盖章日期",
                                align: "center",
                                width: 155,
                            },
                            {
                                field: "lastApproverName",
                                title: "最终审批人名称",
                                align: "center",
                                width: 122,
                            },
                            {
                                field: "performerDeptName",
                                title: "履行部门",
                                align: "center",
                                width: 160,
                                style: "text-align:left",
                            },
                            {
                                field: "performerName",
                                title: "履行人名称",
                                align: "center",
                                width: 100,
                            },
                            {
                                field: "enterperformtime",
                                title: "履行开始时间",
                                align: "center",
                                width: 156,
                            },
                            {title: '操作', width: '70', align: 'center', toolbar: '#toolBar', fixed: 'right'},

                        ],
                    ],
                    done: function (res, curr, count1) {
                        globalCurr = curr;
                        globalRes = res;
                    },
                });
            };

            //监听操作
            table.on('tool(table_1)', function (obj) {
                var data = obj.data; //获得当前行数据
                var layEvent = obj.event; //获得 lay-event 对应的值（也可以是表头的 event 参数对应的值）

                if (layEvent == 'detail') { //选择文件
                    var indexs = top.layer.open({
                        title: '附件列表',
                        content:
                            "/jtaudithtml/views/audit/colligate/contract/dialog.html?contractId=" + data.contractId,
                        type: 2,
                        area: ["70%", "80%"],
                        fixed: true,
                        maxmin: false,
                        resize: false,
                    });
                }
            });

            tabelIns1()

            // 导出
            window.exportData = function () {
                // 获取查询条件
                var data = conditionShow();
                window.location.href = ctx + "/colligate/contractInfo/export?" +
                    "globalsn=" + encodeURI(data.globalsn) +
                    "&contractName=" + encodeURI(data.contractName) +
                    "&contractStatusCodes=" + data.contractStatusCodes.join(", ") +
                    "&startFinalSignTime=" + encodeURI(data.startFinalSignTime) +
                    "&endFinalSignTime=" + encodeURI(data.endFinalSignTime) +
                    "&comName=" + encodeURI(data.comName) +
                    "&deptName=" + encodeURI(data.deptName) +
                    "&weSignName=" + encodeURI(data.weSignName) +
                    "&otherSignName=" + encodeURI(data.otherSignName) +
                    // "&contractCateL1Code=" + encodeURI(data.contractCateL1Code) +
                    "&page=" + globalCurr +
                    "&limit=" + globalRes.limit;

            };


        })
</script>

</html>
