<!--#include virtual ="include/header.html"-->
<style>
    .layui-form-pane .layui-form-switch, .layui-form-pane .layui-form-radio {
        margin-top: 2px;
        margin-left: 10px;
    }

    .tagmodel-popup {
        padding: 6px 0;
        text-align: center;
    }

    .checkbox-li {
        padding: 0 10px;
        box-sizing: border-box;
    }

    .tagmodel-popup {
        position: absolute;
        padding: 10px;
        box-sizing: border-box;
        width: 420px;
        box-shadow: 0 0 6px 0 #ddd;
        border-radius: 4px;
        background: #fff;
        z-index: 9999999;
    }
    .layui-form-pane .layui-form-switch, .layui-form-pane .layui-form-radio {
        margin-top: 2px;
        margin-left: 0px;
    }
</style>
<body>
<div class="layui-fluid larry-wrapper">
    <section class="panel panel-padding">
        <form class="layui-form layui-form-pane form-conmon form-conmon-more" id="prjSendInfoSearch">
            <div class="transition-500ms search-condition">
                <div class="layui-row layui-col-space10">
                    <div class="layui-col-xs4">
                        <div class="layui-form-item layui-form-item-sm">
                            <label class="layui-form-label">查询方式</label>
                            <div class="layui-input-block">
                                <input checked lay-filter="selectType" name="selectType" title="按单位" type="radio" value="1"/>
                                <input lay-filter="selectType" name="selectType" title="按专业" type="radio" value="2"/>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-xs3">
                        <div class="layui-form-item layui-form-item-sm">
                            <label class="layui-form-label">审计分部</label>
                            <div class="layui-input-block">
                                <select id="auditDevisionName" lay-filter="auditDevisionName" name="auditDevisionName">
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-xs1">
                        <div class="layui-form-item layui-form-item-sm">
                            <select id="auditDevisionArea" lay-filter="auditDevisionArea" name="auditDevisionArea">
                            </select>
                        </div>
                    </div>
                    <div class="layui-col-xs4 ">
                        <div class="layui-form-item layui-form-item-sm">
                            <label class="layui-form-label">项目专业</label>
                            <div class="tagmodel-search-condition" onclick="professionCategoryClick( event )">
                                <div class="layui-input-block">
                                    <div class="layui-select-title">
                                        <input class="layui-input layui-unselect" id="professionCategoryInput" placeholder="点击项目专业(可多选)"
                                               readonly type="text" value="点击选择项目专业">
                                        <i class="layui-edge"></i>
                                    </div>
                                </div>
                                <div class="tagmodel-popup" id="tagmodelprofessionCategory" style="display: none">
                                    <div class="tagmodel-popup-title">-可多选-</div>
                                    <div class="checkbox-list" id="professionCategorySelect"></div>
                                    <div class="tagmodel-prov-btn">
                                            <span class="layui-new-btn new-confirm"
                                                  onclick="professionCategoryClick( event )">确认</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-row layui-col-space10">
                    <div class="layui-col-xs4">
                        <div class="layui-form-item layui-form-item-sm">
                            <label class="layui-form-label">审结账期</label>
                            <div class="layui-input-block">
                                <input class="layui-input" id="auditMonth" lay-filter="auditMonth"
                                       name="auditMonth" readonly
                                       placeholder=" 请选择账期范围">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="search-btn">
                <div class="layui-form-item layui-form-item-sm">
                    <button class="layui-btn btn-outline layui-btn-normal layui-btn-sm" id="searchBtn" type="button">
                        <i class="iconfont search-icon">&#xe60b;</i> 查询
                    </button>
                </div>
                <div class="layui-form-item layui-form-item-sm">
                    <button class="layui-btn btn-outline layui-btn-normal layui-btn-sm" type="button" onclick="resetClick();">
                        <i class="iconfont search-icon">&#xe63a;</i> 重置
                    </button>
                </div>
            </div>
        </form>
        <form class="layui-form layui-form-pane form-conmon form-conmon-more" id="finalAccountAuditInfo">

            <div>
                <table class="layui-table" id="infoTable" lay-filter="infoTable"></table>
            </div>
        </form>
    </section>
</div>
</body>
<!--#include virtual ="include/version.html"-->
<!--#include virtual ="include/tpl/select-cat.html"-->
<script id="profession-category-tpl" type="text/html">
    {{# layui.each(d, function(index, obj){ }}
    <div data-code='{{obj.code}}' data-index="{{index}}" class='checkbox-li {{obj.checked?"active":""}}'>
        {{obj.codeText}}
    </div>
    {{# }); }}
</script>
<script type="text/javascript">
    layui.use(['jqform', 'table', 'laytpl', 'jquery', 'laydate', 'layer', 'jqbind'], function () {
        var $ = layui.jquery,
            form = layui.jqform,
            laydate = layui.laydate,
            tpl = layui.laytpl,
            ctx = top.global.ctx,
            table = layui.table;


        form.on('radio(selectType)', function (data) {
            console.log(data)
            showData();
        })

        // 首页决算审结钻取
        // if ("finalSendNum" == getUrlParam("homePageFlag")) {
        //     var monthId = decodeURIComponent(getUrlParam("monthId"));
        //     if (monthId) {
        //         $('#auditMonth').val(monthId);
        //     }
        //     $("#tipsInfo").show();
        // }

        // 加载表头数据
        showData();
        //重置
        window.resetClick = function(){
            $("#prjSendInfoSearch")[0].reset();

            //项目专业
            unCheckList(categoryGroup);
            $('#professionCategoryInput').val('点击选择项目专业');
            rendercategoryGroup(categoryGroup);
        }


        /**
         * 项目专业类别开始-----------------------------------------------------------------
         * @param e
         */
        var categoryGroup = []
        // 返回字符串 arrayToStr
        window.printStr = function (arr) {
            var str = [];
            $.each(arr, function (index, item) {
                str.push(item.name);
            })
            return str.join("，")
        };
        window.setDefaultChecked = function (arr) {
            for (var i = 0; i < arr.length; i++) {
                arr[i].checked = false;
            }
        };
        // 项目专业打开与关闭
        window.professionCategoryClick = function (e) {
            e.stopPropagation(); // 事件冒泡
            var type = $(e.target).hasClass("new-confirm") ? false : true;
            if (type) {
                $('#tagmodelprofessionCategory').show();
            } else {
                showSelectCondition();
                $('#tagmodelprofessionCategory').hide();
            }
        };
        $('#professionCategorySelect').on('click', '.checkbox-li', function () {
            var index = $(this).data("index");
            categoryGroup[index].checked = !categoryGroup[index].checked;
            rendercategoryGroup(categoryGroup);
        });
        // 获取项目专业列表
        window.querycategoryGroup = function () {
            $.ajax({
                url: ctx + "/prj/endAccountAuditReport/queryProfession",
                dataType: "JSON",
                type: "POST",
                data: JSON.stringify({}),
                contentType: "application/json;charset=UTF-8",
                success: function (res) {
                    if (res.httpCode == 200) {
                        setDefaultChecked(res.data);
                        $.each(res.data, function (index, item) {
                            item.className = "monitor_group";
                        });
                        categoryGroup = res.data;
                        console.log(categoryGroup)
                        rendercategoryGroup(res.data);
                    }
                },
                error: function (e) {
                    console.info('加载项目专业下拉列表出错:' + e.info);
                }
            });
        };
        window.rendercategoryGroup = function (data) {
            lp(tpl, data, $("#profession-category-tpl").html(), $('#professionCategorySelect'));
        };
        window.unCheckList = function (arr) {
            $.each(arr, function (index, item) {
                item.checked = false;
            });
        }
        window.showSelectCondition = function () {
            var tmp1 = getCheckedSelect(categoryGroup);
            var str;
            if (tmp1.length) {
                str = printStr(tmp1);
                $("#professionCategoryInput").val(str);
                $("#professionCategoryInput").parent().attr("title", str);
            } else {
                $("#professionCategoryInput").val("点击选择项目专业");
            }

        };
        // 获取选中的
        function getCheckedSelect (arr, type) {
            var tmp = [];
            var item;
            for (var i = 0; i < arr.length; i++) {
                item = arr[i];
                if (item.checked) {
                    if (type !== "param") {
                        tmp.push({
                            name: item.codeText || item.CODETEXT,
                            code: item.code || item.CODE,
                            class: item.className
                        });
                    } else {
                        tmp.push(item.code || item.CODE);
                    }
                }
            }
            return tmp;
        };
        querycategoryGroup();
        // 项目专业类别结束-----------------------------------------------------------------

        function showData() {
            $.ajax({
                url: ctx + "/prj/auditInfo/tableTitle/" + 220003
                , dataType: "json"
                , type: "POST"
                , success: function (data) {
                    var tableData = [[], [], [], [], []];
                    var reportId = "";
                    if (data.httpCode == 200) {
                        tableData[0].push({
                            type: 'numbers',
                            title: '序号',
                            rowspan: 2,
                            width: '5%',
                            fixed: 'left'
                        });
                        $.each(data.data, function (index, obj) {

                            if (Number(obj.spanStartRow) == 0) {
                                if(obj.field=='PRJ_COUNT'){
                                    tableData[0].push({
                                        field: obj.field,
                                        title: obj.title,
                                        align: 'center',
                                        width: obj.width,
                                        colspan: Number(obj.colspan),
                                        rowspan: Number(obj.rowspan),
                                        style: 'text-align: center;text-decoration:underline;color:red;cursor:pointer;',
                                        event: 'prjCount'
                                    })
                                }else{
                                    tableData[0].push({
                                        field: obj.field,
                                        title: obj.title,
                                        style: obj.style,
                                        align: 'center',
                                        width: obj.width,
                                        colspan: Number(obj.colspan),
                                        rowspan: Number(obj.rowspan)
                                    })
                                }

                            } else if (Number(obj.spanStartRow) == 1) {
                                tableData[1].push({
                                    field: obj.field,
                                    title: obj.title,
                                    style: obj.style,
                                    align: 'center',
                                    width: obj.width,
                                    colspan: Number(obj.colspan),
                                    rowspan: Number(obj.rowspan)
                                })
                            } else if (Number(obj.spanStartRow) == 2) {
                                tableData[2].push({
                                    field: obj.field,
                                    title: obj.title,
                                    style: obj.style,
                                    align: 'center',
                                    width: obj.width,
                                    colspan: Number(obj.colspan),
                                    rowspan: Number(obj.rowspan)
                                })
                            } else if (Number(obj.spanStartRow) == 3) {
                                tableData[3].push({
                                    field: obj.field,
                                    title: obj.title,
                                    style: obj.style,
                                    align: 'center',
                                    width: obj.width,
                                    colspan: Number(obj.colspan),
                                    rowspan: Number(obj.rowspan)
                                })
                            } else if (Number(obj.spanStartRow) == 4) {
                                tableData[4].push({
                                    field: obj.field,
                                    title: obj.title,
                                    style: obj.style,
                                    align: 'center',
                                    width: obj.width,
                                    colspan: Number(obj.colspan),
                                    rowspan: Number(obj.rowspan)
                                })
                            }
                        });
                        showTable(tableData);
                    }
                }
                , error: function (e) {
                    console.info('决算台账数据加载出错:' + e.info);
                }
            });
        }

        // 加载表格数据
        function showTable(tableData) {
            // 获取查询条件
            var data = conditionShow();
            table.render({
                elem: '#infoTable',
                url: ctx + "/prj/finalAuditLedger/queryAuditReportData",
                where: data,
                page: true,
                even: true,
                cols: tableData,
                done: function (res) {
                    //表头颜色一致
                    $('.layui-table-header .layui-table').removeAttr("lay-even");
                    $('.layui-table-header .layui-table').attr("class", 'layui-table lay-even');
                }
            });
        }
        /**
         * 格式化账期时间具体到月
         */
        function getMonth() {
            var monthId = $("#auditMonth").val();
            if (monthId) {
                var stratYearMonth = $("#auditMonth").val().split("~")[0].trim();
                var endYearMonth = $("#auditMonth").val().split("~")[1].trim();
                var stratYear = stratYearMonth.substr(0, 4);
                var endYear = endYearMonth.substr(0, 4);
                var stratMonth = stratYearMonth.substr(4, 5);
                var endMonth = endYearMonth.substr(4, 5);
                var dateTime = stratYear + '-' + stratMonth + ' ~ ' + endYear + '-' + endMonth;
                return dateTime;
            } else {
                return "";
            }
        }
        /**
         * 项目数监听事件
         */
        table.on('tool(infoTable)', function (obj) {
            // 查询条件
            var data = obj.data;
            var monthId = $("#auditMonth").val();
            var professionCategoryInput = $("#professionCategoryInput").val();
            var stratYearMonth = "";
            var endYearMonth = "";
            if (monthId) {
                stratYearMonth = $("#auditMonth").val().split("~")[0].trim();
                endYearMonth = $("#auditMonth").val().split("~")[1].trim();
            }

            var selectType = $('input[name="selectType"]:checked').val();

            var professionCategory = getCheckedSelect(categoryGroup, "param");
            var professionCategoryValue;
            professionCategoryValue = professionCategory.join();
            var professionCategoryNameValue = getCheckedSelect(categoryGroup);
            //结算审计方式
            var vOrder = data.V_ORDER;
            professionCategoryNameValue = printStr(professionCategoryNameValue);

            if ('prjCount' == obj.event) {
                var value = obj.data.AUDIT_DEVISION_1;
                var auditDevisionName = (value && value != 0 && value !== "")
                    ? value
                    : "";
                var auditDevisionArea = '';
                var auditTypeEnumId = obj.data.V_NAME == 1 || obj.data.V_NAME == 2 ? obj.data.V_NAME : "";
                // 判断长度是否大于 2，进行截取
                if (selectType == 1) {
                    if (auditDevisionName && auditDevisionName.length > 2) {
                        auditDevisionArea = auditDevisionName;
                        auditDevisionName = auditDevisionName.substring(0, 2);
                    } else {
                        auditDevisionArea = $('#auditDevisionArea option:selected').val();//选中的值
                        auditDevisionName = $('#auditDevisionName option:selected').val();//选中的文本
                    }
                } else {
                    auditDevisionArea = $('#auditDevisionArea option:selected').val();//选中的值
                    auditDevisionName = $('#auditDevisionName option:selected').val();//选中的文本
                }

                // 专业类别取选中的值
                // if (professionCategoryValue) {
                //     console.log("auditDevisionName1--->", auditDevisionName1);
                //     console.log("auditDevisionArea1--->", auditDevisionArea1);
                //     if (auditDevisionName1) {
                //         auditDevisionName = auditDevisionName1;
                //     }
                //     if (auditDevisionArea1) {
                //         auditDevisionArea = auditDevisionArea1;
                //     }
                // }

                // 页面跳转-审结数
                // views/audit/prj/finalaccount/auditinfo/auditLedgers.html
                layer.open({
                    type: 2,
                    content: "views/audit/prj/finalaccount/auditinfo/auditLedgers.html?auditDevision=" + auditDevisionName + '' +
                        '&auditDevisionArea=' + auditDevisionArea + "&auditTypeEnumId=" + auditTypeEnumId + "&stratYearMonth=" + stratYearMonth + "&endYearMonth=" + endYearMonth
                        + "&professionCategoryInput=" + encodeURIComponent(professionCategoryInput) + "&professionCategory=" + encodeURIComponent(professionCategoryValue),
                    title: "决算台账查询",
                    area: ['98%', '98%']
                });
                // parent.layer.open({
                //     type: 2,
                //     content: "views/audit/prj/extractend/auditledgers/endAuditLedgersTwo.html?reportPageFlag=auditNum" + '&dateTime=' +
                //         encodeURI(dateTime) + '&vOrder=' + vOrder + '&auditDevision=' + auditDevision + '&auditDevisionName=' + encodeURI(auditDevisionName)
                //         + '&auditDevisionArea=' + auditDevisionArea + '&auditDevisionAreaName=' + encodeURI(auditDevisionAreaName)
                //         + '&professionCategory=' + professionCategoryValue + '&professionCategoryName=' + encodeURI(professionCategoryNameValue)
                //         + '&auditType=&agencyName=&agencyCode=&constructionUnit=&auditTypeName=&auditTypeCode=',
                //     title: ['工程审计台账2.0', 'font-size:14px;background-color: #f2f2f2;'],
                //     area: ['98%', '98%']
                // });
            }
        })

        // 查询条件
        function conditionShow() {
            // 项目名称
            // 查询方式
            var selectType = $('input[name="selectType"]:checked').val();
            // 报审单位
            var auditDevisionArea = document.getElementById("auditDevisionArea").value;
            var auditDevisionName = document.getElementById("auditDevisionName").value;
            // 专业类别
            var professionCategory = getCheckedSelect(categoryGroup, "param");
            // 审结账期
            var auditMonth = $("#auditMonth").val();
            var beginAuditMonth = "";
            var endAuditMonth = "";
            if (auditMonth != "") {
                beginAuditMonth = auditMonth.split("~")[0].trim();
                endAuditMonth = auditMonth.split("~")[1].trim();
            }

            var data = {
                reportId: 220003,
                queryType: selectType,// 查询方式
                auditDevisionArea: auditDevisionArea,
                auditDevisionName: auditDevisionName,
                professionCategory: professionCategory,
                beginAuditMonth: beginAuditMonth,
                endAuditMonth: endAuditMonth,
            };
            return data;
        }

        // 查询
        $('#searchBtn').click(function () {
            showData();
        });

        // 加载省分审计分部下拉列表
        $.ajax({
            url: ctx + "/prj/auditInfo/auditDevision"
            , dataType: "json"
            , type: "POST"
            , success: function (data) {
                if (data.length > 1) {
                    var option = new Option();
                    $("#auditDevisionName").append(option);
                    $.each(data, function (index, obj) {
                        var option = new Option(obj.AUDIT_NAME, obj.AUDIT_ID);
                        $("#auditDevisionName").append(option);
                        form.render('select');
                    })
                } else if (data.length == 1) {
                    var option = new Option(data[0].AUDIT_NAME, data[0].AUDIT_ID);
                    $("#auditDevisionName").append(option);
                    form.render('select');
                    getAuditName();
                } else {
                    // 展示当前地市审计分部
                    getOrgGrade();
                }
            }
            , error: function (e) {
                console.info('加载省分审计分部下拉列表出错:' + e.info);
            }
        });

        // 加载地市审计分部下拉列表
        function getAuditName() {
            $("#auditDevisionArea").empty();
            form.render();
            var orgId = document.getElementById("auditDevisionName").value?document.getElementById("auditDevisionName").value:0;
            $.ajax({
                url: ctx + "/prj/auditInfo/getAuditName/" + orgId
                , dataType: "json"
                , type: "POST"
                , success: function (data) {
                    var option = new Option();
                    $("#auditDevisionArea").append(option);
                    $.each(data, function (index, obj) {
                        var option = new Option(obj.DEVISION_NAME, obj.AUDIT_ID);
                        $("#auditDevisionArea").append(option);
                        form.render('select');
                    })
                }
                , error: function (e) {
                    console.info('加载地市审计分部下拉列表出错:' + e.info);
                }
            })
        }

        // 省分审计分部下拉框触发
        form.on('select(auditDevisionName)', function (data) {
            $("#AUDIT_ID").empty();
            getAuditName();
        });

        // 审结账期
        laydate.render({
            elem: '#auditMonth'
            , type: 'month'
            , range: '~'
            , format: 'yyyyMM'
        });

        $(function () {
            //更多查询
            $('.btnMore').on('click', function () {
                var me = $(this), childDwon = me.children('.slide-down'), childUp = me.children('.slide-up');
                if (childDwon.hasClass('none')) {
                    childDwon.removeClass('none');
                    childUp.addClass('none');
                    me.find('span').text("搜索更多");
                    $('.search-more').stop().hide();
                } else {
                    childDwon.addClass('none');
                    childUp.removeClass('none');
                    me.find('span').text("收起更多");
                    $('.search-more').stop().show();
                }
            });
        });

    });
</script>

</html>
