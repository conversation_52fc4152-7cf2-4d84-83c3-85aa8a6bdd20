<!--dialog.html-->
<!--预警待阅-->
<!--#include virtual ="include/header.html"-->
<link href="resource/css/audit/online/tagmodel/tagIndex.css?v=6.5" rel="stylesheet" type="text/css" />
<link href="resource/css/audit/online/tagmodel/tagCommon.css?v=6.5" rel="stylesheet" type="text/css" />
<style>
    .top-candel {
        width: 100%;
        height: 60px;
        background: rgba(244, 244, 244, 0.39);
        box-sizing: border-box;
        padding: 0px 20px;
        display: flex;
        align-items: center;
    }

    .top-candel .left-search {
        display: flex;
        align-items: center;
    }

    .top-candel .left-search .left-search-name {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 400;
        color: #333333;
        padding-right: 12px;
    }

    .top-candel .left-search input {
        width: 160px;
        height: 32px;
        background: rgba(255, 255, 255, 0.39);
        border: 1px solid #D9D9D9;
        border-radius: 1px;
        padding-left: 10PX;
        padding-right: 10PX;
        font-size: 14PX;
    }

    .right-btn {
        display: flex;
        align-items: center;
    }

    .empty-flex {
        flex: 1
    }

    .model-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 80px !important;
        border-radius: 4px !important;
    }

    .model-btn i {
        margin-right: 3px;
    }

    .model-btn.other-btn-submit {
        background: #FCECED;
        color: #C20000;
        border: 1px solid #C20000;
        margin-left: 12px;
    }

    .bottom-table {
        margin-top: 8px;
        padding-bottom: 15px;
    }

    .table-flex-j-s {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .table-flex-j-s span {
        cursor: pointer;
        color: #C20000;
        font-size: 15px;
    }

    .table-flex-j-s span:last-child {
        margin-left: 25px;
    }
    .layui-laydate .laydate-btns-clear{
        display:inline-block!important;
    }
</style>

<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <button id="sendPostBtn" class="layui-btn model-btn other-btn-submit" style="float: right;">
                    <i class="layui-icon layui-icon-release"></i> 附件同步
                </button>
                <div class="bottom-table">
                    <table class="layui-table jq-even" id="table_1" lay-filter="table_1"> </table>
                </div>
            </div>
        </div>
    </div>
</div>
</body>

<script id="upload" type="text/html">
    <a href='/jtauditwo/files/downLoad/{{encrypt(d.attachmentId)}}' class="table-btn tip-edit"
       title="下载">
        <i class="iconfont">&#xeac3;</i>
    </a>
</script>

<!--#include virtual ="include/version.html"-->
<script src="resource/js/jquery/jquery.js" type="text/javascript"></script>

<script id="toolbar">
    layui.use([
        "jqform",
        "table",
        "laytpl",
        "jquery",
        "laydate",
        "layer",
        "jqbind",
        "upload",
        "jqztree",
        "laypage",
        "laydate",
        "jqfrm"
    ], function () {
        var $ = layui.jquery,
            ctx = top.global.ctx,
            $ZTree = layui.jqztree,
            layer = layui.layer,
            form = layui.jqform,
            tpl = layui.laytpl,
            table = layui.table,
            upload = layui.upload,
            laypage = layui.laypage,
            laydate = layui.laydate,
            frm = layui.jqfrm,
            jqbind = layui.jqbind;
        jqbind.init();
        // 获取URL中的globalsn参数
        var contractId = getUrlParam('contractId').toString();
        var contractGlobalsn = getUrlParam('contractGlobalsn').toString();

        // 调用接口获取文件列表
        if (contractId) {
            window.tabelIns1 = function () {
                table.render({
                        elem: "#table_1",
                        even: true,
                        url: ctx + '/colligate/contractInfo/getConFileList',
                        where: {
                            contractId: contractId,
                            contractGlobalsn: contractGlobalsn,
                        },
                        isClient: false,
                        cols: [
                            [
                                { type: "numbers", title: "序号", align: "center", width: "5%" },
                                {
                                    field: "contractName",
                                    title: "合同名称",
                                    align: "center",
                                    width: "35%",
                                    style: "text-align:left",
                                },
                                {
                                    field: "sourceName",
                                    title: "文件名称",
                                    align: "center",
                                    width: "35%",
                                    style: "text-align:left",
                                },
                                {
                                    field: "fileTypeName",
                                    title: "文件类别",
                                    align: "center",
                                    width: "15%",
                                },
                                {title: '操作', align: 'center',toolbar: '#upload'},
                            ]
                        ],
                    }
                )

            }
            tabelIns1()

            $('#sendPostBtn').on('click', function () {
                $.ajax({
                    url: ctx + '/colligate/contractInfo/syncContractFileInfo',
                    type: 'POST',
                    dataType: 'json',
                    data: JSON.stringify({ // 转换数据为 JSON 字符串
                        contractId: contractId,
                    }),
                    contentType: "application/json;charset=UTF-8",
                    success: function (res) {
                        if (res.httpCode === 200) {
                            frm.success("附件同步成功");
                            tabelIns1(); // 刷新表格
                        } else {
                            frm.msg('请求失败: ' + res.msg, { icon: 2 });
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error('POST请求出错:', error);
                        frm.msg('网络错误，请检查连接', { icon: 2 });
                    }
                })
            });

        }
    });
</script>
