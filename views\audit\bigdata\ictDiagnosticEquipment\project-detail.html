<!--ICT诊断仪 项目详情-->
<!--#include virtual ="include/header.html"-->
<link href="resource/css/audit/online/tagmodel/tagIndex.css?v=6.5" rel="stylesheet" type="text/css">
<link href="resource/css/audit/online/tagmodel/tagCommon.css?v=6.5" rel="stylesheet" type="text/css">
<link href="resource/css/number-risk-diagnosis/index.css?v=6.5" rel="stylesheet" type="text/css"/>
<style>

    .new-table .layui-table-view .layui-table td, .new-table .layui-table-view .layui-table th {
        border-left: 1px solid #ddd;
        border-right: 1px solid #ddd;
    }

    .layui-common-body {
        background: #fff;
    }

    .layui-form-item {
        margin-bottom: 10px;
    }

    .tab_2 .new-style .layui-input-block {
        margin-left: 177px;
    }

    .tab_2 .new-style .layui-form-label {
        width: 175px;
    }

    .new-style .layui-input-block {
        margin-left: 112px;
    }

    .new-style .layui-form-label {
        width: 110px;
    }

    .layui-search-new.new-style .layui-form-label {
        padding: 0 6px 0 0;
    }

    .ict-header-list {
        display: inline-block;
        width: 100%;
        text-align: left;
    }

    .ict-header-li {
        padding: 0 26px;
        box-sizing: border-box;
        display: inline-block;
        cursor: pointer;
    }

    .ict-header-title {
        font-family: PingFangSC, PingFang SC;
        font-size: 15px;
        color: #333333;
        font-style: normal;
        border-bottom: 3px solid #fff;
        height: 42px;
    }

    .ict-header-li.active .ict-header-title {
        color: #C20000;
        border-bottom: 3px solid #C20000;
    }

    .ict-search-box {
        width: 100%;
        margin-top: 8px;
        background: #F7F9FA;
        padding: 10px 10px 0 10px;
        box-sizing: border-box;
    }

    .ict-search-form {
        width: 100%;
        float: left;
    }

    .ict-search-btn {
        width: 100px;
        float: right;
    }

    .model-btn-submit {
        margin-bottom: 10px;
    }

    .search-input input {
        height: 42px;
        width: 450px;
    }

    .search-input {
        margin-top: 15px;
    }

    .search-input .search-btn-right {
        width: 100px;
        height: 42px;
        border-radius: 0 4px 4px 0;
        background: #C20000;
    }

    .model-btn.model-btn-submit {
        background-color: #C20000;
        border: 1px solid #C20000;
    }

    .search-input .search-btn-right.search-btn-right-2 {
        margin-left: 20px;
        width: 120px;
        border-radius: 4px;
    }

    .search-input .search-btn-right.search-btn-right-2 .search-btn-right-text {
        padding: 0;
    }

    .new-style .layui-input-block .input-p {
        padding: 4px 0;
        display: inline-block;
        box-sizing: border-box;
    }

    .formButton {
        text-align: right;
    }

    .formButton button {
        height: 32px;
        line-height: 32px;
    }

    .layui-input[disabled="disabled"] {
        background: #eee !important;
    }

    .layui-laydate .laydate-btns-clear {
        display: inline-block;
    }

    .goClick {
        color: #C20000;
        cursor: pointer;
    }

    .dw-tip {
        text-align: right;
        color: #333333;
    }

    .model-btn.model-btn-export {
        min-width: 88px;
        padding: 0 12px;
        box-sizing: border-box;
        background-color: #ffffff;
        border: 1px solid #c20000;
        border-radius: 2px;
    }

    .model-btn.model-btn-export span {
        color: #c20000;
    }

    .model-btn.model-btn-export i {
        color: #c20000;
    }

    .dw-text {
        text-align: right;
        font-weight: 600;
        font-size: 16px;
        padding-top: 10px;
    }

</style>

<body>

<div class="layui-common-body layui-row">

    <div class="tab-menu">
        <a data-parent="true" href="javascript:" id="tableMenu" style="color: #c00;text-decoration:underline;">
            <i class="iconfont" data-icon=""></i>
        </a>
    </div>

    <div class="layui-row layui-col-md12 layui-col-sm12 layui-col-lg12">
        <div class="layui-row">
            <div class="layui-common-box">
                <div class="layui-row layui-common-card">
                    <div class="layui-card-body main-list-body layui-row" style="padding-top: 4px;min-height: 740px;">
                        <div class="ict-search-box layui-row">
                            <div class="ict-search-form layui-form new-style layui-row">

                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="layui-form-item layui-form-item-sm">
                                        <label class="layui-form-label color">省分</label>
                                        <div class="layui-input-block">
                                            <select id="provCode" lay-filter="provCode" name="provCode"></select>
                                        </div>
                                    </div>
                                </div>
                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="layui-form-item layui-form-item-sm">
                                        <label class="layui-form-label color">地市</label>
                                        <div class="layui-input-block">
                                            <select id="areaCode" lay-filter="areaCode" name="areaCode"></select>
                                        </div>
                                    </div>
                                </div>
                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="layui-form-item layui-form-item-sm">
                                        <label class="layui-form-label color">风险大类</label>
                                        <div class="layui-input-block">
                                            <select id="riskCategoryCode" lay-filter="riskCategoryCode"
                                                    name="riskCategoryCode"></select>
                                        </div>
                                    </div>
                                </div>
                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="layui-form-item layui-form-item-sm">
                                        <label class="layui-form-label color">风险小类</label>
                                        <div class="layui-input-block">
                                            <select id="riskSubCategoryCode" lay-filter="riskSubCategoryCode"
                                                    name="riskSubCategoryCode"></select>
                                        </div>
                                    </div>
                                </div>
                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="layui-form-item layui-form-item-sm">
                                        <label class="layui-form-label color">公司名称</label>
                                        <div class="layui-input-block">
                                            <input class="layui-input" id="comName" type="text">
                                        </div>
                                    </div>
                                </div>
                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="layui-form-item layui-form-item-sm">
                                        <label class="layui-form-label color">客户名称</label>
                                        <div class="layui-input-block">
                                            <input class="layui-input" id="customerName" type="text">
                                        </div>
                                    </div>
                                </div>
                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="layui-form-item layui-form-item-sm">
                                        <label class="layui-form-label color">供应商名称</label>
                                        <div class="layui-input-block">
                                            <input class="layui-input" id="otherside" type="text">
                                        </div>
                                    </div>
                                </div>

                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="layui-form-item layui-form-item-sm">
                                        <label class="layui-form-label color">项目编号</label>
                                        <div class="layui-input-block">
                                            <input class="layui-input" id="projectNumber" type="text">
                                        </div>
                                    </div>
                                </div>

                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="layui-form-item layui-form-item-sm">
                                        <label class="layui-form-label color">项目名称</label>
                                        <div class="layui-input-block">
                                            <input class="layui-input" id="projectName" type="text">
                                        </div>
                                    </div>
                                </div>
                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="layui-form layui-form-item layui-form-item-sm">
                                        <label class="layui-form-label color">类型</label>
                                        <div class="right-value">
                                            <div class=" layui-input-block">
                                                <input lay-filter="riskTypeRadio" lay-skin="primary" name="type"
                                                       value="1" title="按立项期间" type="radio" checked>
                                                <input lay-filter="riskTypeRadio" lay-skin="primary" name="type"
                                                       value="2" title="按计收期间" type="radio">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="layui-form-item layui-form-item-sm">
                                        <label class="layui-form-label color">立项期间</label>
                                        <div class="layui-input-block">
                                            <input class="layui-input" id="initiationTime" readonly type="text">
                                        </div>
                                    </div>
                                </div>

                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="layui-form-item layui-form-item-sm">
                                        <label class="layui-form-label color">截止账期</label>
                                        <div class="layui-input-block">
                                            <input class="layui-input" id="date1" readonly type="text">
                                        </div>
                                    </div>
                                </div>
                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="layui-form-item layui-form-item-sm">
                                        <label class="layui-form-label color">计收期间</label>
                                        <div class="layui-input-block">
                                            <input class="layui-input" id="date2" readonly type="text">
                                        </div>
                                    </div>
                                </div>



                                <div class="float-left layui-col-md9 layui-col-sm9 layui-col-lg9"
                                     style="text-align: right;">
                                    <div class="model-btn model-btn-submit" onclick="reloadForm()" title="查询">
                                        <i class="iconfont search-icon">&#xe60b;</i>
                                        <span>查询</span>
                                    </div>
                                    <div class="model-btn model-btn-reset" onclick="restFun()" title="重置">
                                        <i class="iconfont search-icon">&#xe63a;</i>
                                        <span>重置</span>
                                    </div>

                                    <div class="model-btn model-btn-export" onclick="exportBtn()" title="明细导出">
                                        <i class="iconfont search-icon">&#xe60c;</i>
                                        <span>明细导出</span>
                                    </div>


                                </div>

                            </div>
                        </div>


                        <div class="tab_1 tab_box">
                            <div class="layui-card-body main-list-body">
                                <div class="layui-form new-table">
                                    <div class="dw-text">金额单位：元</div>
                                    <table class="layui-table jq-even" id="risk_profile_table"
                                           lay-filter="risk_profile_table"></table>
                                </div>
                            </div>
                        </div>


                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--#include virtual ="include/version.html"-->
<script src="resource/js/jquery/jquery.js" type="text/javascript"></script>
<script src="resource/js/echarts/echarts.min.js?v=6.5" type="text/javascript"></script>
<script id="select-sub-tpl" type="text/html">
    <option value="">--请选择--</option>
    {{# layui.each(d.list, function(index, item){ }}
    <option value="{{item.CODE}}">{{item.CODETEXT}}</option>
    {{# }); }}
</script>

<script id="select-prov-tpl" type="text/html">
    <option value="">--请选择--</option>
    {{# layui.each(d.list, function(index, item){ }}
    <option value="{{item.provCode}}">{{item.provName}}</option>
    {{# }); }}
</script>
<script id="select-area-tpl" type="text/html">
    <option value="">--请选择--</option>
    {{# layui.each(d.list, function(index, item){ }}
    <option value="{{item.areaCode}}">{{item.areaName}}</option>
    {{# }); }}
</script>
<script id="select-Category-tpl" type="text/html">
    <option value="">--请选择--</option>
    {{# layui.each(d.list, function(index, item){ }}
    <option value="{{item.lev1Code}}">{{item.lev1Name}}</option>
    {{# }); }}
</script>
<script>

    layui.use(['jqform', 'table', 'laytpl', 'jquery', 'laydate', 'layer', 'jqbind', 'jqfrm'], function () {

        var $ = layui.jquery,
            ctx = top.global.ctx,
            layer = layui.layer,
            laydate = layui.laydate,
            form = layui.jqform,
            frm = layui.jqfrm,
            tpl = layui.laytpl,
            table = layui.table,
            jqbind = layui.jqbind;

        //监听类型单选框
        form.on('radio(riskTypeRadio)', function (data) {
            if (data.value == "1") {
                $('#date2').attr('disabled', true)
                $('#date1').val("");
                $('#initiationTime').val("");
                $('#date2').val("");
                $('#date1').attr('disabled', false)
                $('#initiationTime').attr('disabled', false)
            } else {
                $('#date1').val("");
                $('#initiationTime').val("");
                $('#date2').val("");
                $('#date2').attr('disabled', false)
                $('#date1').attr('disabled', true)
                $('#initiationTime').attr('disabled', true)
            }
        });


        var selectedValue = getUrlParam("selectedValue");
        if (selectedValue == "1") {
            $('#date2').attr('disabled', true)
            $('#date1').val("");
            $('#initiationTime').val("");
            $('#date2').val("");
            $('#date1').attr('disabled', false)
            $('#initiationTime').attr('disabled', false)
        } else {
            $('#date1').val("");
            $('#initiationTime').val("");
            $('#date2').val("");
            $('#date2').attr('disabled', false)
            $('#date1').attr('disabled', true)
            $('#initiationTime').attr('disabled', true)
        }
        $('[name="type"][value="'+selectedValue+'"]').attr('checked', true)
        var projectDateStart = getUrlParam("projectDateStart");
        var projectDateEnd = getUrlParam("projectDateEnd");
        // 计收期间
        var accountPeriodStart = getUrlParam("accountPeriodStart");
        var accountPeriodEnd = getUrlParam("accountPeriodEnd");

        var diagnosisPeriod = getUrlParam("diagnosisPeriod");
        var riskCategoryCode = getUrlParam("riskCategoryCode");
        var riskSubCategoryCode = getUrlParam("riskSubCategoryCode");
        var provCode = getUrlParam("provCode");
        var areaCode = getUrlParam("areaCode");
        var searchCount = 0;
        var categoryChangeFlag = true
        var provChangeFlag = true


        var initiationTimeValEnd;
        if (getNowDate() > 15) {
            initiationTimeValEnd = getMonthAll()[1]
        } else {
            initiationTimeValEnd = getMonthAll()[2]
        }
        var initiationTimeValStart = getMonthArr(initiationTimeValEnd, 12)[0];
        if (!diagnosisPeriod&&selectedValue=='1') {
            diagnosisPeriod = initiationTimeValEnd;
        }
        form.render()

        /* 省分*/
        $.ajax({
            url: ctx + "/ict/getProv",
            dataType: "JSON",
            type: "POST",
            data: JSON.stringify({}),
            contentType: "application/json;charset=UTF-8",
            success: function (ret) {
                var data = {list: ret.data};
                var getTpl = $("#select-prov-tpl").html();
                var obj = $("#provCode");
                tpl(getTpl).render(data, function (html) {
                    obj.html(html);
                });
                $("#provCode").val(provCode)
                if (provCode) {
                    getArea(provCode)
                }
                form.render();
            },
            error: function (e) {
                console.info(e);
            }
        });


        //省分下拉框触发
        form.on('select(provCode)', function (data) {
            getArea(data.value);
        })

        ///加载地市下拉列表
        function getArea(provCode) {
            $.ajax({
                url: ctx + "/ict/getArea"
                , dataType: "json"
                , type: "POST"
                , data: JSON.stringify({provCode: provCode})
                , contentType: "application/json;charset=UTF-8"
                , success: function (res) {
                    var data = {list: res.data};
                    var getTpl = $("#select-area-tpl").html();
                    var obj = $("#areaCode");
                    tpl(getTpl).render(data, function (html) {
                        obj.html(html);
                    });
                    if (provChangeFlag) {
                        $("#areaCode").val(areaCode)
                    }
                    provChangeFlag = false;
                    form.render();
                }
                , error: function (e) {
                }
            })
        };

        /* 风险大类*/
        $.ajax({
            url: ctx + "/ict/getRiskCategory",
            dataType: "JSON",
            type: "POST",
            contentType: "application/json;charset=UTF-8",
            data: JSON.stringify({
                fCode: ""
            }),
            success: function (ret) {
                var data = {list: ret.data};
                var getTpl = $("#select-Category-tpl").html();
                var obj = $("#riskCategoryCode");
                tpl(getTpl).render(data, function (html) {
                    obj.html(html);
                });
                $("#riskCategoryCode").val(riskCategoryCode)
                if (riskCategoryCode) {
                    riskSubCategoryCodeFunction(riskCategoryCode);
                }
                form.render();
            },
            error: function (e) {
                console.info(e);
            }
        });

        //风险大类下拉框触发
        form.on('select(riskCategoryCode)', function (data) {
            riskSubCategoryCodeFunction(data.value);
        });

        //风险小类下拉列表
        function riskSubCategoryCodeFunction(fCode) {
            var riskCategoryCode = $('#riskCategoryCode').val();

            $.ajax({
                url: ctx + "/ict/getRiskCategory",
                dataType: "JSON",
                type: "POST",
                contentType: "application/json;charset=UTF-8",
                data: JSON.stringify({
                    fCode: fCode
                }),
                success: function (ret) {
                    var data = {list: ret.data};
                    var getTpl = $("#select-Category-tpl").html();
                    var obj = $("#riskSubCategoryCode");
                    tpl(getTpl).render(data, function (html) {
                        obj.html(html);
                    });
                    console.log(searchCount)
                    if (categoryChangeFlag) {
                        $("#riskSubCategoryCode").val(riskSubCategoryCode)
                    }
                    categoryChangeFlag = false
                    form.render();
                },
                error: function (e) {
                    console.info(e);
                }
            });
        }

        var lastDefaultMonth = getMonthAll()[0].substr(-2, 2)
        if (lastDefaultMonth.indexOf('0') != -1) {
            lastDefaultMonth = lastDefaultMonth.substr(-1, 1)
            if (lastDefaultMonth == 1) {
                lastDefaultMonth = 12
            } else {
                lastDefaultMonth = Number(lastDefaultMonth) - 1
            }

        } else {
            lastDefaultMonth = Number(lastDefaultMonth) - 1
        }

        if (lastDefaultMonth < 10) {
            lastDefaultMonth = getMonthAll()[0].substr(0, 4) + '0' + lastDefaultMonth
        } else {
            lastDefaultMonth = getMonthAll()[0].substr(0, 4) + lastDefaultMonth
        }

        // 诊断立项期间
        laydate.render({
            elem: "#initiationTime",
            range: true,
            type: 'month',
            value: projectDateStart + ' - ' + projectDateEnd,
            format: 'yyyyMM',
            done: function (val) {

            }
        });
        //诊断截至账期
        laydate.render({
            elem: "#date1",
            range: false,
            type: 'month',
            format: 'yyyyMM',
            value: diagnosisPeriod,
            btns: ['now', 'confirm'],
            done: function (val) {

            }
        });
        // 诊断会计期间
        laydate.render({
            elem: "#date2",
            range: true,
            type: 'month',
            format: 'yyyyMM',
            isInitValue: true,
            value:accountPeriodStart?accountPeriodStart + ' - ' + accountPeriodEnd:'',
            showBottom: true, //关闭底部框 去掉取消、确定、清空按钮
            done: function (val) {
            }
        });

        function getNowFormatDate(pre) {
            let date = new Date()
            let year = date.getFullYear() - pre
            let month = date.getMonth() + 1
            if (month < 10) month = '0' + month // 如果月份是个位数，在前面补0
            return year + '-' + month
        }

        function getMonthAll() {
            let end = getNowFormatDate(0)
            let begin = getNowFormatDate(1)
            var d1 = begin;
            var d2 = end;
            var dateArry = new Array();
            var s1 = d1.split("-");
            var s2 = d2.split("-");
            var mCount = 0;
            if (parseInt(s1[0]) < parseInt(s2[0])) {
                mCount = (parseInt(s2[0]) - parseInt(s1[0])) * 12 + parseInt(s2[1]) - parseInt(s1[1]) + 1;
            } else {
                mCount = parseInt(s2[1]) - parseInt(s1[1]) + 1;
            }
            if (mCount > 0) {
                var startM = parseInt(s1[1]);
                var startY = parseInt(s1[0]);
                for (var i = 0; i < mCount; i++) {
                    if (startM < 12) {
                        dateArry[i] = startY + "" + (startM > 9 ? startM : "0" + startM);
                        startM += 1;
                    } else {
                        dateArry[i] = startY + "" + (startM > 9 ? startM : "0" + startM);
                        startM = 1;
                        startY += 1;
                    }
                }
            }
            dateArry.reverse()
            return dateArry;
        }

        function getNowDate() {
            let date = new Date()
            return date.getDate();
        }

        function getMonthArr(date, n) {
            let dateArr = [];
            let year = date.substr(0, 4);
            let month = date.substr(-2, 2);
            let m = Number(month);
            console.log(year)
            console.log(m)

            if (n < m) {
                //1.n<month的情况
                for (let i = m - n + 1; i <= month; i++) {
                    let m1 = i < 10 ? "0" + i : i;
                    dateArr.push(year + '' + m1);
                }
            } else {
                //2.n>month的情况
                for (let i = (12 - (n - m) + 1); i <= 12; i++) {
                    let m1 = i < 10 ? "0" + i : i;
                    dateArr.push((year - 1) + '' + m1);
                }
                for (let i = 1; i <= m; i++) {
                    let m1 = i < 10 ? "0" + i : i;
                    dateArr.push(year + '' + m1);
                }
            }
            return dateArr;

        }


        // 重置
        window.restFun = function () {
            $("#projectName").val("");
            $("#customerName").val("");
            $("#projectNumber").val("");
            $('#riskCategoryCode').val("")
            $('#riskSubCategoryCode').val("")
            $('#comName').val("")
            $('#otherside').val("")
            $('[name="type"][value="'+selectedValue+'"]').attr('checked', true)
            $('#initiationTime').val(projectDateStart + ' - ' + projectDateEnd)
            $('#date2').val(accountPeriodStart + ' - ' + accountPeriodEnd)
            $('#date1').val(diagnosisPeriod)


            if (selectedValue == "1") {
                $('#date2').attr('disabled', true)
                $('#date1').attr('disabled', false)
                $('#initiationTime').attr('disabled', false)
            } else {
                $('#date2').attr('disabled', false)
                $('#date1').attr('disabled', true)
                $('#initiationTime').attr('disabled', true)
            }


            form.render();
            riskProfileTable()
        };

        //reloadForm 查询
        window.reloadForm = function (type) {

            riskProfileTable()


        }


        // 项目详情
        window.riskProfileTable = function () {
            var colZ = [
                {
                    field: 'provName', title: '省分', align: 'center', width: '100',
                },
                {
                    field: 'areaName', title: '地市', align: 'center', width: '100',
                },

                {
                    field: 'periodName', title: '期间', align: 'center', width: '100',
                },
                {
                    field: 'comName', title: '公司名称', align: 'center', width: '200',
                },
                {
                    field: 'projectNumber', title: '项目编号', align: 'center', width: '150', templet: function (d) {
                        return '<div class="goClick ovflowHidden" onclick=goClickTableItem("' + d.projectNumber + '","' + d.periodName + '","' + d.projectDate + '")>' + d.projectNumber + '</div>';
                    }
                },
                {
                    field: 'projectName', title: '项目名称', align: 'center', width: '300', style: 'text-align: left;',
                },
                {
                    field: 'customerName', title: '客户名称', align: 'center', width: '250', style: 'text-align: left;',

                    templet: function (d) {
                        return '<div class="goClick ovflowHidden" title="'+d.customerName+'" onclick=goClickTableItem2("' + d.customerName + '","' + d.periodName + '")>' + d.customerName + '</div>';
                    }
                },
                {
                    field: 'projectDate', title: '立项日期', align: 'center', width: '120',
                },
                {
                    field: 'incomeTypeName',
                    title: '项目计收类型名称',
                    align: 'center',
                    width: '250',
                    style: 'text-align: left;',
                },
                {
                    field: 'contract', title: '合同号', align: 'center', width: '200',
                },
                {
                    field: 'contractAmount',
                    title: '收入合同含税总额',
                    align: 'center',
                    width: '150',
                    style: 'text-align: right;',
                    templet: function (d) {
                        return thousands(d.contractAmount);
                    }
                },
                {
                    field: 'contractAmountNotax',
                    title: '收入合同不含税总额',
                    align: 'center',
                    width: '150',
                    style: 'text-align: right;',
                    templet: function (d) {
                        return thousands(d.contractAmountNotax);
                    }
                },
                {
                    field: 'totalMargin', title: '总累计毛利率（%）', align: 'center', width: '150',
                },
                {
                    field: 'projectRevTotal',
                    title: '项目收入总额',
                    align: 'center',
                    width: '100',
                    style: 'text-align: right;',
                    width: '150',
                    templet: function (d) {
                        return thousands(d.projectRevTotal);
                    }
                },

                {
                    field: 'rev1Amount',
                    title: '项目收入总额其中：集成服务收入',
                    align: 'center',
                    style: 'text-align: right;',
                    width: '260',
                    templet: function (d) {
                        return thousands(d.rev1Amount);
                    }
                }, {
                    field: 'rev2Amount',
                    title: '项目收入总额其中：工程服务收入',
                    align: 'center',
                    style: 'text-align: right;',
                    width: '260',
                    templet: function (d) {
                        return thousands(d.rev2Amount);
                    }
                }, {
                    field: 'rev3Amount',
                    title: '项目收入总额其中：租赁服务收入',
                    align: 'center',
                    style: 'text-align: right;',
                    width: '260',
                    templet: function (d) {
                        return thousands(d.rev3Amount);
                    }
                }, {
                    field: 'rev4Amount',
                    title: '项目收入总额其中：设备销售收入',
                    align: 'center',
                    style: 'text-align: right;',
                    width: '260',
                    templet: function (d) {
                        return thousands(d.rev4Amount);
                    }
                },


                {
                    field: 'receivableAmount',
                    title: '本月末应收账款',
                    align: 'center',
                    width: '150',
                    style: 'text-align: right;',
                    templet: function (d) {
                        return thousands(d.receivableAmount);
                    }
                },
                {
                    field: 'cashAmount',
                    title: '已回款金额(含税)',
                    align: 'center',
                    width: '150',
                    style: 'text-align: right;',
                    templet: function (d) {
                        return thousands(d.cashAmount);
                    }
                },
                {
                    field: 'paidAmount',
                    title: '已付款金额(含税)（集成产互专用）',
                    align: 'center',
                    style: 'text-align: right;',
                    width: '240',
                    templet: function (d) {
                        return thousands(d.paidAmount);
                    }
                },
                {
                    field: 'proDate30Amount',
                    title: '立项起30日内计收金额（不含税）',
                    align: 'center',
                    style: 'text-align: right;',
                    width: '240',
                    templet: function (d) {
                        return thousands(d.proDate30Amount);
                    }
                },
                {
                    field: 'proDate30AmountRatio',
                    title: '立项起30日内计收比例',
                    align: 'center',
                    width: '180'
                },
                {
                    field: 'diff',
                    title: '首次计收日期与立项日期间隔天数',
                    align: 'center',
                    width: '240'
                },


                {
                    field: 'contractNo1',
                    title: '成本合同编号1',
                    align: 'center',
                    width: '215'
                },{
                    field: 'signDate1',
                    title: '成本合同1签订日期',
                    align: 'center',
                    width: '145'
                },{
                    field: 'otherSide1',
                    title: '供应商1',
                    align: 'center',
                    style: 'text-align: left;',
                    width: '240',
                    templet: function (d) {
                        return '<div class="goClick ovflowHidden" lay-event="otherSide1" title="'+d.otherSide1+'">' + d.otherSide1 + '</div>';
                    }
                },{
                    field: 'otherSideProv1',
                    title: '供应商1企业归属省',
                    align: 'center',
                    width: '145'
                },{
                    field: 'sum1',
                    title: '含增值税合同金额1',
                    align: 'center',
                    width: '145',
                    style: 'text-align: right;',
                    templet: function (d) {
                        return thousands(d.sum1);
                    }
                },

                {
                    field: 'contractNo2',
                    title: '成本合同编号2',
                    align: 'center',
                    width: '215'
                },{
                    field: 'signDate2',
                    title: '成本合同2签订日期',
                    align: 'center',
                    width: '145'
                },{
                    field: 'otherSide2',
                    title: '供应商2',
                    align: 'center',
                    style: 'text-align: left;',
                    width: '240',
                    templet: function (d) {
                        return '<div class="goClick ovflowHidden" lay-event="otherSide2" title="'+d.otherSide2+'">' + d.otherSide2 + '</div>';
                    }
                },{
                    field: 'otherSideProv2',
                    title: '供应商2企业归属省',
                    align: 'center',
                    width: '145'
                },{
                    field: 'sum2',
                    title: '含增值税合同金额2',
                    align: 'center',
                    width: '145',
                    style: 'text-align: right;',
                    templet: function (d) {
                        return thousands(d.sum2);
                    }
                },


                {
                    field: 'contractNo3',
                    title: '成本合同编号3',
                    align: 'center',
                    width: '215'
                },{
                    field: 'signDate3',
                    title: '成本合同3签订日期',
                    align: 'center',
                    width: '145'
                },{
                    field: 'otherSide3',
                    title: '供应商3',
                    align: 'center',
                    style: 'text-align: left;',
                    width: '240',
                    templet: function (d) {
                        return '<div class="goClick ovflowHidden" lay-event="otherSide3" title="'+d.otherSide3+'">' + d.otherSide3 + '</div>';
                    }
                },{
                    field: 'otherSideProv3',
                    title: '供应商3企业归属省',
                    align: 'center',
                    width: '145'
                },{
                    field: 'sum3',
                    title: '含增值税合同金额3',
                    align: 'center',
                    width: '145',
                    style: 'text-align: right;',
                    templet: function (d) {
                        return thousands(d.sum3);
                    }
                },

                {
                    field: 'contractNo4',
                    title: '成本合同编号4',
                    align: 'center',
                    width: '215'
                },{
                    field: 'signDate4',
                    title: '成本合同4签订日期',
                    align: 'center',
                    width: '145'
                },{
                    field: 'otherSide4',
                    title: '供应商4',
                    align: 'center',
                    style: 'text-align: left;',
                    width: '240',
                    templet: function (d) {
                        return '<div class="goClick ovflowHidden" lay-event="otherSide4" title="'+d.otherSide4+'">' + d.otherSide4 + '</div>';
                    }
                },{
                    field: 'otherSideProv4',
                    title: '供应商4企业归属省',
                    align: 'center',
                    width: '145'
                },{
                    field: 'sum4',
                    title: '含增值税合同金额4',
                    align: 'center',
                    width: '145',
                    style: 'text-align: right;',
                    templet: function (d) {
                        return thousands(d.sum4);
                    }
                },

                {
                    field: 'contractNo5',
                    title: '成本合同编号5',
                    align: 'center',
                    width: '215'
                },{
                    field: 'signDate5',
                    title: '成本合同5签订日期',
                    align: 'center',
                    width: '145'
                },{
                    field: 'otherSide5',
                    title: '供应商5',
                    align: 'center',
                    style: 'text-align: left;',
                    width: '240',
                    templet: function (d) {
                        return '<div class="goClick ovflowHidden" lay-event="otherSide5" title="'+d.otherSide5+'">' + d.otherSide5 + '</div>';
                    }
                },{
                    field: 'otherSideProv5',
                    title: '供应商5企业归属省',
                    align: 'center',
                    width: '145'
                },{
                    field: 'sum5',
                    title: '含增值税合同金额5',
                    align: 'center',
                    width: '145',
                    style: 'text-align: right;',
                    templet: function (d) {
                        return thousands(d.sum5);
                    }
                },
                {
                    field: 'requestAmount',
                    title: '本月计收金额（含税）',
                    align: 'center',
                    width: '165',
                    style: 'text-align: right;',
                    templet: function (d) {
                        return thousands(d.requestAmount);
                    }
                },
            ]

            if (riskSubCategoryCode) {
                $.ajax({
                    url: ctx + "/ictRisk/getCfgField",
                    dataType: "JSON",
                    type: "POST",
                    data: JSON.stringify({lev1Code: riskSubCategoryCode}),
                    contentType: "application/json;charset=UTF-8",
                    success: function (ret) {
                        if (ret.httpCode == 200) {
                            var retData = ret.data
                            retData.forEach(item => {
                                colZ.push({
                                    field: item.showField,
                                    title: item.showChnField,
                                    align: 'center',
                                    width: '250',
                                    style: 'text-align: right;'
                                })
                            })
                            getIctProjectDetail(colZ)
                        } else {
                            getIctProjectDetail(colZ)
                        }

                    },
                    error: function (e) {
                        console.info(e);
                    }
                });

            } else {
                getIctProjectDetail(colZ)
            }

        }


        window.getIctProjectDetail = function (colZ) {
            var initiationTime = $("#initiationTime").val() ? ($("#initiationTime").val()).split(" - ") : [];
            var provCode;
            var riskCategoryCode;
            var riskSubCategoryCode;
            var diagnosisPeriod = $('#date1').val();
            var date2 = $("#date2").val() ? ($("#date2").val()).split(" - ") : [];
            var type = $('input[name="type"]:checked').val();
            if (searchCount === 0) {
                riskCategoryCode = getUrlParam("riskCategoryCode");
                riskSubCategoryCode = getUrlParam("riskSubCategoryCode");
                provCode = getUrlParam("provCode");
                areaCode = getUrlParam("areaCode");
            } else {
                riskCategoryCode = $('#riskCategoryCode').val()
                riskSubCategoryCode = $('#riskSubCategoryCode').val()
                provCode = $('#provCode').val();
                areaCode = $('#areaCode').val();
            }
            searchCount = searchCount + 1
            table.render({
                elem: '#risk_profile_table',
                id: 'risk_profile_table',
                url: ctx + '/ict/getIctProjectDetail',
                where: {
                    type:type,
                    projectDateStart: initiationTime[0] || '',
                    projectDateEnd: initiationTime[1] || '',
                    accountPeriodStart: date2[0] || '',
                    accountPeriodEnd: date2[1] || '',
                    diagnosisPeriod: diagnosisPeriod || '',
                    riskCategoryCode: riskCategoryCode || '',
                    riskSubCategoryCode: riskSubCategoryCode || '',
                    provCode: provCode || '',
                    areaCode: areaCode || '',
                    projectNumber: $('#projectNumber').val(),
                    projectName: $('#projectName').val(),
                    customerName: $('#customerName').val(),
                    comName: $('#comName').val(),
                    otherside: $('#otherside').val()
                },
                page: true,
                limit: 10,
                cols: [colZ],
                done: function (res, curr, count) {
                }
            });
        }
        table.on('tool(risk_profile_table)', function(obj){
            var data = obj.data;
            var comName = ''
            closeTab("供应商合同信息");

            if (obj.event === 'otherSide1') {
                comName = data.otherSide1;
            }else if (obj.event === 'otherSide2') {
                comName = data.otherSide2;
            }else if (obj.event === 'otherSide3') {
                comName = data.otherSide3;
            }
                $("#tableMenu")
                .data("title", "供应商合同信息")
                .data(
                    "url",
                    '/views/audit/bigdata/ictDiagnosticEquipment/supplierContractList.html?comName=' + encodeURI(comName)).click();

        });
        //导出

        window.exportBtn = function () {
            var initiationTime = $("#initiationTime").val() ? ($("#initiationTime").val()).split(" - ") : [];
            var diagnosisPeriod = $('#date1').val();
            var indexD = layer.load(1, {
                shade: [0.1, "#fff"],
            });
            var date2 = $("#date2").val() ? ($("#date2").val()).split(" - ") : [];
            var type = $('input[name="type"]:checked').val();

            $.ajax({
                url: ctx + "/ict/exportIctProjectDetail",
                dataType: "JSON",
                type: "POST",
                contentType: "application/json;charset=UTF-8",
                data: JSON.stringify(
                    {
                        type:type,
                        projectDateStart: initiationTime[0] || '',
                        projectDateEnd: initiationTime[1] || '',
                        accountPeriodStart: date2[0] || '',
                        accountPeriodEnd: date2[1] || '',
                        diagnosisPeriod: diagnosisPeriod || '',
                        riskCategoryCode: $('#riskCategoryCode').val() || '',
                        riskSubCategoryCode: $('#riskSubCategoryCode').val() || '',
                        provCode: $('#provCode').val() || '',
                        areaCode: $('#areaCode').val() || '',
                        projectNumber: $('#projectNumber').val(),
                        projectName: $('#projectName').val(),
                        customerName: $('#customerName').val(),
                        comName: $('#comName').val(),
                        otherside: $('#otherside').val()
                    }
                ),
                success: function (ret) {
                    if (ret.httpCode == 200) {
                        layer.msg('系统已在后台导出处理，请您耐心等待，稍后请从“工作台--我的工具--模型导出下载”中进行文件下载。<br/>为提高服务器空间利用率，请您及时清理删除已下载的文件，谢谢您的配合！', {
                            time: 0
                            , icon: 6
                            , btn: ['确定']
                        });
                    } else {
                        frm.error(ret.msg);
                    }

                    form.render();
                    layer.close(indexD);
                },
                error: function (e) {
                    console.info(e);
                    layer.close(indexD);
                }
            });
        };

        window.goClickTableItem = function (projectNumber, periodName, projectDate) {


            closeTab("按项目编号汇总风险");
            $("#tableMenu")
                .data("title", "按项目编号汇总风险")
                .data(
                    "url",
                    '/views/audit/bigdata/ictDiagnosticEquipment/projectRisk.html?projectNumber=' + projectNumber + '&periodName=' + periodName + '&projectDate=' + projectDate).click();
        }




        window.goClickTableItem2 = function (customerName, periodName) {


            closeTab("按项目编号汇总风险");
            $("#tableMenu")
                .data("title", "按客户名称汇总风险")
                .data(
                    "url",
                    '/views/audit/bigdata/ictDiagnosticEquipment/projectkh.html?customerName=' + encodeURI(customerName) + '&periodName=' + periodName).click();
        }


        riskProfileTable()
    })


</script>

</body>
