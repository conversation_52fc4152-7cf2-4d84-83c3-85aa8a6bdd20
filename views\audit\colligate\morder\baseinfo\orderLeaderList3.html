<!-- 中台工单领导首页 -->
<!--#include virtual ="include/header.html"-->
<link href="resource/css/audit-welcome.css?v=6.5" rel="stylesheet">
<link href="resource/css/ordinary-welcome.css?v=6.5" rel="stylesheet">
<link href="resource/css/lead.css?v=6.5" rel="stylesheet">
<link href="resource/css/style-red.css?v=6.5" id="linkid" rel="stylesheet" type="text/css">
<link href="resource/css/new-style.css?v=6.5" media="all" rel="stylesheet" type="text/css">
<link href="resource/css/jacobforwecomejob.css?v=6.5" rel="stylesheet" type="text/css">
<link href="resource/css/audit/colligate/morder/baseinfo/orderLeaderList.css?v=6.5" rel="stylesheet" type="text/css">
<link href="resource/css/enterLine.css" media="all" rel="stylesheet" type="text/css">

<style>
    .layui-form-checkbox{position:relative;display:inline-block;vertical-align:middle;height:24px;line-height:24px;margin-right:5px;/* padding-right:30px;*/
        border:1px solid #d2d2d2;background-color:#fff;cursor:pointer;font-size:0;border-radius:4px;-webkit-transition:.1s linear;transition:.1s linear;box-sizing:border-box;width:24px;padding-right:0px;}

    .layui-form-checkbox i{width:24px;font-size:13px;}
    .layui-form-pane .layui-form-checkbox {
        margin:0 8px 0 0;
    }
    @media only screen and (min-width:1200px) and (max-width:1440px){
        .one-checkbox>label{font-size:12px;}
        .layui-form-checkbox{position:relative;display:inline-block;vertical-align:middle;height:20px;line-height:20px;margin-right:5px;/* padding-right:30px;*/
            border:1px solid #d2d2d2;background-color:#fff;cursor:pointer;font-size:0;border-radius:4px;-webkit-transition:.1s linear;transition:.1s linear;box-sizing:border-box;width:20px;padding-right:0px;}
        .layui-form-checkbox i{width:20px;font-size:12px;}
    }
    @media only screen and (min-width:1440px) and (max-width:1600px){
        .one-checkbox>label{font-size:12px;}
        .layui-form-checkbox{position:relative;display:inline-block;vertical-align:middle;height:20px;line-height:20px;margin-right:5px;/* padding-right:30px;*/
            border:1px solid #d2d2d2;background-color:#fff;cursor:pointer;font-size:0;border-radius:4px;-webkit-transition:.1s linear;transition:.1s linear;box-sizing:border-box;width:20px;padding-right:0px;}
        .layui-form-checkbox i{width:20px;font-size:12px;}
    }
  /*.ie-header{*/
 /*position:absolute;*/
 /*top:0;*/
 /*}
*/
 .layui-table-cell{/* padding:0 0 0 15px;*/
}
    .layui-fluid{
        padding:0 5px;
    }
    .topcon>div{
        padding-top:5px;
    }
.topcon{padding:0 10px;}
.layui-select-btn-span{margin-right:22px;}
.order4-top{width:100%;height:141px;background:#FFFFFF;border-radius:4px;padding:11px;box-sizing:border-box;}
.order4-top-li{width:50%;float:left;height:60px;padding:5px;box-sizing:border-box;}
.order4-top-li-bg{width:100%;height:100%;background:#F6F7F8;line-height:50px;padding:0 24px;box-sizing:border-box;display:flex;justify-content:space-between;}
.order4-top-li-left{font-size:15px;font-family:PingFangSC-Regular,PingFang SC;font-weight:400;color:#333333;overflow:hidden;}
.order4-top-li-right{font-size:20px;font-family:PingFangSC-Medium,PingFang SC;font-weight:500;color:#F5222D;}
.rightone{height:141px;padding-right:0 !important;background-image:url(resource/images/welcomefour1.png);background-size:100% 100%;box-shadow:0px 0px 10px 0px rgba(155,11,9,0.1);border-radius:4px;padding-top:15px;box-sizing:border-box;}
.manman{margin-top:90px;}
.orderTitle{padding-left:10px;}
.tplText{}
.tplNum{color:#333333;font-size:14px;width:15%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;display:flex;align-items:center;justify-content:flex-end;font-family:Helvetica;}
.achieveBox .layui-row{border-bottom-width:0;padding:8px 0;padding-right:32px;}
.achieveBoxF,.achieveBoxG{height:268px;}
.achieveBoxG .center-btn{padding:26px 0 6px;}
#container1,#container2,#container3{height:268px;}
.outCycle{margin-top:0;width:100%;height:100%;display: flex;align-items: center}
.middle-charts{width: calc(100% - 920px);margin:0px 0 0 4px;min-width:200px;height:36px;box-shadow:0px 0px 7px 0px rgba(0,0,0,0.09);border-radius:2px;}
.middleList{margin-left:20px;width:calc(100% - 346px);}
.overflowhidden-2{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;}
.middleList .layui-border-box.layui-table-view{margin-top:0 !important;}
.middleList .layui-table{margin:0}
.middleList .layui-table tr td{padding:2px 0px;}
.middleList .layui-table tr td .layui-table-cell{height:45px;display:flex;align-items:center;overflow:hidden;text-overflow:inherit;white-space:initial;box-sizing:border-box;font-size:14px;font-family:PingFangSC-Regular,PingFang SC;font-weight:400;color:#333333;}
.middleList .layui-table-header th,.middleList .layui-table-header{background-color:#f5f8fc !important;}
.middleList .layui-table th,.layui-table td,.layui-table[lay-skin="line"],.layui-table[lay-skin="row"],.layui-table-view,.layui-table-header,.layui-table-tool,.layui-table-page,.layui-table-fixed-r,.layui-table-tips-main{border-color:#E6EAEE !important;}
.middleList thead .layui-table-cell{font-weight:initial;color:#000;}
.middleList .layui-table tbody tr:hover,.middleList .task-todo.html .layui-table-hover,.middleList .layui-table-click{background:#fff;}
.preliminary_assessment{width:100%;}
.preliminary_assessment li{width:100%;}
.preliminary_assessment li .content{display:inline-block;width:calc(100% - 100px);overflow:hidden;height:20px;line-height:20px;}
.preliminary_assessment li .span1,.preliminary_assessment li .span2{font-size:13px;overflow:hidden;font-family:PingFangSC-Regular,PingFang SC;font-weight:400;display:inline-block;line-height:20px;padding-left:7px;box-sizing:border-box;width:60px;height:20px;border-radius:1px;}
.preliminary_assessment li .span1{color:#FF8404;background:rgba(255,176,94,0.1);}
.preliminary_assessment li .span2{color:#238CDB;background:rgba(111,154,205,0.1);}
.flex-header{display:flex;align-items:center;justify-content:space-between;}
.flex-header .width-span{width:74px;}
.flex-header .order-button-update{width:74px;text-align:right;}
.flex-header .layui-form-radioed>i,.flex-header .layui-form-radio>i:hover{color:#f5222d;}
.flex-header .layui-form-radio *{display:inline-block;vertical-align:middle;font-weight:400;}
.left-table-height{height:300px;}
.order-4-table{width:100%;padding:8px 0;box-sizing:border-box;}
.order-4-table .order-4-table-header{width:100%;height:44px;background:#F4F8FC;border-bottom:1px solid #E6EAEE;}
.order-4-table .order-4-table-th{float:left;width:20%;height:44px;line-height:44px;font-size:14px;font-family:PingFangSC-Medium,PingFang SC;font-weight:bold;box-sizing:border-box;color:#333333;}
.order-4-table-th1{padding-left:38px;width:22% !important;}
.order-4-table-th2{padding-left:38px;}
.order-4-table-th3{padding-left:38px;width:15% !important;}
.order-4-table-th4{padding-right:38px;text-align:right;width:15% !important}
.order-4-table-th5{padding-right:38px;text-align:right;width:15% !important}
.order-4-table-th6{padding-right:38px;text-align:right;width:12% !important}
.order-4-table .order-4-table-body{height:240px;overflow-y:auto;width:100%;}
.order-4-table .order-4-table-tr{border-bottom:1px solid #E6EAEE;width:100%;}
.order-4-table .order-4-table-td{float:left;width:20%;height:44px;line-height:44px;font-size:14px;font-family:PingFangSC-Medium,PingFang SC;font-weight:500;box-sizing:border-box;color:#333333;}
#personChart,#achievesearchChart{padding:8px 0;}
.person-header{font-size:13px;font-weight:bold;text-align:center;height:44px;background:#f5f8fc;line-height:44px;}
#personChart1 .layui-row:nth-child(even),#personChart2 .layui-row:nth-child(even),#achievesearchChart1 .layui-row:nth-child(even),#achievesearchChart2 .layui-row:nth-child(even){background:#fff;}
.order-4-jifen .layui-row.layui-row-check{width:100%;margin-top:6px;box-sizing:border-box;border:1px solid #E6EAEE;}
.person-data{font-size:13px;height:36px;line-height:36px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;}
.layui-card-header .layui-select-brn1{width:calc(100% - 148px);text-align:center;}
.layui-card-header .layui-select-brn2{width:calc(100% - 252px);text-align:center;}
.order4-top-bg-1{background:url('resource/images/bigdata/fybg.png') center center no-repeat;background-size:100% 100%;border-radius:6px;width:100%;height:100%;padding:20px 25px;display:flex;align-items:center;box-sizing:border-box;justify-content:space-between;}
.order4-top-bg-2{border-radius:6px;width:100%;height:100%;padding:20px 25px;display:flex;align-items:center;box-sizing:border-box;justify-content:space-between;border:1px solid #FF6E6E;}
.items-project-one{margin-right:0px;display:flex;flex-direction:column;align-items:center;justify-content:center;}
.items-project-one:last-child{margin-right:0px;}
.items-project-one .items-project-one-top{font-size:16px;font-family:PingFangSC-Regular,PingFang SC;font-weight:400;color:#333333;line-height:22px;text-align:center;}
.items-project-one .items-project-one-bottom{font-size:28px;font-family:PingFangSC-Medium,PingFang SC;font-weight:500;color:#333333;line-height:40px;margin-top:8px;text-align:center;}
.order4-top-bg-1 .iconfont{color:#F5222D;font-size:24px;position:relative;top:15px;left:-15px;}
.icon-line{width:1px;height:41px;background-color:#FF6E6E;}
.header-btn-1{height:28px;background:#FCF8F8;border:1px solid #F5222D;border-radius:2px;line-height:28px;padding:0px 12px;font-size:14px;font-family:PingFangSC-Regular,PingFang SC;font-weight:400;color:#F5222D;box-sizing:border-box;margin-left:10px;margin-right:12px;cursor:pointer;}
.header-btn-2{height:28px;background:#FCF8F8;border:1px solid #F5222D;border-radius:2px;line-height:28px;padding:0px 12px;font-size:14px;font-family:PingFangSC-Regular,PingFang SC;font-weight:400;color:#F5222D;box-sizing:border-box;cursor:pointer;}
.header-btn-1.actived,.header-btn-2.actived{height:28px;background:#F5222D;border-radius:2px;line-height:28px;padding:0px 12px;font-size:14px;font-family:PingFangSC-Regular,PingFang SC;font-weight:400;color:#FFFFFF;cursor:pointer;}
#btns-group{display:flex;align-items:center;margin-right:25px;}
#btns-group .header-btn-2{margin-right:12px;}
#btns-group .header-btn-2:last-child{margin-right:0px;}
.stars{font-size:18px;width:100%;}
.icon-xingxing1{font-size:16px;color:#FFB955;}
.layui-table tbody tr:first-child>td>div{text-align:center !important;width:100% !important;}
.layui-table tbody tr:first-child>td>div>div{text-align:center !important;width:100% !important;}
.layui-table tbody tr:first-child>td>div{height:45px!important;}
td[data-field='initialFocusRiskProv']>div{display:block!important;padding:0px!important;}
td[data-field='initialFocusRiskProv']>div div{padding:0px 10px;border-bottom:1px solid #E6EAEE !important;height:48px;line-height:22px;text-align:left;text-overflow:-o-ellipsis-lastline;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;}
td[data-field='initialFocusRiskProv']>div div:last-child{border-bottom:none!important;}
td[data-field='initialFocusRiskProv'] .initialFocusRiskProv{    display: flex;align-items: center;justify-content: center;}
td[data-field='initialFocusRisk']>div{display:block!important;padding:0px!important;}
td[data-field='initialFocusRisk']>div div{padding:0px 10px;border-bottom:1px solid #E6EAEE !important;height:48px;line-height:22px;text-align:left;text-overflow:-o-ellipsis-lastline;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;}
td[data-field='initialFocusRisk']>div div:last-child{border-bottom:none!important;}
@media only screen and (min-width:1440px) and (max-width:1600px){.items-project-one .items-project-one-top{font-size:14px;}
.items-project-one .items-project-one-bottom{font-size:20px;}
.middle-charts{width: calc(100% - 805px)}
}
@media only screen and  (max-width:1440px){
        .middle-charts{width: calc(100% - 822px)}
    .items-project-one .items-project-one-top{
        font-size: 13px;
    }
    .items-project-one .items-project-one-bottom{
        font-size: 24px;
    }
}
.layui-layer-content-my{
    background: none;
    padding: 0;
    box-shadow: 0 1px 6px rgba(0,0,0,.1);
    position: relative;
    line-height: 22px;
    min-width: 12px;
    font-size: 12px;
    _float: left;
    border-radius: 4px;
    color: #fff;
    position: absolute;
}
.layui-table-tips-main-my{
    max-height: 150px;
    padding: 8px 15px;
    padding-right: 18px;
    font-size: 14px;
    overflow-y: scroll;
    background-color: #fff;
    color: #333;
    border-color: #dcdcdc;
    border-width: 1px;
    border-style: solid;
    margin-top: -10px;
}
.layui-table-tips-main-my .icon-guanbishixin{
    position: absolute;
    right: -6px;
    top: -14px;
    color:#666;
    font-size: 24px;
    cursor: pointer
}
 .one-row{display:flex;align-items:center;}
  .one-row-left{height:32px;line-height:32px;width:80px;text-align:right;}
  .one-row:last-child{position:relative;}
    .outCycleBox{
        font-weight: lighter;
        display: inline-block;
        margin: 0 10px;
    }
    .outCycleTitle{
        display: inline-block;
        font-size: 16px;
        color:#666;
    }
    .outCycleProp{
        display: inline-block;
        font-size: 16px;
        color:#f5222d;
    }
    @media only screen and  (max-width:1440px){
        .outCycleBox{
            margin: 0 10px;
        }
        .outCycleTitle{
            font-size: 12px;
        }
        .outCycleProp{
            font-size: 12px;
        }
    }
</style>

<body class="new-audit-fluid">
    <div class="layui-fluid larry-wrapper">
        <div class="tab-menu">
            <a data-parent="true" href="javascript:" id="tableMenu" style="color: #c00;text-decoration:underline;">
                <i class="iconfont" data-icon=""></i>
            </a>
        </div>
        <div class="layui-row layui-col-space20 topcon">
            <!-- 新修改内容 -->
            <div class="layui-col-lg3 layui-col-sm3">
                <div class="order4-top">
                    <div class="order4-top-bg-1">
                        <div class="items-project-one">
                            <div class="items-project-one-top">专家团队</div>
                            <a href = "javaScript:viod(0)" onclick=groupMore('8')>
                                <div class="items-project-one-bottom" id="zhuanjia">0</div>
                            </a>
                        </div>
                        <span class="icon iconfont icon-ic_join_dialing_norm"></span>
                        <div class="items-project-one">
                            <div class="items-project-one-top">监控中心</div>
                            <a href = "javaScript:viod(0)" onclick=groupMore('1')>
                                <div class="items-project-one-bottom" id="jiankong">0</div>
                            </a>
                        </div>
                        <span class="icon iconfont icon-ic_join_dialing_norm"></span>
                        <div class="items-project-one">
                            <div class="items-project-one-top">课题团队</div>
                            <a href = "javaScript:viod(0)" onclick=groupMore('2')>
                                <div class="items-project-one-bottom" id="fengyan">0</div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-col-lg3 layui-col-sm3">
                <div class="order4-top">
                    <div class="order4-top-bg-2" style="    background: #FFFBFB;">
                        <div class="items-project-one">
                            <div class="items-project-one-top">专题</div>
                            <div class="items-project-one-bottom" id="topicNum"  style="cursor: pointer;" onclick="openTopic()">0</div>
                        </div>
                        <span class="icon-line"></span>
                        <div class="items-project-one">
                            <div class="items-project-one-top">子专题</div>
                            <div class="items-project-one-bottom" id="subTopicNum" style="cursor: pointer;" onclick="openSubTopic()">0</div>
                        </div>
                        <span class="icon-line"></span>
                        <div class="items-project-one">
                            <div class="items-project-one-top">聚焦风险</div>
                            <a href="javaScript:void(0)" onclick="achieveMore('','')">
                                <div class="items-project-one-bottom" id="riskNum">0</div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-col-lg3 layui-col-sm3">
                <div class="order4-top">
                    <div class="order4-top-bg-2" style="border-color: #6ECCFF; background: #FBFCFF;">
                        <div class="items-project-one">
                            <div class="items-project-one-top">触发项目</div>
                            <div class="items-project-one-bottom" id="xiangMu" style="cursor: pointer;"
                                onclick="openCfxm()">0</div>
                        </div>
                        <span class="icon-line" style="background-color: #6ECCFF;"></span>
                        <div class="items-project-one">
                            <div class="items-project-one-top">审计通报</div>
                            <div class="items-project-one-bottom" id="tongBao" style="cursor: pointer;"
                                onclick="openSjtb()">0</div>
                        </div>
                        <span class="icon-line" style="background-color: #6ECCFF;"></span>
                        <div class="items-project-one">
                            <div class="items-project-one-top">发现问题</div>
                            <div class="items-project-one-bottom" id="wenTi" style="cursor: pointer;"
                                onclick="openFxwt()">0</div>
                        </div>
                        <span class="icon-line" style="background-color: #6ECCFF;"></span>
                        <div class="items-project-one">
                            <div class="items-project-one-top">积分汇总</div>
                            <div class="items-project-one-bottom" id="score" style="cursor: pointer;"
                                 onclick="openScore('','','','','')">0</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-col-lg3 layui-col-sm3">
                <div class="rightone pl20">
                    <div class="trtTitle">选择账期区间</div>
                    <div class="trm layui-form radioTime">
                        <span class="trmTitle">快速选择</span>
                        <input lay-filter="quickchoose" name="quickchoose" title="去年" type="radio" value="2">
                        <input lay-filter="quickchoose" name="quickchoose" title="本年" type="radio" value="1">
                        <input lay-filter="quickchoose" name="quickchoose" title="自定义" type="radio" value="0">
                    </div>
                    <div class="trb ">
                        <span class="trbTitle">自定义</span>
                        <i class="iconfont">&#xe719;</i>
                        <input class="layui-input" disabled="disabled" id="payment" lay-filter="payment" name="payment"
                            placeholder="">
                    </div>
                </div>
            </div>
        </div>

        <div style="clear: both;"></div>

        <div class="layui-row layui-col-space10 order-echarts">
            <div style="float:left;width: 50%;box-sizing: border-box;">
                <div class="layui-card">
                    <div class="layui-card-header main-list-header task-title-style">专题/成果分布
                        <!-- 刷新-->
                        <button class="order-button order-button-update" layui-type="1">
                            <i class="iconfont index-icon">&#xe631;</i>
                        </button>
                    </div>
                    <div class="layui-card-div">
                        <div class="layui-col-md12 layui-col-sm12" id="container1"></div>
                    </div>
                </div>
            </div>
            <div style="float:left;width: 25%;box-sizing: border-box;">
                <div class="layui-card">
                    <div class="layui-card-header main-list-header task-title-style">
                        <div class="layui-select-brn" id="earlyType">
                            <div class="active layui-select-btn-span" layui-type="0">积分排序</div>
                            <div class="layui-select-btn-span" layui-type="1">成果排序</div>
                        </div>
                        <button class="order-button order-button-more" onclick="sortDetailed()">
                            <i class="iconfont index-icon">&#xe689;</i>
                        </button>
                    </div>
                    <div class="layui-card-div achieveBoxF" id="hasDataDiv">
                        <div class="achieveBox" id="achieveBox"></div>
                    </div>
                    <div class="layui-card-div achieveBoxF" id="noDataDiv" style="display: none">
                        <div class="middleList">
                            <img alt="" class="manman" src="resource/images/manman.jpg" style="width: 50%;">
                            <img alt="" class="manbb" src="resource/images/manbb.jpg" style="width: 100%">
                        </div>
                    </div>
                </div>
            </div>
            <div style="float:left;width: 25%;box-sizing: border-box;">
                <div class="layui-card">
                    <div class="layui-card-header main-list-header task-title-style">
                        中控
                    </div>
                    <div class="layui-card-div achieveBoxG layui-row">
                        <div class="layui-col-md4 layui-col-sm4">
                            <div class="center-btn" onclick="openOrderCommOnlist(10)">
                                <img alt=""  onclick="openOrderCommOnlist(10)"  src="resource/images/center/ztBtn1.png">
                            </div>
                            <span>成果应用</span>
                        </div>
                        <div class="layui-col-md4 layui-col-sm4" >
                            <div class="center-btn"  onclick="openOrderCommOnlist(11)">
                                <img alt=""  src="resource/images/center/ztBtn2.png">
                            </div>
                            <span onclick="openOrderCommOnlist(11)">通报管理</span>
                        </div>
                        <div class="layui-col-md4 layui-col-sm4">
                            <div class="center-btn" onclick="openOrderCommOnlist(2)">
                                <img alt="" src="resource/images/center/ztBtn3.png">
                            </div>
                            <span onclick="openOrderCommOnlist(2)">审计报告</span>
                        </div>
                        <div class="layui-col-md4 layui-col-sm4">
                            <div class="center-btn" onclick="openOrderCommOnlist(5)">
                                <img alt="" src="resource/images/center/ztBtn5.png">
                            </div>
                            <span onclick="openOrderCommOnlist(5)">提问回答</span>
                        </div>

                        <div class="layui-col-md4 layui-col-sm4">
                            <div class="center-btn" onclick="selectAllResultsStatistic()">
                                <img alt="" src="resource/images/center/ztBtn4.png">
                            </div>
                            <span onclick="selectAllResultsStatistic()">全国汇总</span>
                        </div>
                        <div class="layui-col-md4 layui-col-sm4">
                            <div class="center-btn" onclick="selectProvinceResultsStatistic()">
                                <img alt="" src="resource/images/center/ztBtn6.png">
                            </div>
                            <span onclick="selectProvinceResultsStatistic()">省分维度</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row layui-col-space10">
            <!-- 应用要素成果 -->
            <div class="layui-col-lg12 layui-col-sm12">
                <div class="layui-card main-list-card">
                    <div class="layui-card-header main-list-header task-title-style"
                        style="display:flex;align-items: center;">风研成果
                        <div class="middle-charts">
                            <div id="outCycle" class="outCycle"></div>
                        </div>
                        <div style="flex:1"></div>
                            <div class="one-row" style="font-weight:normal">
                                <div class="one-row-left">审计成果：</div>
                                <div class="row-table layui-form" style="display: flex;">
                                    <div class="one-checkbox">
                                        <label>风险预警单</label>
                                        <input type="checkbox" name="sjcg" value="warning" lay-filter="sjcg"
                                               class="checkboxs">
                                    </div>

                                    <div class="one-checkbox">
                                        <label>触发项目</label>
                                        <input type="checkbox" name="sjcg" value="project" lay-filter="sjcg"
                                               class="checkboxs">
                                    </div>

                                    <div class="one-checkbox">
                                        <label>审计通报</label>
                                        <input type="checkbox" name="sjcg" value="tongbao" lay-filter="sjcg"
                                               class="checkboxs">
                                    </div>

                                    <div class="one-checkbox">
                                        <label>发现问题</label>
                                        <input type="checkbox" name="sjcg" value="wenti" lay-filter="sjcg"
                                               class="checkboxs">
                                    </div>

                                    <div class="one-checkbox">
                                        <label>产生积分</label>
                                        <input type="checkbox" name="sjcg" value="generate" lay-filter="sjcg"
                                               class="checkboxs">
                                    </div>
                                </div>
                            </div>

                        <!-- 刷新-->
                        <a class="order-button order-button-update" href="javascript:;" layui-type="3">
                            <i class="iconfont index-icon">&#xe631;</i>
                        </a>
                        <!--更多-->
                        <button class="order-button order-button-more" onclick="achieveMore2('','')">
                            <i class="iconfont index-icon">&#xe689;</i>
                        </button>
                    </div>
                    <div class="layui-card-body main-news-body">
                        <div class="middleBox" style="height: 385px;box-sizing: border-box">


                            <div class="middleList" id="middleList">
                                <table class="layui-table jq-even" id="middleListTable" lay-filter="middleList"></table>
                            </div>
                            <div class="middleList noList">
                                <img alt="" class="manman" src="resource/images/manman.jpg">
                                <span class="middleText">暂无风研成果...</span>
                                <img alt="" class="manbb" src="resource/images/manbb.jpg">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row layui-col-space10">
            <!--小组看板-->
            <div class="groupGround layui-col-md6 layui-col-sm6">
                <div class="layui-card">
                    <div class="layui-card-header main-list-header task-title-style flex-header">
                        <span class="width-span ie-header">团队看板</span>
                        <div class="layui-select-brn1 layui-form">
                            <input lay-filter="loadGroup" name="loadGroup" title="专家团队" type="radio" checked value="8">
                            <input lay-filter="loadGroup" name="loadGroup" title="监控中心" type="radio" value="1">
                            <input lay-filter="loadGroup" name="loadGroup" title="课题团队" type="radio" value="2">
                        </div>
                        <button class="order-button order-button-update" layui-type="4">
                            <i class="iconfont index-icon">&#xe631;</i>
                        </button>
                        <!--更多-->
                        <button class="order-button order-button-more" onclick="groupMore()">
                            <i class="iconfont index-icon">&#xe689;</i>
                        </button>
                    </div>
                    <div class="layui-row layui-col-space10 left-table-height">
                        <div class="order-4-table">
                            <div class="order-4-table-header layui-row">
                                <div class="order-4-table-th order-4-table-th1">团队名称</div>
                                <div class="order-4-table-th order-4-table-th2">业务领域</div>
                                <div class="order-4-table-th order-4-table-th3">组长</div>
                                <div class="order-4-table-th order-4-table-th4">团队人数</div>
                                <div class="order-4-table-th order-4-table-th5">风研成果</div>
                                <div class="order-4-table-th order-4-table-th6">积分数</div>
                            </div>
                            <div class="order-4-table-body" id="group_table">

                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--成就值排行榜-->
            <div class="layui-col-md6 layui-col-sm6">
                <div class="layui-card">
                    <div class="layui-card-header main-list-header task-title-style flex-header">
                        <span class="width-span ie-header" style="width: 126px">积分排行榜</span>
                        <div class="layui-select-brn2 layui-form">
                            <input lay-filter="selectBtn" name="selectBtn" title="单位" checked type="radio" value="prov">
                            <input lay-filter="selectBtn" name="selectBtn" title="人员" type="radio" value="person">
                        </div>
                        <div style="width: 126px">
                            <!--更多-->
                            <button class="order-button order-button-more" onclick="achieveValuesMore()">
                                <i class="iconfont index-icon">&#xe689;</i>
                            </button>
                            <!-- 刷新-->
                            <button class="order-button order-button-update" layui-type="5">
                                <i class="iconfont index-icon">&#xe631;</i>
                            </button>
                        </div>

                    </div>
                    <div style="height:300px;overflow: hidden;" class="order-4-jifen">
                        <div class="layui-row" id="personChart" style="height: 300px;width:100%;">
                            <div class="layui-col-md6 layui-col-sm6 layui-col-lg6" style="padding-right:5px">
                                <div class="layui-row">
                                    <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-header">单位</div>
                                    <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-header">积分</div>
                                    <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-header">涉及人员数</div>
                                    <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-header">平均分</div>
                                </div>
                                <div id="personChart1"></div>
                            </div>
                            <div class="layui-col-md6 layui-col-sm6 layui-col-lg6" style="padding-left:5px">
                                <div class="layui-row">
                                    <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-header">单位</div>
                                    <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-header">积分</div>
                                    <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-header">涉及人员数</div>
                                    <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-header">平均分</div>
                                </div>
                                <div id="personChart2"></div>
                            </div>
                        </div>
                        <div class="layui-hide layui-row" id="achievesearchChart" style="height: 300px;width:100%;">
                            <div class="layui-col-md6 layui-col-sm6 layui-col-lg6 " style="padding-right:5px">
                                <div class="layui-row">
                                    <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-header">姓名</div>
                                    <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-header">省分</div>
                                    <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-header">积分</div>
                                    <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-header">风研成果</div>
                                </div>
                                <div id="achievesearchChart1"></div>
                            </div>
                            <div class="layui-col-md6 layui-col-sm6 layui-col-lg6" style="padding-left:5px">
                                <div class="layui-row">
                                    <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-header">姓名</div>
                                    <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-header">省分</div>
                                    <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-header">积分</div>
                                    <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-header">风研成果</div>
                                </div>
                                <div id="achievesearchChart2"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--发布任务-->
        <div class="eject-task" id="openMorder" style="display: none">
            <p class="task-title">-请选择您的任务-</p>
            <div class="task-box">
                <div class="task-btn1" onclick="taskAdd(2)"></div>
                <div class="task-btn2" onclick="taskAdd(1)"></div>
            </div>
        </div>
    </div>
    <div class="layui-layer-content-my" style="display: none;">
        <div class="layui-table-tips-main-my">
            <div class="text-left" id="lastRisk"></div>
            <span class="icon iconfont icon-guanbishixin" onclick="closeRisk()"></span>
        </div>
    </div>
</body>

<!--#include virtual ="include/version.html"-->
<script src="resource/js/jsencrypt/index.js?v=6.5" type="text/javascript"></script>
<script src="resource/js/jsencrypt/jsencrypt.js?v=6.5" type="text/javascript"></script>
<script src="resource/js/common.js?v=11.04" type="text/javascript"></script>
<script src="resource/js/lib/ueditor/third-party/jquery-ueditor.js?v=6.5" type="text/javascript"></script>
<script src="resource/js/audit/pro/common/common.js?v=6.5" type="text/javascript"></script>
<script src="resource/js/audit/colligate/morder/baseinfo/mobaseinfo.js?v=6.5" type="text/javascript"></script>
<script src="resource/js/echarts/echarts.min.js?v=6.5" type="text/javascript"></script>
<script src="resource/js/audit/online/onlineModel/echartsText/infographic.js?v=6.5" type="text/javascript"></script>
<script src="resource/js/audit/online/onlineModel/echartsText/macarons.js?v=6.5" type="text/javascript"></script>
<script src="resource/js/audit/colligate/morder/common/updateAnimation.js?v=6.5" type="text/javascript"></script>
<script src="resource/js/preDealTimeIndex.js" type="text/javascript"></script>
<!--成就值排序-->
<script id="achieveSortTpl" type="text/html">
    {{#  layui.each(d, function(index, item){ }}
    <div class="layui-row">
        <div class="tplText">
      <span>
        {{# if(index <= 2) { }}
        <img src="resource/images/model/rankings_{{index+1}}.png" class="bestImg" alt="">
        {{# } else { }}
        <span class='rankNum'>{{index+1}}</span>
        {{#} }}
      </span>
            <!-- cursor udl -->
            <span class="orderTitle "
            >{{item.GROUP_NAME}}</span>
        </div>
        <a class="tplNum">
            {{#if(item.NUM){ }}
                <a href="javaScript:void(0)" onclick="achieveMore('{{item.GROUP_ID}}','')">{{item.NUM}}</a>
            {{#}else{ }}
                <a href="javaScript:void(0)" onclick="openScore('{{item.GROUP_NAME}}','','','','')">{{item.SCORE}}</a>
            {{# } }}

        </div>
    </div>
    {{# }); }}
</script>
<!--应用成果列表-->
<script id="applyListTpl" type="text/html">
    <div class="middleListLine"></div>
    <div class="middleItem header layui-row">
        <div class="middleILName ovflowHidden" style="padding-left: 10px;">
            <span class="applyTitle">应用名称</span>
        </div>
        <div class="middleIL ovflowHidden">
            <span class="applyTitle">成果名称</span>
        </div>
        <div class="middleIL ovflowHidden" style="text-align: left">
            <span class="applyTitle">所属任务</span>
        </div>
        <div class="middleIS ovflowHidden" style="text-align: left">
            <span class="applyTitle">应用类型</span>
        </div>
        <div class="middleIS ovflowHidden" style="text-align: left">
            <span class="applyTitle">所属小组</span>
        </div>
    </div>
    {{#  layui.each(d, function(index, item){ }}
    <div class="middleItem middleItemContent layui-row">
        <div class="middleILName ovflowHidden pl10">
            {{# if ( item.objectName ) { }}
            <span class="iconfont icon-color1">&#xe698;</span>
            {{# } }}
            <span class="applyTextF cursorred" title="{{item.objectName}}"
                  onclick="openDetailModal('{{item.objectViewUrl}}','{{item.objectName}}','{{item.panelWidth}}','{{item.panelHeight}}')">{{item.objectName}}</span>
        </div>
        <div class="middleIL ovflowHidden">
            {{# if ( item.resultName ) { }}
            <span class="iconfont icon-color2">&#xe7bb;</span>
            {{# } }}
            <span class="applyText" title="{{item.resultName}}">{{item.resultName}}</span>
        </div>
        <div class="middleIL ovflowHidden" style="text-align: left">
            {{# if ( item.orderName ) { }}
            <span class="iconfont icon-color3">&#xe6f8;</span>
            {{# } }}
            <span class="applyText" title="{{item.orderName}}">{{item.orderName}}</span>
        </div>
        <div class="middleIS ovflowHidden" style="text-align: left">
            {{# if ( item.applyTypeName ) { }}
            <span class="iconfont icon-color4">&#xe769;</span>
            {{# } }}
            <span class="applyText" title="{{item.applyTypeName}}">{{item.applyTypeName}}</span>
        </div>
        <div class="middleIS ovflowHidden" style="text-align: left">
            {{# if ( item.groupName ) { }}
            <span class="iconfont icon-color5">&#xe795;</span>
            {{# } }}
            <span class="applyText" title="{{item.groupName}}">{{item.groupName}}</span>
        </div>
    </div>
    {{# }); }}
</script>
<!--要素成果列表-->
<script id="elementListTpl" type="text/html">
    {{#  layui.each(d, function(index, item){ }}
    <div class="elementItem">
        <div class="eITop">
            <div class="eITF">
                <span class="aTF">成果名称</span>
                <span class="applyTextF cursorred"
                      onclick="openDeailMorderInfo('{{item.resultDetailId}}', '{{item.resultTypeCode}}')"
                      title="{{item.resultName}}">{{item.resultName}}</span>
            </div>
            <div class="eITS">
                <span>所属任务:</span>
                <span class="eITSC" title="{{item.orderTitle}}">{{item.orderTitle}}</span>
            </div>
            <div class="groupLeader">
                <span>组长:</span>
                <span class="groupLeaderC">{{item.groupLeaderName}}</span>
            </div>
        </div>
        <div class="eIBottom">
            {{# layui.each(item.applyNumberVoList, function(indexIn, itemIn){ }}
            <div>
                <span>{{itemIn.statisticsName}}:</span>
                <span class="eIBottomNum cursorred" style="background-color:{{itemIn.statisticsColor}};color:#fff"
                      onclick="cgUseNumber('{{itemIn.statisticsCode}}','{{item.resultId}}')">{{itemIn.statisticsValue}}</span>
            </div>
            {{# }); }}
        </div>
    </div>
    {{# }); }}
</script>

<!--聚焦风险-->
<script id="INITIAL_FOCUS_RISK" type="text/html">
    <span title="{{d.INITIAL_FOCUS_RISK}}" class="overflowhidden-2"> {{ d.INITIAL_FOCUS_RISK }}</span>
</script>
<!--SUB_TOPIC_NAME-->
<script id="SUB_TOPIC_NAME" type="text/html">
    <span title="{{d.SUB_TOPIC_NAME}}" class="overflowhidden-2"> {{ d.SUB_TOPIC_NAME }}</span>
</script>
<!--CASES-->
<script id="CASES" type="text/html">
    <span title="{{d.CASES}}" class="overflowhidden-2"> {{ d.CASES }}</span>
</script>
<!--INVOLVE_PROVINCE_NAME-->
<script id="INVOLVE_PROVINCE_NAME" type="text/html">
    <span title="{{d.INVOLVE_PROVINCE_NAME}}" class="overflowhidden-2"> {{ d.INVOLVE_PROVINCE_NAME }}</span>
</script>
<!--ADVISE_AUDIT_TYPE-->
<script id="ADVISE_AUDIT_TYPE" type="text/html">
    <span title="{{d.ADVISE_AUDIT_TYPE}}" class="overflowhidden-2"> {{ d.ADVISE_AUDIT_TYPE }}</span>
</script>
<!--CHARGE_PERSON_NAME-->
<script id="CHARGE_PERSON_NAME" type="text/html">
    <span title="{{d.CHARGE_PERSON_NAME}}" class="overflowhidden-2"> {{ d.CHARGE_PERSON_NAME }}</span>
</script>
<!--ANALYST_PERSON_NAME-->
<script id="ANALYST_PERSON_NAME" type="text/html">
    <span title="{{d.ANALYST_PERSON_NAME}}" class="overflowhidden-2"> {{ d.ANALYST_PERSON_NAME }}</span>
</script>
<!--COUNTERPART_DEPARTMENT-->
<script id="COUNTERPART_DEPARTMENT" type="text/html">
    <span title="{{d.COUNTERPART_DEPARTMENT}}" class="overflowhidden-2"> {{ d.COUNTERPART_DEPARTMENT }}</span>
</script>
<!--风险初判-->
<script id="preliminary_assessment" type="text/html">
    <ul class="preliminary_assessment">
        <li>
            <span class="span1">普遍性：</span>
            <span class="content ovflowHidden" title="{{d.RISK_UNIVERSAL}}">{{d.RISK_UNIVERSAL}}</span>
        </li>
        <li>
            <span class="span2">重要性：</span>
            <span class="content ovflowHidden" title="{{d.RISK_IMPORTANT}}">{{d.RISK_IMPORTANT }}</span>
        </li>
    </ul>
</script>


<!--团队看板-->
<script id="group_content" type="text/html">
    {{#  layui.each(d, function(index, item){ }}
    <div class="order-4-table-tr layui-row">
        <div class="order-4-table-td order-4-table-th1 ovflowHidden" title="{{item.groupName}}">{{item.groupName}}
        </div>
        <div class="order-4-table-td order-4-table-th2 ovflowHidden" title="{{item.fieldName}}">{{item.fieldName}}
        </div>
        <div class="order-4-table-td order-4-table-th3 ovflowHidden" title="{{item.memberUserName}}">
            {{item.memberUserName}}
        </div>
        <div class="order-4-table-td order-4-table-th4 ovflowHidden" title="{{item.memNum}}">
            {{#if(item.memNum){ }}
                <a href = "javaScript:void(0)" onclick=showGroupInfo('{{item.id}}')>
                    {{item.memNum}}</a>
            {{# }else{  }}
                0
            {{# } }}

        </div>
        <div class="order-4-table-td order-4-table-th5 ovflowHidden" title="{{item.cnt}}">
            {{#if(item.cnt){ }}
            <a href = "javaScript:void(0)" onclick=achieveMore('{{item.id}}','')>
                {{item.cnt}}</a>
            {{# }else{  }}
                0
            {{# } }}
        </div>
        <div class="order-4-table-td order-4-table-th6 ovflowHidden" title="{{item.score}}">
            {{#if(item.score){ }}
            <a href = "javaScript:void(0)" onclick=openScore('{{item.groupName}}','','','','')>
                {{item.score}}</a>
            {{# }else{  }}
                0
            {{# } }}
        </div>
    </div>
    {{# }); }}
</script>
<!--组名-->
<script id="group_name" type="text/html">
    {{# if(d.groupDuty == "") { }}
    <span> {{ d.groupName }}</span>
    {{#}else { }}
    <span layui-title="{{ d.groupDuty}}" class="group_name_title"> {{ d.groupName }}</span>
    {{#} }}
</script>
<!--成果-->
<script id="resultCount_detail" type="text/html">
    {{# if(grouptype == '1') { }}
    <a lay-event="resultCount_detail" onclick="resultNumber(true,'{{ d.id }}','')">{{ d.resultNumber }}</a>
    {{#}else { }}
    <a lay-event="resultCount_detail" onclick="resultNumber(true,'','{{ d.loginName}}')">{{ d.resultNumber }}</a>
    {{#} }}
</script>
<!--任务总数-->
<script id="morder_detail" type="text/html">
    {{# if(grouptype == '1') { }}
    <a lay-event="morder_detail" onclick="taskNumber(true,'{{ d.id }}','')">{{ d.taskNumber }}</a>
    {{#}else { }}
    <a lay-event="morder_detail" onclick="taskNumber(true,'','{{ d.loginName}}')">{{ d.taskNumber }}</a>
    {{#} }}
</script>
<!--小组人数-->
<script id="morder_important" type="text/html">
    {{# if(grouptype == '1') { }}
    <a lay-event="morder_detail" onclick="memberNumber(true,'{{ d.id }}','')">{{ d.memberNumber }}</a>
    {{#}else { }}
    <a lay-event="morder_detail" onclick="memberNumber(true,'','{{ d.loginName}}')">{{ d.memberNumber }}</a>
    {{#} }}
</script>
<!--应用-->
<script id="apply_detail" type="text/html">
    {{# if(grouptype == '1') { }}
    <a lay-event="morder_detail" onclick="useNumber(true,'{{ d.id }}','')">{{ d.useNumber }}</a>
    {{#}else { }}
    <a lay-event="morder_detail" onclick="useNumber(true,'','{{ d.loginName}}')">{{ d.useNumber }}</a>
    {{#} }}
</script>
<!--成就值-->
<script id="achievement_detail" type="text/html">
    {{# if(grouptype == '1') { }}
    <a lay-event="morder_detail" onclick="achievementValue(true,'{{ d.id }}','')">{{ d.achievementValue }}</a>
    {{#}else { }}
    <a lay-event="morder_detail"
       onclick="achievementValue(true,'','{{ d.loginName}}')">{{ d.achievementValue }}</a>
    {{#} }}
</script>
<!--成就值排行--省分top5-->
<script id="shenList1" type="text/html">
    {{#  layui.each(d, function(index, item){ }}
    <div class="layui-row layui-row-check" provinceCode="{{item.provName}}">
        <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-data">
            <div class="person-data-img">
                {{# if(index <= 2) { }}
                <img src="resource/images/model/rankings_{{index+1}}.png" class="bestImg" alt="">
                {{# } else { }}
                <span class='rankNum'>{{index+1}} </span>
                {{#} }}
            </div>
            <span class="person-data-span">{{item.provName}}</span>
        </div>
        <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-data text-center ">
            {{#if(item.score){ }}
            <a href = "javaScript:void(0)" onclick=openScore('','','','{{item.provinceCode}}','')>
                {{item.score}}</a>
            {{# }else{  }}
            0
            {{# } }}
        </div>
        <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-data text-center " data-type="daily">
            {{item.personNum?item.personNum:0}}
        </div>
        <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-data text-center " data-type="task">
            {{item.pingJun?item.pingJun:0}}
        </div>
    </div>
    {{# }); }}
</script>
<!--成就值排行--省分last5-->
<script id="shenList2" type="text/html">
    {{#  layui.each(d, function(index, item){ }}
    <div class="layui-row layui-row-check" provinceCode="{{item.provName}}">
        <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-data ">
            <div class="person-data-img">
                <span class='rankNum'>{{index+6}} </span>
            </div>
            <span class="person-data-span">{{item.provName}}</span>
        </div>
        <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-data text-center "
             provinceCode="{{item.score}}">
            {{#if(item.score){ }}
            <a href = "javaScript:void(0)" onclick=openScore('','','','{{item.provinceCode}}','')>
                {{item.score}}</a>
            {{# }else{  }}
            0
            {{# } }}
        </div>
        <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-data text-center "
             provinceCode="{{item.personNum}}" data-type="daily">{{item.personNum?item.personNum:0}}
        </div>
        <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-data text-center "
             provinceCode="{{item.pingJun}}" data-type="task">{{item.pingJun?item.pingJun:0}}
        </div>
    </div>
    {{# }); }}
</script>
<!--成就值排行--人员top5-->
<script id="personList" type="text/html">
    {{#  layui.each(d, function(index, item){ }}
    <div class="layui-row layui-row-check">
        <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-data ">
            <div class="person-data-img">
                {{# if(index <= 2) { }}
                <img src="resource/images/model/rankings_{{index+1}}.png" class="bestImg" alt="">
                {{# } else { }}
                <span class='rankNum'>{{index+1}} </span>
                {{#} }}
            </div>
            <span class="person-data-span " titie="{{item.userName}}">{{item.userName}}</span>
        </div>
        <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-data text-center "
             titie="{{item.provName}}">{{item.provName}}
        </div>
        <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-data text-center "
             titie="{{item.score}}">
            {{#if(item.score){ }}
            <a href = "javaScript:void(0)" onclick=openScore('','','{{item.userName}}','','')>
                {{item.score}}</a>
            {{# }else{  }}
            0
            {{# } }}
        </div>
        <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-data text-center "
             titie="{{item.chengguoNum}}">{{item.chengguoNum?item.chengguoNum:0}}
        </div>
    </div>
    {{# }); }}
</script>
<!--成就值排行--人员last5-->
<script id="personList1" type="text/html">
    {{#  layui.each(d, function(index, item){ }}
    <div class="layui-row layui-row-check">
        <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-data pl8">
            <div class="person-data-img">
                <span class='rankNum'>{{index+6}} </span>
            </div>
            <span class="person-data-span " titie="{{item.userName}}">{{item.userName}}</span>
        </div>
        <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-data text-center "
             titie="{{item.provName}}">{{item.provName}}
        </div>
        <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-data text-center "
             titie="{{item.score}}">
            {{#if(item.score){ }}
            <a href = "javaScript:void(0)" onclick=openScore('','','{{item.userName}}','','')>
                {{item.score}}</a>
            {{# }else{  }}
            0
            {{# } }}
        </div>
        <div class="layui-col-md3 layui-col-sm3 layui-col-lg3 person-data text-center "
             titie="{{item.chengguoNum}}">{{item.chengguoNum?item.chengguoNum:0}}
        </div>
    </div>
    {{# }); }}
</script>

<!--风研成果--占比-->
<script id="outCycleTpl" type="text/html">
    {{#  layui.each(d, function(index, item){ }}
        <div class="outCycleBox cursor" onclick=outCycleSelect('{{item.name}}')>
            <div class="outCycleTitle">{{item.name}}:</div>
            <div class="outCycleProp">{{item.proportion}}</div>
        </div>
    {{# }); }}
</script>
<script>
    var startTime = '',scoreStartTime = '';
    var endTime = '';
    //定义团队看板index
    var grouptype = '8';
    //积分排行榜index
    var achieveType = 'prov';
    layui.use(['jqform', 'element', 'jquery', 'laytpl', 'layer', 'jqfrm', 'table', 'laydate', 'jqbind'], function () {
        var tpl = layui.laytpl,
            element = layui.element,
            form = layui.jqform,
            ctx = top.global.ctx,
            jqbind = layui.jqbind,
            $ = layui.jquery,
            table = layui.table,
            laydate = layui.laydate,
            layer = layui.layer,
            frm = layui.jqfrm;
        jqbind.init();
        var importanceLevel = 0
        var myDate = new Date();
        var targetYear = myDate.getFullYear();
        var datetype = 1; //0:自定义; 1: 本年; 2: 去年;
        var fileCode = '';
        //定义应用/要素index;echarts index,成果接口片段集合
        var middleGIndex = '0',
            middleGApply = '',
            middleGResult = '',
            achieveParamsBox = ['apply', 'result'],
            achieveLParamsBox = ['Apply', 'Main'],
            tableData = [];
        preDealTime({
            ctx: ctx,
            funcFlag: "SJYWZT",
            type: 'year',
            range: true,
            format: 'yyyy',
        }, form, laydate);
        var warning = false //预警
        var project = false //项目
        var tongbao = false //通报
        var wenti = false //问题
        var generateScore = false  //是否产生积分
        var stars='';//星级

        window.onloadFunction = function () {
            var time = $("#payment").val();
            startTime = time.split(" - ")[0];
            scoreStartTime = startTime;
            endTime = time.split(" - ")[1];
            //上方统计数据
            statisticGroupInfo();
            statisticTopicInfo();
            statisticApplyInfo();
            //专题、成果分布Bar
            initChart1Data();
            //积分排序、成果排序
            achieveSort();

            //todo 成果Pie图
            initAchieveData();
            //todo 成果列表
            initAchieveList();

            //团队看板
            loadGroup();
            //积分排行-----省分、人员
            achieveType === 'prov' ? initChart5Data() : initChart6Data();
        }
        /**---------------------------------------------首页上方统计--------------------**/
        window.statisticGroupInfo = function () {
            //专家团队\监控中心\课题团队
            $.ajax({
                type: 'post',
                url: ctx + '/bdata/remoteDataLeaderHome/statisticGroupInfo',
                dataType: "json",
                data: JSON.stringify({
                    startTime: startTime,
                    endTime: endTime
                }),
                contentType: "application/json;charset=UTF-8",
                success: function (ret) {
                    if (ret.httpCode === 200) {
                        var data = ret.data
                        for (var key in data) {
                            $('#' + key).text(data[key]);
                        }
                    }
                }
            });
        }
        window.statisticTopicInfo = function () {
            //专题\子专题\ 聚焦风险
            $.ajax({
                type: 'post',
                url: ctx + '/bdata/remoteDataLeaderHome/statisticTopicInfo',
                dataType: "json",
                data: JSON.stringify({
                    startTime: startTime,
                    endTime: endTime
                }),
                contentType: "application/json;charset=UTF-8",
                success: function (ret) {
                    if (ret.httpCode === 200) {
                        var data = ret.data
                        for (var key in data) {
                            $('#' + key).text(data[key]);
                        }
                    }
                }
            });
        }
        window.statisticApplyInfo = function () {
            //触发项目\ 审计通报\ 发现问题\积分汇总
            $.ajax({
                type: 'post',
                url: ctx + '/bdata/remoteDataLeaderHome/statisticApplyInfo',
                dataType: "json",
                data: JSON.stringify({
                    startTime: startTime - 1+'',
                    scoreStartTime: scoreStartTime,
                    endTime: endTime
                }),
                contentType: "application/json;charset=UTF-8",
                success: function (ret) {
                    if (ret.httpCode === 200) {
                        var data = ret.data
                        for (var key in data) {
                            $('#' + key).text(data[key]);
                        }
                    }
                }
            });
        }
        /**---------------------------------------------首页上方统计--------------------**/

        /**---------------------------------------------专题、成果分布柱状图--------------------**/
        //专题、成果分布echarts渲染
        function initChart1(data) {
            var myChart = echarts.init(document.getElementById("container1"));
            var title = []; //x轴值
            var taskNumber = []; //任务
            var resultNumber = []; //成果
            for (var i = 0; i < data.length; i++) {
                title.push(data[i].fieldName)
                taskNumber.push(data[i].ztNumber)
                resultNumber.push(data[i].chengGuo)
            }
            myChart.off('click')
            var option = {
                color: ['#d88e93', '#e5c689'],
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        crossStyle: {
                            color: '#999'
                        }
                    }
                },
                toolbox: {
                    feature: {
                        saveAsImage: {
                            show: true
                        }
                    }
                },
                legend: {
                    data: ['专题', '成果'],
                    textStyle: {
                        color: '#000',//坐标值得具体的颜色
                        fontSize: '14'
                    }
                },
                grid: {
                    left: '2%',
                    right: '5%',
                    bottom: '2%',
                    containLabel: true
                },
                xAxis: [{
                    type: 'category',
                    data: title,
                    key: 'codeText',
                    axisPointer: {
                        type: 'shadow'
                    },
                    axisLabel: {
                        rotate: 22,
                        formatter: function (params) {
                            return params ? lineBreak(params, 8) : "";
                        },
                        textStyle: {
                            color: '#000',//坐标值得具体的颜色
                            fontSize: '14'
                        }
                    },
                }],
                yAxis: [{
                    type: 'value',
                    name: '单位/个',
                    axisLabel: {
                        formatter: '{value}'
                    }
                }],
                series: [{
                    name: '专题',
                    type: 'bar',
                    barWidth: '18',
                    data: taskNumber,
                    label: {
                        normal: {
                            show: false,
                            position: 'top',
                            textStyle: {
                                color: '#333',
                            }
                        }
                    }
                },
                {
                    name: '成果',
                    type: 'bar',
                    barWidth: '18',
                    data: resultNumber,
                    label: {
                        normal: {
                            show: false,
                            position: 'top',
                            textStyle: {
                                color: '#333',
                            }
                        }
                    }
                }
                ]
            };
            myChart.setOption(option, true);
            myChart.on('click', function (params) {
                achieveMore('',data[params.dataIndex].fieldCode)
            })
        }
        //专题、成果分布echarts数据获取
        function initChart1Data() {
            $.ajax({
                type: 'post',
                url: ctx + '/bdata/remoteDataLeaderHome/selectFieldHistogram',
                dataType: "json",
                data: JSON.stringify({
                    startTime: startTime,
                    endTime: endTime
                }),
                contentType: "application/json;charset=UTF-8",
                success: function (ret) {
                    initChart1(ret.data);
                }
            });
        }
        /**---------------------------------------------专题、成果分布柱状图--------------------**/

        /**---------------------------------------------积分排序、成果排序--------------------**/
        //积分排序、成果排序
        //定义成果排序index;排序接口片段集合
        var achieveListIndex = '0',
            sortParamsBox = ['/bdata/remoteDataLeaderHome/selectPointSort', '/bdata/remoteDataLeaderHome/selectResultsRanking']
        function achieveSort() {
            $.ajax({
                type: 'post',
                url: ctx + sortParamsBox[+achieveListIndex],
                dataType: "json",
                data: JSON.stringify({
                    startTime: startTime,
                    endTime: endTime,
                    page: 1,
                    limit: 6
                }),
                contentType: "application/json;charset=UTF-8",
                success: function (ret) {
                    if (ret.httpCode === 200) {
                        if (ret.data && ret.data.length) {
                            $("#noDataDiv").hide();
                            $("#hasDataDiv").show();
                            lp(tpl, ret.data, achieveSortTpl.innerHTML, $('#achieveBox'));
                        } else {
                            $("#hasDataDiv").hide();
                            $("#noDataDiv").show();
                        }

                    }
                }
            });
        }
        window.sortDetailed = function () {
            var sortIndex;
            $("#earlyType .layui-select-btn-span").each(function (index, obj) {
                var hasClass = $(obj).attr("class");
                if (hasClass.indexOf("active") > -1) {
                    sortIndex = $(obj).attr("layui-type");
                    return false;
                }
            });
            layer.open({
                type: 2,
                content: 'views/audit/bigdata/remoteDataKanban/moreAchieveSort.html?sortIndex=' + sortIndex + '&startTime=' + startTime + '&endTime=' + endTime,
                area: ['900px', '590px']
            });
        };
        /**---------------------------------------------积分排序、成果排序--------------------**/


        /**---------------------------------------------团队看板table--------------------**/
        //团队看板table
        window.loadGroup = function () {
            $.ajax({
                url: ctx + '/bdata/remoteDataLeaderHome/selectTeamKanban',
                data: JSON.stringify({
                    startTime: startTime,
                    endTime: endTime,
                    type: grouptype,
                    page: 1,
                    limit: 5
                }),
                dataType: "json",
                type: "POST",
                contentType: "application/json;charset=UTF-8",
                success: function (ret) {
                    if (ret.httpCode == 200) {
                        var getTpl = group_content.innerHTML,
                            view = document.getElementById('group_table');
                        tpl(getTpl).render(ret.data, function (html) {
                            view.innerHTML = html;
                        });

                    }
                }
            });

        }
        /**---------------------------------------------团队看板table--------------------**/

        /**---------------------------------------------积分排行榜--------------------**/
        //积分排行榜
        layui.form.on('radio(selectBtn)', function (data) {
            achieveType = data.value;
            if (achieveType == 'prov') {
                $("#personChart").removeClass('layui-hide');
                $("#achievesearchChart").addClass('layui-hide');
                initChart5Data();
            } else if (achieveType == 'person') {
                $("#achievesearchChart").removeClass('layui-hide')
                $("#personChart").addClass('layui-hide');
                initChart6Data();
            }
        });
        //积分排行榜-----省分
        window.initChart5Data = function () {
            var getTpl1 = shenList1.innerHTML,
                view1 = document.getElementById('personChart1');
            var getTpl2 = shenList2.innerHTML,
                view2 = document.getElementById('personChart2');
            $.ajax({
                url: ctx + '/bdata/remoteDataLeaderHome/selectPointRanking',
                data: JSON.stringify({
                    startTime: startTime,
                    endTime: endTime,
                    type: achieveType,
                    page: 1,
                    limit: 5
                }),
                dataType: "json",
                type: "POST",
                contentType: "application/json;charset=UTF-8",
                success: function (ret) {
                    if (ret.httpCode == 200) {
                        tpl(getTpl1).render(ret.data, function (html) {
                            view1.innerHTML = html;
                        });
                    }
                }
            });
            $.ajax({
                url: ctx + '/bdata/remoteDataLeaderHome/selectPointRanking',
                data: JSON.stringify({
                    startTime: startTime,
                    endTime: endTime,
                    type: achieveType,
                    page: 2,
                    limit: 5
                }),
                dataType: "json",
                type: "POST",
                contentType: "application/json;charset=UTF-8",
                success: function (ret) {
                    if (ret.httpCode == 200) {
                        tpl(getTpl2).render(ret.data, function (html) {
                            view2.innerHTML = html;
                        });
                    }
                }
            });
        }
        //积分排行榜-----人员
        window.initChart6Data = function () {
            var getTpl = personList.innerHTML,
                getTpl1 = personList1.innerHTML,
                view1 = document.getElementById('achievesearchChart1'),
                view2 = document.getElementById('achievesearchChart2');
            $.ajax({
                type: 'post',
                url: ctx + '/bdata/remoteDataLeaderHome/selectPointRanking',
                data: JSON.stringify({
                    startTime: startTime,
                    endTime: endTime,
                    type: achieveType,
                    page: 1,
                    limit: 5
                }),
                dataType: "json",
                contentType: "application/json;charset=UTF-8",
                success: function (ret) {
                    var data = ret.data;
                    console.log(ret);
                    tpl(getTpl).render(data, function (html) {
                        view1.innerHTML = html;
                    });
                }
            });
            $.ajax({
                type: 'post',
                url: ctx + '/bdata/remoteDataLeaderHome/selectPointRanking',
                data: JSON.stringify({
                    startTime: startTime,
                    endTime: endTime,
                    type: achieveType,
                    page: 2,
                    limit: 5
                }),
                dataType: "json",
                contentType: "application/json;charset=UTF-8",
                success: function (ret) {
                    var data = ret.data;
                    if (ret.httpCode == 200) {
                        tpl(getTpl1).render(data, function (html) {
                            view2.innerHTML = html;
                        });
                    }
                }
            });
        }

        //积分排行榜-----更多
        window.achieveValuesMore = function () {
            layer.open({
                title: '积分排行榜',
                type: 2,
                content: 'views/audit/bigdata/remoteDataKanban/morePointsRanking.html?startTime=' +
                    startTime + '&endTime=' + endTime + '&achieveType=' + achieveType,
                area: ['900px', '580px']
            });
        };
        /**---------------------------------------------积分排行榜--------------------**/

        /**---------------------------------------------风研成果饼状图echarts渲染--------------------**/
        function initAchieveChart(outCycle,numMax) {
            $.each(outCycle, function (index, item) {
                item.proportion =  (Number(item.value/numMax)*100).toFixed(2)+'%'
            });
            lp(tpl, outCycle, outCycleTpl.innerHTML, $('#outCycle'));
        }
        //图标点击事件
        window.outCycleSelect = function(seriesName) {
            var starsName = seriesName;
            if(starsName == '无'){
                stars = '0';
            }else{
                stars = starsName.substr(0,1)
            }
            initAchieveList();
        };
        //风研成果charts数据请求
        function initAchieveData() {
            $.ajax({
                type: 'post',
                url: ctx + '/bdata/remoteDataLeaderHome/selectFengYanResultsPie',
                dataType: "json",
                data: JSON.stringify({
                    startTime: startTime -1 +'',
                    scoreStartTime:scoreStartTime,
                    endTime: endTime,
                    //importanceLevel: importanceLevel,
                    warning: warning,//预警
                    project: project, //项目
                    tongbao: tongbao,//通报
                    wenti: wenti, //问题
                    generateScore: generateScore, //是否产生积分
                    type :1
                }),
                contentType: "application/json;charset=UTF-8",
                success: function (ret) {
                    if (ret.httpCode === 200) {
                        var data = ret.data;

                        var outCycle = new Array();
                        // var inPie = new Array();
                        var numMax = 0;
                        $.each(data.data, function (index, item) {
                            outCycle.unshift({
                                name: item.stars == "1" ? '1星' : item.stars == "2" ? '2星' : item.stars == "3" ? '3星' : '无',
                                value: item.cgNumber,
                            });
                            numMax+=item.cgNumber;
                            // inPie.push({
                            //     name: item.stars == "1" ? '1星' : item.stars == "2" ? '2星' : item.stars == "3" ? '3星' : '无',
                            //     value: item.applyNumber !== undefined && item.applyNumber !== '' ? item.applyNumber : 0,
                            // });
                        });
                        // console.log('inPie:', inPie);
                        console.log('outCycle:', outCycle,numMax);
                        initAchieveChart(outCycle, numMax);
                    }
                }
            });
        }

        form.on('checkbox(sjcg)', function (data) {
            if (data.elem.checked) {
                if (data.elem.value == 'warning') {
                    warning = true
                }
                if (data.elem.value == 'project') {
                    project = true
                }
                if (data.elem.value == 'tongbao') {
                    tongbao = true
                }
                if (data.elem.value == 'wenti') {
                    wenti = true
                }
                if (data.elem.value == 'generate') {
                    generateScore = true
                }

            } else {

                if (data.elem.value == 'warning') {
                    warning = false
                }
                if (data.elem.value == 'project') {
                    project = false
                }
                if (data.elem.value == 'tongbao') {
                    tongbao = false
                }
                if (data.elem.value == 'wenti') {
                    wenti = false
                }
                if (data.elem.value == 'generate') {
                    generateScore = false
                }
            }
            initAchieveData();
            initAchieveList();
        })

        //风研成果列表数据请求
        function initAchieveList() {
            $.ajax({
                type: 'post',
                url: ctx + '/bdata/remoteDataLeaderHome/selectFengYanResultsList',
                dataType: "json",
                data: JSON.stringify({
                    startTime: startTime -1 +'',
                    scoreStartTime:scoreStartTime,
                    endTime: endTime,
                    fileCode: fileCode,
                    page: 1,
                    limit: 6,
                    //importanceLevel: importanceLevel,
                    warning: warning,//预警
                    project: project, //项目
                    tongbao: tongbao,//通报
                    wenti: wenti, //问题
                    generateScore: generateScore, //是否产生积分
                    stars:stars,
                    type:1
                }),
                contentType: "application/json;charset=UTF-8",
                success: function (ret) {
                    if (ret.httpCode === 200) {
                        if (ret.count) {
                            $('#middleList').show().siblings().not('.middle-charts').hide()
                            var data = ret.data;
                            tableData = data
                            table.render({
                                elem: '#middleListTable',
                                id: 'middleListTable',
                                data: data,
                                page: false,
                                height: '380',
                                cols: [
                                    [{
                                        title: '序号',
                                        align: 'center',
                                        style: 'text-align: center',
                                        width: '60',
                                        field: 'number',
                                        fixed:true,
                                        rowspan: 2,
                                        templet: function (d) {
                                            var html = ''
                                            if (d.LAY_TABLE_INDEX == 0) {
                                                html = '<div style="width:100%;text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">合计</div>'
                                            } else {
                                                html = '<div title="' + d.LAY_TABLE_INDEX + '" style="width:100%;text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.LAY_TABLE_INDEX + '</div>'
                                            }
                                            return html
                                        }

                                    }, {
                                        title: '专题',
                                        align: 'center',
                                        style: 'text-align: center',
                                        width: '70',
                                        field: 'topicName',
                                        rowspan: 2,
                                        fixed:true,
                                        templet: function (d) {
                                            var html = ''
                                            if (d.LAY_INDEX == 1) {
                                                html = '<div  onclick=openNewTable(this,' + d.LAY_INDEX +',"topicName") title="' + d.topicName + '" style="width:100%;text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.topicName + '</div>'
                                            } else {
                                                html = '<div  onclick=openNewTable(this,' + d.LAY_INDEX +',"topicName") title="' + d.topicName + '" style="width:100%;text-align:left;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.topicName + '</div>'
                                            }
                                            return html
                                        }
                                    }, {
                                        title: '子专题',
                                        align: 'center',
                                        style: 'text-align: center',
                                        width: '70',
                                        field: 'subTopicName',
                                        rowspan: 2,
                                           fixed:true,
                                        templet: function (d) {
                                            var html = ''
                                            if (d.LAY_INDEX == 1) {
                                                html = '<div onclick=openNewTable(this,' + d.LAY_INDEX +',"subTopicName") title="' + d.subTopicName + '" style="width:100%;text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.subTopicName + '</div>'
                                            } else {
                                                html = '<div onclick=openNewTable(this,' + d.LAY_INDEX +',"subTopicName") title="' + d.subTopicName + '" style="width:100%;text-align:left;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.subTopicName + '</div>'
                                            }
                                            return html
                                        }
                                    }, {
                                        title: '星级',
                                        align: 'center',
                                        style: 'text-align: center',
                                        width: '70',
                                        field: 'stars',
                                        rowspan: 2,
                                        templet: function (d) {
                                            var titles = ''
                                            titles += '重要性：' + d.riskImportant
                                            titles += '普遍性：' + d.riskUniversal
                                            if (d.stars == '1') {
                                                return "<span class='stars' onclick=openNewTableRiskStars(this," + d.LAY_INDEX +") data-title1='" + d.riskImportant + "'  data-title2='" + d.riskUniversal + "'><span class='icon iconfont icon-xingxing1'></span> </span>"
                                            } else if
                                                (d.stars == '2') {
                                                return "<span class='stars' onclick=openNewTableRiskStars(this," + d.LAY_INDEX +") data-title1='" + d.riskImportant + "'  data-title2='" + d.riskUniversal + "'><span class='icon iconfont icon-xingxing1'></span><span class='icon iconfont icon-xingxing1'></span></span>"
                                            } else
                                                if (d.stars == '3') {

                                                    return "<span class='stars' onclick=openNewTableRiskStars(this," + d.LAY_INDEX +") data-title1='" + d.riskImportant + "'  data-title2='" + d.riskUniversal + "'><span class='icon iconfont icon-xingxing1'></span><span class='icon iconfont icon-xingxing1'></span><span class='icon iconfont icon-xingxing1'></span></span>"
                                                } else {
                                                    return "<span class='stars' onclick=openNewTableRiskStars(this," + d.LAY_INDEX +") data-title1='" + d.riskImportant + "'  data-title2='" + d.riskUniversal + "' >-</span>"
                                                }
                                        }
                                    },{
                                        title: '聚焦风险',
                                        align: 'center',
                                        style: 'text-align: center',
                                        width: '290',
                                        field: 'initialFocusRiskText',
                                        rowspan: 2,
                                        templet: function (d) {
                                            // var html = ''
                                            // if (d.LAY_INDEX == 1) {
                                            //     html = '<div title="' + d.initialFocusRisk + '" style="width:100%;text-align:left;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">-</div>'
                                            // } else {
                                            //     html = '<div onclick=initialFocusRisk(this,' + d.LAY_TABLE_INDEX +',"initialFocusRisk")>eee<span>' +d.initialFocusRisk+'</span></div>';
                                            // }
                                            // return html
                                            if (d.LAY_TABLE_INDEX == 0) {
                                                return '<div>-</div>'
                                            } else {
                                                return '<div class="overflowhidden-2"  onclick=initialFocusRisk(this,' + d.LAY_TABLE_INDEX +',"initialFocusRisk")>' + d.initialFocusRiskText + '</div>'
                                            }
                                        }
                                        // templet: function (d) {
                                        //     var html = ''
                                        //     if (d.LAY_INDEX == 1) {
                                        //         html = '<div title="' + d.initialFocusRisk + '" style="width:100%;text-align:left;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">-</div>'
                                        //     } else {
                                        //         if(d.initialFocusRiskProvList){
                                        //
                                        //             if(d.initialFocusRiskProvList.length>0){
                                        //                 for(var i=0; i<d.initialFocusRiskProvList.length; i++){
                                        //                    // html +=  '<div title="' + d.initialFocusRiskList[i] + '" >' + d.initialFocusRiskList[i] + '</div>'
                                        //                     html += '<div onclick=openNewTableRisk(this,' + d.LAY_INDEX +','+i+')><span>' + d.initialFocusRiskProvList[i].INITIAL_FOCUS_RISK + '</span></div>'
                                        //                 }
                                        //             }
                                        //         }
                                        //     }
                                        //     return html
                                        // }
                                    }, {
                                        title: '风险<br/>预警单',
                                        align: 'center',
                                        style: 'text-align: center',
                                        field: 'risk',
                                        width: '70',
                                        rowspan: 2,
                                        templet: function (d) {
                                            return  '<div onclick=openYjdPreView("' + d.LAY_INDEX +  '","' +  d.risk + '","' + d.id + '")  title="' + d.risk + '" style="color:#F5222D;  cursor: pointer; width:100%;text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.risk + '</div>'
                                        }
                                    }, {
                                        title: '触发<br/>项目',
                                        align: 'center',
                                        style: 'text-align: center',
                                        width: '70',
                                        field: 'xiangmu',
                                        rowspan: 2,
                                        templet: function (d) {
                                            return '<div onclick=openCfxm("' + d.LAY_INDEX +  '","' + d.id + '") title="' + d.xiangmu + '" style="cursor: pointer;color:#F5222D; width:100%;text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.xiangmu + '</div>'
                                        }
                                    }, {
                                        title: '审计成果',
                                        align: 'center',
                                        style: 'text-align: center',
                                        width: '10%',
                                        colspan: 2
                                    }, {
                                        title: '积分',
                                        align: 'center',
                                        style: 'text-align: center',
                                        width: '60',
                                        field: 'score',
                                        rowspan: 2,
                                        templet: function (d) {
                                            if(d.researchIds){
                                                return '<div onclick=openScore("","","","","research") title="' + d.projectList + '" style="cursor: pointer;color:#F5222D; width:100%;text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.score + '</div>'
                                            }else{
                                                return '<div onclick=openScore("","'+d.id+'","","","research") title="' + d.projectList + '" style="cursor: pointer;color:#F5222D; width:100%;text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.score + '</div>'
                                            }
                                        }
                                    }, {
                                        title: '来源',
                                        align: 'center',
                                        style: 'text-align: left',
                                        width: '140',
                                        templet: function (d) {
                                            if (d.LAY_INDEX == 1) {
                                                return '<div style="width:100%;text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">-</div>'
                                            } else {
                                                if (d.spanType == '0') {
                                                    return '<div title="' + d.remoteYear + '年' + d.spanTypeName + '" style="text-align:left;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.remoteYear + '年' + d.spanTypeName + '</div>'
                                                } else if (d.spanType == '1') {
                                                    return '<div title="' + d.remoteYear + '年' + d.spanTypeName + '" style="text-align:left;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.remoteYear + '年' + d.spanTypeName + '</div>'
                                                } else if (d.spanType == '2') {
                                                    return '<div title="' + d.remoteYear + '年' + d.spanTypeName + '" style="text-align:left;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.remoteYear + '年' + d.spanTypeName + '</div>'
                                                } else {
                                                    return '';
                                                }
                                            }

                                        },
                                        rowspan: 2
                                    },  {
                                        title: '涉及单位',
                                        align: 'center',
                                        style: 'text-align: center',
                                        width: '100',
                                        field: 'involveProvinceName',
                                        rowspan: 2,
                                        templet: function (d) {
                                            if (d.LAY_INDEX == 1) {
                                                return '<div style="text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">-</div>'
                                            }else{
                                                return '<div onclick=openNewTable(this,' + d.LAY_INDEX +',"involveProvinceName") title="' + d.involveProvinceName + '" style="text-align:left;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.involveProvinceName + '</div>'
                                            }

                                        }
                                    }, {
                                        title: '类型',
                                        align: 'center',
                                        style: 'text-align: center',
                                        width: '80',
                                        field: 'topicTypeName',
                                        rowspan: 2,
                                        templet: function (d) {
                                            if (d.LAY_INDEX == 1) {
                                                return '<div style="text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">-</div>'
                                            }else{
                                                return '<div onclick=openNewTable(this,' + d.LAY_INDEX +',"topicTypeName") title="' + d.topicTypeName + '" style="text-align:left;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.topicTypeName + '</div>'
                                            }

                                        }

                                    }, {
                                        title: '测试用例',
                                        align: 'center',
                                        style: 'text-align: center',
                                        width: '85',
                                        field: 'cases',
                                        rowspan: 2,
                                        templet: function (d) {

                                            if (d.LAY_INDEX == 1) {
                                                return '<div style="text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">-</div>'
                                            }else{
                                                return (
                                                    '<div style="width:100%;text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;"><span style="color:#c20000" onclick=openMyOpu("' +
                                                    d.id +
                                                    '")  class="icon iconfont icon-chakan pointer color-red" title="查看"></span></div>'
                                                );
                                            }

                                        }
                                    },{
                                        title: '新打通数据',
                                        align: 'center',
                                        style: 'text-align: center',
                                        width: '100',
                                        field: 'openNewDataContent',
                                        rowspan: 2,
                                        templet: function (d) {

                                            if (d.LAY_INDEX == 1) {
                                                return '<div style="text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">-</div>'
                                            }else{
                                                return '<div title="' + d.openNewDataContent + '" style="text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;" onclick=openNewTable(this,' + d.LAY_INDEX +',"openNewDataContent")>' + d.openNewDataContent + '</div>'
                                            }

                                        }
                                    }, {
                                        title: '负责人',
                                        align: 'center',
                                        style: 'text-align: center',
                                        width: '90',
                                        field: 'chargePersonName',
                                        rowspan: 2,
                                        templet: function (d) {

                                            if (d.LAY_INDEX == 1) {
                                                return '<div style="text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">-</div>'
                                            }else{
                                                return '<div title="' + d.chargePersonName + '" style="width:100%;text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.chargePersonName + '</div>'
                                            }

                                        }
                                    }, {
                                        title: '分析人',
                                        align: 'center',
                                        style: 'text-align: center',
                                        width: '120',
                                        field: 'analystPersonName',
                                        rowspan: 2,
                                        templet: function (d) {

                                            if (d.LAY_INDEX == 1) {
                                                return '<div style="text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">-</div>'
                                            }else{
                                                return '<div onclick=openNewTable(this,' + d.LAY_INDEX +',"analystPersonName") title="' + d.analystPersonName + '" style="width:100%;text-align:left;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.analystPersonName + '</div>'
                                            }

                                        }
                                    }, {
                                        title: '对口处室',
                                        align: 'center',
                                        style: 'text-align: center',
                                        width: '100',
                                        field: 'counterpartDepartment',
                                        rowspan: 2,
                                        templet: function (d) {
                                            if (d.LAY_INDEX == 1) {
                                                return '<div style="text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">-</div>'
                                            }else{
                                                return '<div title="' + d.counterpartDepartment + '" style="text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.counterpartDepartment + '</div>'
                                            }
                                        }
                                    },{
                                        title: '提交日期',
                                        align: 'center',
                                        style: 'text-align: center',
                                        width: '100',
                                        field: 'commitDate',
                                        rowspan: 2,
                                        templet: function (d) {
                                            if (d.LAY_INDEX == 1) {
                                                return '<div style="text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">-</div>'
                                            } else {
                                                return '<div title="' + d.commitDate + '" style="text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.commitDate + '</div>'
                                            }

                                        }
                                    },{
                                        title: '核查单位',
                                        align: 'center',
                                        style: 'text-align: left',
                                        width: '100',
                                        field: 'checkProv',
                                        rowspan: 2,
                                        templet: function (d) {
                                            if (d.checkProv) {
                                                var provNames = [];
                                                for (var i = 0; i < d.checkProv.length; i++) {
                                                    // 检查 status 是否为 1
                                                    var color = d.checkProv[i].status == "1" ? "text-red" : "";
                                                    provNames.push(`<span class="${color}">${d.checkProv[i].provName}</span>`);
                                                }
                                                return '<div onclick=openNewTable(this,' + d.LAY_TABLE_INDEX +',"checkProv") class="overflowhidden-2">' + provNames.join('、') + '</div>';
                                            } else {
                                                return '<div style="text-align:center;">-</div>';
                                            }
                                        }
                                    },
                                        {
                                            title: '推送单位',
                                            align: 'center',
                                            style: 'text-align: left',
                                            width: '100',
                                            field: 'pushProv',
                                            rowspan: 2,
                                            templet: function (d) {
                                                if (d.pushProv) {
                                                    var provNames = [];
                                                    for (var i = 0; i < d.pushProv.length; i++) {
                                                        // 检查 status 是否为 1
                                                        var color = d.pushProv[i].status == "1" ? "text-red" : "";
                                                        provNames.push(`<span class="${color}">${d.pushProv[i].provName}</span>`);
                                                    }
                                                    return '<div onclick=openNewTable(this,' + d.LAY_TABLE_INDEX +',"pushProv") class="overflowhidden-2">' + provNames.join('、') + '</div>';
                                                } else {
                                                    return '<div style="text-align:center;">-</div>';
                                                }
                                            }
                                        },{
                                        field: "operate",
                                        title: "操作",
                                        fixed: 'right',
                                        align: "center",
                                        width: "60",
                                        rowspan: 2,
                                        templet: function (d) {
                                            if (d.LAY_INDEX == 1) {
                                                return '<div style="text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">-</div>'
                                            }else{
                                                let text = '<div title="查看阶段性风研" style="text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;width: 100%">'
                                                if (d.id) {
                                                    text +=  '<span class="icon iconfont text-red cursor table-btn" title="查看" onclick=detailLeaderManager("' + d.id + '","' + d.buttonType + '")>&#xe651;</span>'
                                                }
                                                text += '</div>';
                                                return text;
                                            }

                                        },
                                    } ], [{
                                        title: '审计通报',
                                        align: 'center',
                                        style: 'text-align: center',
                                        width: '85',
                                        field: 'tongbao',
                                        templet: function (d) {
                                            return '<div  onclick=openSjtbTable("' + d.LAY_INDEX +  '","' +  d.tongbao + '","' + d.id + '") title="' + d.tongbao + '" style=" cursor: pointer; color:#F5222D; width:100%; text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.tongbao + '</div>'
                                        }
                                    }, {
                                        title: '发现问题',
                                        align: 'center',
                                        style: 'text-align: center',
                                        width: '85',
                                        field: 'wenti',
                                        templet: function (d) {
                                            return '<div onclick=openFxwtTable("' + d.LAY_INDEX +  '","' +  d.wenti + '","' + d.id+ '")  title="' + d.wenti + '" style=" cursor: pointer; color:#F5222D;  width:100%; text-align:center;text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">' + d.wenti + '</div>'
                                        }
                                    }]
                                ],
                                done: function (res) {
                                    var data = res.data
                                    for(var i=0; i<data.length; i++){
                                        if(data[i].initialFocusRiskProvList){
                                            if(data[i].initialFocusRiskProvList.length>0){
                                                var doms = $('.layui-table-main>.layui-table>tbody>tr').eq(i).find('td').find('.layui-table-cell')
                                                for(var j=0; j<1;j++){
                                                    $(doms[j]).css('height', 48 + 'px')
                                                }

                                                var doms = $('.layui-table-fixed>.layui-table-body>.layui-table>tbody>tr').eq(i).find('td').find('.layui-table-cell')
                                                for(var j=0; j<1;j++){
                                                    $(doms[j]).css('height', 48 + 'px')
                                                }

                                                var domss = $('.layui-table-fixed-r>.layui-table-body>.layui-table>tbody>tr').eq(i).find('td').find('.layui-table-cell')
                                                for (var j = 0; j < 1; j++) {
                                                    $(domss[j]).css('height', 48 + 'px')
                                                }
                                            }
                                        }
                                    }
                                    form.render()
                                }
                            });
                        } else {
                            $('.noList').show().siblings().not('.middle-charts').hide()
                        }
                    }
                }
            });
        }

        //详情
        window.detailLeaderManager = function (id,buttonType) {
            var indexs = top.layer.open({
                title: '详情',
                content:
                    "views/audit/bigdata/remoteDataKanban/riskResearchInput/wind-research-achievements-detail-new.html" +
                    "?id=" + id+'&buttonType='+buttonType,
                type: 2,
                area: ["80%", "80%"],
                fixed: true,
                maxmin: false,
                resize: false,
                yes: function (index, layero) {

                },
                btn2: function (index, layero) {

                },
                success: function (layero, index) {
                },
            });
        }


        window.refreshData = function (obj, type) {
            $('.header-btn-1').removeClass('actived')
            $('.header-btn-2').removeClass('actived')
            $(obj).addClass('actived')
            importanceLevel = type
            initAchieveData()
            initAchieveList()

        }

        /**---------------------------------------------风研成果饼状图echarts渲染--------------------**/

        //全国汇总
        window.selectAllResultsStatistic = function () {
            var title = '全国汇总';
            var target =
                '/views/audit/bigdata/remoteDataKanban/allResultsStatistic.html?true&startTime=' +
                startTime + "&endTime=" + endTime;
            $("#tableMenu").data("title", title).data("url", target).click();
        };

        //省份维度
        window.selectProvinceResultsStatistic = function () {
            var title = '省分维度';
            var target =
                '/views/audit/bigdata/remoteDataKanban/provinceResultsStatistic.html?true&startTime=' +
                startTime + "&endTime=" + endTime;
            $("#tableMenu").data("title", title).data("url", target).click();
        };

        //风研成果：更多
        window.achieveMore = function (groupId,fieldCode,type) {
           /* var time = "";
            if(type == 1){
                time = scoreStartTime
            }else{
                time = startTime - 1
            }*/
            var title = '风研成果列表';
            var target =
                '/views/audit/bigdata/remoteDataKanban/moreResearchAchievements.html?involveProvince=&true&startTime=' +
                startTime + "&endTime=" + endTime +"&groupId="+groupId+"&fieldCode="+fieldCode;
            $("#tableMenu").data("title", title).data("url", target).click();
        };
        //风研成果：更多
        window.achieveMore2 = function (groupId,fieldCode) {
            var title = '风研成果列表';
            var time = startTime - 1
            var target =
                '/views/audit/bigdata/remoteDataKanban/moreResearchAchievements.html?involveProvince=&true' +
                '&startTime='+time+ "&endTime=" + endTime +
                "&scoreStartTime=" +scoreStartTime + "&scoreEndTime=" + endTime +"&groupId="+groupId+"&fieldCode="+fieldCode;
            $("#tableMenu").data("title", title).data("url", target).click();
        };
        //团队看板：更多
        window.groupMore = function (groupType) {
            var title = '风研团队看板';
            var target =
                '/views/audit/colligate/morder/common/teamKanban.html?true&startTime=' +
                startTime + "&endTime=" + endTime+"&groupType="+groupType;
            $("#tableMenu").data("title", title).data("url", target).click();
        };
        //团队详情
        window.showGroupInfo = function(groupId){
            var title = '团队详情';
            var target ='/views/audit/colligate/riskResearch/TeamManagement/groupOriginalData.html?groupId='+groupId;
            $("#tableMenu").data("title", title).data("url", target).click();
        }

        /*//顶部任务数钻取
        window.taskClick = function (type) {
            var url = '/views/audit/colligate/morder/baseinfo/mysearch.html?finishedFlag=false&leaderEntry=true&orderMode=' +
                type + '&startTime=' + startTime + '&endTime=' + endTime + '&oneLevelTask=';
            $("#tableMenu").data("title", "任务列表").data("url", url).click();
        };

        //顶部成果数钻取
        window.achieveClick = function () {
            var url = "/views/audit/colligate/morder/baseinfo/achievesearch.html?leaderEntry=true&startTime=" + startTime + '&endTime=' + endTime;
            $("#tableMenu").data("title", "成果列表").data("url", url).click();
        };

        //成果应用数钻取
        window.applicationClick = function () {
            var url = "/views/audit/colligate/morder/baseinfo/centralPlatformDetail/applicationList.html?startTime=" + startTime + "&endTime=" + endTime;
            $("#tableMenu").data("title", "成果应用列表").data("url", url).click();
        };

        //顶部问题数钻取
        window.questionClick = function () {
            var url = "/views/audit/colligate/forum/forumHomePage.html?startTime=" + startTime + "&endTime=" + endTime;
            $("#tableMenu").data("title", "问题列表").data("url", url).click();
        };*/

        //柱状图任务总数钻取
        window.taskNumbersEchart1 = function (type, code) {
            var url = "", title = "";
            if (type == 0) { //点击任务
                url = "/views/audit/colligate/morder/baseinfo/mysearch.html?finishedFlag=false&leaderEntry=true&startTime=" + startTime + "&endTime=" +
                    endTime + "&oneLevelTask=" + code;
                title = "任务列表";
            } else { //点击成果
                url = "/views/audit/colligate/morder/baseinfo/achievesearch.html?leaderEntry=true&startTime=" + startTime + "&endTime=" +
                    endTime + "&oneLevelTask=" + code;
                title = "成果列表";
            }
            $("#tableMenu").data("title", title).data("url", url).click();
        };
        //排序列表切换
        $("#earlyType .layui-select-btn-span").on('click', function () {
            achieveListIndex = $(this).attr('layui-type');
            $("#earlyType .layui-select-btn-span").removeClass('active');
            $(this).addClass('active');
            achieveSort()
        });

        //应用成果列表钻取
        window.openDetailModal = function (url, title, width, height) {
            $("#tableMenu").data("title", title).data("url", "/" + url).click();
        };

        //要素成果名称钻取
        window.openDeailMorderInfo = function (resultDetailId, resultTypeCode) {
            var url = "";
            if ("FINAL_RESULT_SUBJECT" === resultTypeCode) {
                url = "/views/audit/colligate/morder/common/programmeFinalResult_show.html?resultId=" + resultDetailId;
            } else if ("FINAL_RESULT_MODEL" === resultTypeCode) {
                url = "/views/audit/colligate/morder/common/modelFinalResult_show.html?resultId=" + resultDetailId;
            } else if ("FINAL_RESULT_PROGRAMME" === resultTypeCode) {
                url = "/views/audit/colligate/morder/common/programmeFinalResult_show.html?resultId=" + resultDetailId;
            } else {
                url = "/views/audit/colligate/morder/common/monitorFinalResult_show.html?resultId=" + resultDetailId;
            }
            $("#tableMenu").data("title", "成果查看").data("url", url).click();

        };

        //要素成果看板--应用钻取
        window.cgUseNumber = function (statisticsCode, resultId) {
            var url = "/views/audit/colligate/morder/baseinfo/centralPlatformDetail/applicationListNewCentral.html?&fixGroupId=&groupLeaderLoginName=&statisticsCode=" +
                statisticsCode + "&resultId=" + resultId + "&startTime=" + startTime + "&endTime=" + endTime;
            $("#tableMenu").data("title", "应用列表").data("url", url).click();
        };

        //应用/要素成果切换
        $("#middleGround .layui-select-btn-span").on('click', function () {
            middleGIndex = $(this).attr('layui-type');
            middleGIndex === '0' ? $('.middleText').text('暂无风研成果...') : $('.middleText').text('暂无风研成果...')
            $("#middleGround .layui-select-btn-span").removeClass('active');
            $(this).addClass('active');
            initAchieveData()
            initAchieveList()
        });

        //账期type切换事件
        layui.form.on('radio(loadGroup)', function (data) {
            console.log(data);
            grouptype = data.value;
            loadGroup();
        });


        //小组看板--小组人数
        window.memberNumber = function (leaderEntry, fixGroupId, groupLeaderLoginName) {
            var relationParameter = '';
            if (fixGroupId != '') {
                relationParameter = fixGroupId;
            } else {
                relationParameter = groupLeaderLoginName;
            }
            var url = '/views/audit/colligate/morder/baseinfo/centralPlatformDetail/memberNumber.html?relationParameter=' + relationParameter;
            $("#tableMenu").data("title", "小组成员").data("url", url).click();
        };

        //小组看板--成果钻取
        window.resultNumber = function (leaderEntry, fixGroupId, groupLeaderLoginName) {
            var url = '/views/audit/colligate/morder/baseinfo/centralPlatformDetail/groupAchievement.html?leaderEntry=' +
                leaderEntry + "&fixGroupId=" + fixGroupId + "&groupLeaderLoginName=" + groupLeaderLoginName +
                "&startTime=" + startTime + "&endTime=" + endTime;
            $("#tableMenu").data("title", "成果列表").data("url", url).click();
        };

        //小组看板--任务总数
        window.taskNumber = function (leaderEntry, fixGroupId, groupLeaderLoginName) {
            var url = '/views/audit/colligate/morder/baseinfo/centralPlatformDetail/groupTaskNumber.html?leaderEntry=' +
                leaderEntry + "&fixGroupId=" + fixGroupId + "&groupLeaderLoginName=" + groupLeaderLoginName +
                "&startTime=" + startTime + "&endTime=" + endTime;
            $("#tableMenu").data("title", "任务列表").data("url", url).click();
        };

        //小组看板--应用
        window.useNumber = function (leaderEntry, fixGroupId, groupLeaderLoginName) {
            var url = '/views/audit/colligate/morder/baseinfo/centralPlatformDetail/applicationList.html?leaderEntry=' +
                leaderEntry + "&fixGroupId=" + fixGroupId + "&groupLeaderLoginName=" + groupLeaderLoginName +
                "&startTime=" + startTime + "&endTime=" + endTime;
            $("#tableMenu").data("title", "应用列表").data("url", url).click();
        };

        //小组看板--成就值
        window.achievementValue = function (leaderEntry, fixGroupId, groupLeaderLoginName) {
            var url = '/views/audit/colligate/morder/baseinfo/centralPlatformDetail/groupAchievementValue.html?leaderEntry=' +
                leaderEntry + "&fixGroupId=" + fixGroupId + "&groupLeaderLoginName=" + groupLeaderLoginName +
                "&startTime=" + startTime + '&endTime=' + endTime + "&type=" + grouptype;
            $("#tableMenu").data("title", "成就值信息").data("url", url).click();
        };




        // echarts成就值点击钻取
        window.ehartsAchievement = function (loginName, categoryCode) {
            var url = '/views/audit/colligate/morder/baseinfo/centralPlatformDetail/eachertsAchievementValueDetail.html?loginName=' +
                loginName + '&startTime=' + startTime + '&endTime=' + endTime;
            if (categoryCode) {
                url = url + "&categoryCode=" + categoryCode;
            }
            $("#tableMenu").data("title", "人员成就值明细").data("url", url).click();
        };


        //独立刷新
        $(".order-button-update").on('click', function () {
            var type = $(this).attr('layui-type');
            if (type == 1) {
                initChart1Data();
            } else if (type == 2) {
                achieveSort();
            } else if (type == 3) {
                middleGApply = ''
                middleGResult = ''
                fileCode = '';
                initAchieveData();
                initAchieveList();
            } else if (type == 4) {
                loadGroup()
            } else if (type == 5) {
                achieveType === 'prov' ? initChart5Data() : initChart6Data()
            }
            buttonUpdateAnimation(this);
        });
        //账期type切换事件
        layui.form.on('radio(quickchoose)', function (data) {
            datetype = +data.value;
            var monthId;
            if (!datetype) {
                $('#payment').attr("disabled", false)
                return
            } else if (datetype == 1) {
                monthId = (new Date()).getFullYear() + " - " + (new Date()).getFullYear();
            } else if (datetype == 2) {
                monthId = ((new Date()).getFullYear() - 1) + " - " + ((new Date()).getFullYear() - 1);
            }
            startTime = monthId.split(' - ')[0];
            scoreStartTime = startTime;
            endTime = monthId.split(' - ')[1];
            $("#payment").val(monthId);
            $('#payment').attr("disabled", true);
            onloadFunction()
        });

        /**
         * 任务标题下钻，任务详情
         * @param id
         */
        openDatilMorderInfos = function (inidata) {
            // TODO:
            var url = '/views/audit/colligate/morder/homepagedrilldown/resultDigestTaskNameDrillDown.html?orderId=' + inidata;
            $("#tableMenu").data("title", "任务详情").data("url", url).click();
            // top.layer.open({
            //   type: 2,
            //   title: "任务详情",
            //   content: 'views/audit/colligate/morder/homepagedrilldown/resultDigestTaskNameDrillDown.html',
            //   area: ['95%', '99%'],
            //   success: function (layero, index) {
            //     //得到当前层里面的iframe
            //     var eform = layero.find('iframe').contents();
            //     //填充内容
            //     fillFormFields(inidata, eform);
            //     //获取弹框内容对象(在layer.open 的 success 回调中使用一下代码获取弹窗的window中的layer,form对象，首次加载会找不到，取决于网速快慢，
            //     // 慢点网速会造成弹窗资源未加载完毕就被调用，从而报错；浏览器缓存后不会报错；解决方案是此处不访问弹框中的layer，改用其他方法)
            //     var iframeWindow = top.window['layui-layer-iframe' + index];
            //     // 传递弹窗index
            //     iframeWindow.passByParams.selfLayerIdx = index;

            //     // 单纯绑定关闭按钮事件(不许提交数据可这样绑定)
            //     var closeBtnID = 'morder-edit-close';
            //     var clsbtn = layero.find('iframe').contents().find('#' + closeBtnID);
            //     clsbtn.on('click', function () {
            //       top.layer.close(index);
            //     });
            //     //传递参数
            //     if (typeof iframeWindow.passByParams.initPage != "undefined") {
            //       iframeWindow.passByParams.initPage(inidata);
            //     }
            //   },
            //   end: function () {
            //   }
            // });
        };

        //我要发榜
        window.openMorderInfo = function () {
            layer.open({
                type: 1,
                title: '发布任务',
                area: ['800px', '330px'],
                content: $("#openMorder"),
                end: function () {
                    $('#openMorder').hide();
                }
            });
        };
        //我要发榜-确认
        window.taskAdd = function (type) {
            layer.closeAll();
            if (type === 1) {//派单
                openMorderInfos({
                    orderType: CNST_ORDER_TYPE_SEND,
                    orderStatus: CNST_ORDER_STATUS_DRAFT,
                    orderTaskType: CNST_ORDER_TASK_JTDEPT
                }, '新建任务');
            } else if (type === 2) {//抢单
                openMorderInfos({
                    orderType: CNST_ORDER_TYPE_GRAB,
                    orderStatus: CNST_ORDER_STATUS_DRAFT,
                    orderTaskType: CNST_ORDER_TASK_JTDEPT
                }, '新建任务');
            } else if (type === 3) {//数据需求
                top.layer.open({
                    type: 2,
                    title: '数据申请',
                    content: 'views/audit/bigdata/applicationProcess/dataApplication.html',
                    area: ['95%', '99%'],
                    success: function (layero, index) {

                    }
                })
            }
        };
        // //发起风研会议
        // window.riskOpen = function(){
        //     top.layer.open({
        //         type: 2,
        //         title: '风研会议',
        //         content: 'views/audit/colligate/morder/baseinfo/risk/addRisk.html',
        //         area: ['80%', '80%'],
        //         success: function (layero, index) {
        //
        //         }
        //     })
        // };
        //打开新页面
        openMorderInfos = function (inidata, title) {
            top.layer.open({
                type: 2,
                title: title,
                content: 'views/audit/colligate/morder/baseinfo/morder.html',
                area: ['95%', '99%'],
                success: function (layero, index) {
                    var eform = layero.find('iframe').contents();
                    fillFormFields(inidata, eform);
                    //初始化编辑界面
                    if (inidata.orderType == CNST_ORDER_TYPE_SEND) {
                        eform.find('#divOptTask').hide();
                        eform.find('#divOptRefPrj').hide();
                    } else {
                        eform.find('#divOptTask').hide();
                        eform.find('#divOptRefPrj').hide();
                    }
                    if (inidata.isImportant == '1') {
                        eform.find('#isImportantCheckBox').attr("checked", "checked");
                    }
                    //获取弹框内容对象(在layer.open 的 success 回调中使用一下代码获取弹窗的window中的layer,form对象，首次加载会找不到，取决于网速快慢，
                    // 慢点网速会造成弹窗资源未加载完毕就被调用，从而报错；浏览器缓存后不会报错；解决方案是此处不访问弹框中的layer，改用其他方法)
                    var iframeWindow = top.window['layui-layer-iframe' + index];
                    // 传递弹窗index
                    iframeWindow.passByParams.selfLayerIdx = index;
                    //iframeWindow.passByParams.refTblIns = morderTbl;

                    // 单纯绑定关闭按钮事件(不许提交数据可这样绑定)
                    var closeBtnID = 'morder-edit-close';
                    var clsbtn = layero.find('iframe').contents().find('#' + closeBtnID);
                    clsbtn.on('click', function () {
                        top.layer.close(index);
                    });
                    if (typeof iframeWindow.passByParams.initPage != "undefined") {
                        iframeWindow.passByParams.initPage(inidata);
                    }
                },
                end: function () {
                    var data = JSON.stringify({
                        createLoginName: $("#createLoginName").val(),
                        orderTaskType: orderTaskType
                    });
                    loadMorder(data);
                }
            });
        };
        //我要提问和我要查询
        //我要揭榜和我要查询
        window.openOrderCommOnlist = function (isLoad) {
            layui.use(['jqelem', 'jqbind', 'jqform', 'table', 'jqajax', 'laydate', 'util', 'carousel'],
                function () {
                    var $ = layui.jquery;
                    var url = "";
                    var title = '工单中台';
                    if (isLoad == 1) {
                        url = "/views/audit/colligate/morder/baseinfo/mysearch.html?finishedFlag=false&leaderEntry=true&startTime=" + startTime + "&endTime=" + endTime;
                        title = '任务查询';
                    } else if (isLoad == 2) {
                        title = '审计报告';
                        url = '/views/audit/colligate/forum/auditReport.html';
                        // url = '/views/audit/pro/reform/problemQuery/problemQueryin.html';
                    } else if (isLoad == 4) {
                        title = '问题查询';
                        url = "/views/audit/colligate/forum/forumHomePage.html"
                    } else if (isLoad == 5) {
                        title = '提问/回答';
                        url = "/views/audit/colligate/forum/forumHomePage.html";
                    } else if (isLoad == 6) {
                        title = '我要提问';
                        url = "/views/audit/colligate/forum/forumHomePage.html?status=3&startTime=" + startTime + "&endTime=" + endTime;
                    } else if (isLoad == 7) {
                        title = '成果列表';
                        url = "/views/audit/colligate/morder/baseinfo/centralPlatformDetail/achievesearchEcharts.html?leaderEntry=false&startTime=" + startTime + "&endTime=" + endTime;
                    } else if (isLoad == 8) {
                        title = '成果应用查询';
                        url = "/views/audit/colligate/morder/baseinfo/centralPlatformDetail/applicationList.html";
                    } else if (isLoad == 9) {
                        title = '应用列表';
                        url = "/views/audit/colligate/morder/baseinfo/centralPlatformDetail/applicationList.html?leaderEntry=true"
                    }else if (isLoad == 10) {
                        title = '风研成果应用';
                        url = '/views/audit/bigdata/remoteDataKanban/apply/list.html';
                    }else if (isLoad == 11) {
                        title = '审计通报应用';
                        url = '/views/audit/bigdata/auditbulletin/auditbulletinIndex.html';
                    }
                    $("#tableMenu").data("title", title).data("url", url).click();
                });
        };
        /**
         * 我要提问
         */
        window.askQuestions = function () {
            top.layer.open({
                type: 2,
                title: '我要提问',
                content: 'views/audit/colligate/forum/askQuestions.html',
                area: ['80%', '95%'],
                success: function (layero, index) {

                },
                end: function () {
                }
            });
        }


        window.showProStatus = function (obj) {
            var objText = $(obj).attr('data-title1')
            var objText2 = $(obj).attr('data-title2')
            var text = ''
            if (objText || objText2) {
                text = '重要性：' + objText + '<br/>普遍性：' + objText2
                layer.tips(text, obj, {
                    tips: 1,
                })
            }

        }

        // 表格按钮查看
        window.openMyOpu = function (id) {
            var msg = "";
            // 根据\n将返回文字换行
            if (tableData.length > 0) {
                for (var i = 0; i < tableData.length; i++) {
                    if (id == tableData[i].id) {
                        msg = tableData[i].cases.replace(/\n/g, "<br/>");
                    }
                }
            }
            var html = '<div style="line-height:35px">';
            str = msg
            html +=
                '<div class="mag-header-titles" style="font-size:16px;overflow-y: auto;">' +
                str +
                "</div>";

            html += "</div>";
            top.layer.alert(html, { area: ["550px", "500px"] }, function (idx) {
                layer.close(idx);
                top.layer.close(idx);
                layer.closeAll();
            });
        };
        window.openTopic = function () {
            top.layer.open({
                type: 2,
                title: '专题',
                content: 'views/audit/colligate/morder/baseinfo/topicDrill.html',
                area: ['90%', '90%'],
                success: function (layero, index) {
                },
                end: function () {
                }
            });
        };
        window.openSubTopic = function () {
            top.layer.open({
                type: 2,
                title: '专题',
                content: 'views/audit/colligate/morder/baseinfo/subTopicDrill.html',
                area: ['90%', '90%'],
                success: function (layero, index) {
                },
                end: function () {
                }
            });
        };

        //触发项目
        window.openCfxm = function (index,id) {
            if(index == 1){
                var time = startTime - 1;
                top.layer.open({
                    type: 2,
                    title: '触发项目',
                    content: 'views/audit/colligate/morder/baseinfo/cfxm-table.html?startTime=' + time + '&endTime=' + endTime +"&scoreStartTime=" +scoreStartTime + "&scoreEndTime=" + endTime + '&types=1',
                    area: ['90%', '80%'],
                    success: function (layero, index) {
                    },
                    end: function () {
                    }
                });
            }else if(index>1){
                top.layer.open({
                    type: 2,
                    title: '触发项目',
                    content: 'views/audit/colligate/morder/baseinfo/cfxm-table.html?startTime=' + time +'&scoreStartTime='+scoreStartTime+ '&endTime=' + endTime + '&id=' + id   + '&types=2',
                    area: ['90%', '80%'],
                    success: function (layero, index) {
                    },
                    end: function () {
                    }
                });
            }else{
                var time = startTime - 1;
                top.layer.open({
                    type: 2,
                    title: '触发项目',
                    content: 'views/audit/colligate/morder/baseinfo/cfxm-table.html?startTime=' + time + '&endTime=' + endTime +"&scoreStartTime=" +scoreStartTime + "&scoreEndTime=" + endTime + '&types=1',
                    area: ['90%', '80%'],
                    success: function (layero, index) {
                    },
                    end: function () {
                    }
                });
            }

        }
        //通报问题

        window.openSjtb = function () {
            var time = startTime - 1;
            top.layer.open({
                type: 2,
                title: '审计通报',
                content: 'views/audit/colligate/morder/baseinfo/sjtb-table.html?startTime=' + time +"&scoreStartTime=" +scoreStartTime + '&endTime=' + endTime + '&types=1',
                area: ['90%', '80%'],
                success: function (layero, index) {
                },
                end: function () {
                }
            });
        }

        window.openSjtbTable = function(index,num,id){

             if(index == 1){
                 openSjtb();
             }else{
                 var time = startTime - 1;
                 top.layer.open({
                     type: 2,
                     title: '审计通报',
                     content: 'views/audit/colligate/morder/baseinfo/sjtb-table.html?startTime=' + time +'&scoreStartTime='+scoreStartTime+  '&endTime=' + endTime + '&types=2' + '&id=' + id,
                     area: ['90%', '80%'],
                     success: function (layero, index) {
                     },
                     end: function () {
                     }
                 });
             }
            /*if(num==1){
                $.ajax({
                    type: 'post',
                    url: ctx + '/bdata/leaderHomeDetail/queryTongBaoDetail',
                    dataType: "json",
                    data: JSON.stringify({
                        startTime: startTime,
                        endTime: endTime,
                        type:2,
                        id:id
                    }),
                    contentType: "application/json;charset=UTF-8",
                    success: function (ret) {
                        if (ret.httpCode === 200) {
                            var data = ret.data;
                            openPreViewName(data[0].ATTACHMENT_ID, htmlEncodeByRegExp(data[0].ATTACHMENT_NAME))
                        }
                    }
                });
            }*/
        }

        //发现问题
        window.openFxwt = function () {
            var time = startTime - 1;
            top.layer.open({
                type: 2,
                title: '发现问题',
                content: 'views/audit/colligate/morder/baseinfo/fxwt-table.html?startTime=' + time +"&scoreStartTime=" +scoreStartTime +  '&endTime=' + endTime + '&types=1',
                area: ['90%', '80%'],
                success: function (layero, index) {
                },
                end: function () {
                }
            });
        }
        //风研积分攥取
        window.openScore = function(groupName,researchIds,userName,provinceCode,scoreType){
            var paramsData = JSON.stringify({
                // startTime:startTime,查询积分不查询积分来源时间范围
                // endTime:endTime
            })
            var title = '风研积分列表';
            var target =
                '/views/audit/bigdata/remoteDataKanban/integralDetailTab.html?true&startTime=' +
                startTime + "&endTime=" + endTime+'&groupName='+encodeURIComponent(groupName)
                +"&researchIds="+researchIds+"&userName="+encodeURIComponent(userName)
                +"&provinceCode="+provinceCode+"&paramsData="+encodeURI(paramsData)
                +"&scoreType="+encodeURI(scoreType);
            $("#tableMenu").data("title", title).data("url", target).click();
        }


        window.openFxwtTable = function (index,num,id) {
            if(index== 1){
                openFxwt();
            }else{
                var time = startTime - 1;

                if(num>1){
                    top.layer.open({
                        type: 2,
                        title: '发现问题',
                        content: 'views/audit/colligate/morder/baseinfo/fxwt-table.html?startTime=' + time +'&scoreStartTime='+scoreStartTime+ '&endTime=' + endTime  + '&id=' + id + '&types=2',
                        area: ['90%', '80%'],
                        success: function (layero, index) {
                        },
                        end: function () {
                        }
                    });
                }
                if(num==1){
                    $.ajax({
                        type: 'post',
                        url: ctx + '/bdata/leaderHomeDetail/queryWenTiDetail',
                        dataType: "json",
                        data: JSON.stringify({
                            startTime: time,
                            scoreStartTime: scoreStartTime,
                            endTime: endTime,
                            type:2,
                            id:id
                        }),
                        contentType: "application/json;charset=UTF-8",
                        success: function (ret) {
                            if (ret.httpCode === 200) {
                                var data = ret.data;
                                openFxwtPreView(data[0].ACHIEVEMENT_ID)
                            }
                        }
                    });
                }
            }
        }

        window.openFxwtPreView = function(problemId){
            var  flag = '2'
            top.layer.open({
                    type: 2,
                    title: '问题详情',
                    content: "views/audit/pro/reform/reformreadrecord/reformTodoTaskBaseQuery.html?problemId="+problemId+"&flag="+flag,
                    area: ['90%', '80%'],
                    success: function (layero, index) {
                    },
                    end: function () {
                    }
                });
        }


        //预警单
        window.openYjdPreView = function(index,num,id){
            var time = startTime - 1;
            if(index == 1){
                top.layer.open({
                    type: 2,
                    title: '风险预警单',
                    content: 'views/audit/colligate/morder/baseinfo/selectRiskData.html?startTime=' + time +"&scoreStartTime=" +scoreStartTime + '&endTime=' + endTime  +  '&types=1',
                    area: ['90%', '80%'],
                    success: function (layero, index) {
                    },
                    end: function () {
                    }
                });
            }else{
            if(num==1){
                $.ajax({
                    type: 'post',
                    url: ctx + '/bdata/leaderHomeDetail/selectRiskData',
                    dataType: "json",
                    data: JSON.stringify({
                        startTime:time,
                        scoreStartTime: startTime,
                        endTime: endTime,
                        id:id
                    }),
                    contentType: "application/json;charset=UTF-8",
                    success: function (ret) {
                        if (ret.httpCode === 200) {
                            var data = ret.data;
                            opennYjdPreView(data[0].ID)

                        }
                    }
                });
            }
            if(num>1){
                top.layer.open({
                    type: 2,
                    title: '风险预警单',
                    content: 'views/audit/colligate/morder/baseinfo/selectRiskData.html?startTime=' + time +"&scoreStartTime=" +scoreStartTime + '&endTime=' + endTime  + '&id=' + id+  '&types=2',
                    area: ['90%', '80%'],
                    success: function (layero, index) {
                    },
                    end: function () {
                    }
                });
            }
            }

        }


        window.opennYjdPreView = function(researchInfoId){
            top.layer.open({
                    type: 2,
                    title: '风险预警单详情',
                    content: "views/audit/bigdata/remoteDataKanban/apply/detailWarningForm.html?researchInfoId=" + researchInfoId,
                    area: ['90%', '80%'],
                    success: function (layero, index) {
                    },
                    end: function () {
                    }
                });
        }

        //展示全部数据
        window.openNewTable = function(obj,index,indexobj){
            $('.layui-layer-content-my').show()
            $('#lastRisk').empty()
            var html = ''
            if(tableData[index][indexobj]){
                if(indexobj=='checkProv'){
                    var provNames = [];
                    for (var i = 0; i < tableData[index][indexobj].length; i++) {
                        // 检查 status 是否为 1
                        var color = tableData[index][indexobj][i].status == "1" ? "text-red" : "";
                        provNames.push(`<span class="${color}">${tableData[index][indexobj][i].provName}</span>`);
                    }
                    html =  '<div>' + provNames.join('、') + '</div>';
                }else if(indexobj=='pushProv'){
                    var provNames = [];
                    for (var i = 0; i < tableData[index][indexobj].length; i++) {
                        // 检查 status 是否为 1
                        var color = tableData[index][indexobj][i].status == "1" ? "text-red" : "";
                        provNames.push(`<span class="${color}">${tableData[index][indexobj][i].provName}</span>`);
                    }
                    html =  '<div>' + provNames.join('、') + '</div>';

                }else{
                    html =  tableData[index][indexobj]
                }
            }
            console.log(html)
            $('#lastRisk').append(html)
            console.log($(obj).offset())


            $('.layui-layer-content-my').css('left',$(obj).offset().left).css('top',$(obj).offset().top)
        }

        window.openNewTableRisk = function(obj,index,indexobj){
            $('.layui-layer-content-my').show()
            $('#lastRisk').empty()
            var html = ''
            if(tableData[index-1].initialFocusRiskProvList.length>0){
                html =  tableData[index-1].initialFocusRiskProvList[indexobj].INITIAL_FOCUS_RISK
              // tableData[index-1].initialFocusRiskList.forEach(function(item){
              //     html += '<span>' + item + '</span></br>'
              // })
            }
            console.log(html)
            $('#lastRisk').append(html)
            console.log($(obj).offset())


            $('.layui-layer-content-my').css('left',$(obj).offset().left).css('top',$(obj).offset().top)
          }
        // 星级展示
        window.openNewTableRiskStars = function(obj,index){
            $('.layui-layer-content-my').show()
            $('#lastRisk').empty()
            var titles = ''
                    titles += '<span>' + '重要性：' + tableData[index-1].riskImportant + '</span></br>'
                    titles += '<span>' + '普遍性：' + tableData[index-1].riskUniversal + '</span>'

            console.log(titles)
            $('#lastRisk').append(titles)
            console.log($(obj).offset())


            $('.layui-layer-content-my').css('left',$(obj).offset().left).css('top',$(obj).offset().top)
        }



        //查看聚焦风险
        window.initialFocusRisk = function (obj,index,indexobj) {
            $('.layui-layer-content-my').show()
            $('#lastRisk').empty()
            var titles = tableData[index].initialFocusRisk
            $('#lastRisk').append(titles)
            $('.layui-layer-content-my').css('left',$(obj).offset().left).css('top',$(obj).offset().top)

        }

          window.closeRisk = function(){
              $('.layui-layer-content-my').hide()
          }


    })
</script>

