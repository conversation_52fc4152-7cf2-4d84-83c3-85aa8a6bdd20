var $, layer, form, frm, element, table, ctx, flag, assigneeInput, assigneeNoRole, assigneeNeedRoleName, assigneeNeedRoleCode;
var iframe=document.getElementById('businessIframe');
var root = top.global.ctx;
if(typeof(rootService) == "undefined"||!rootService){//rootService未创建

}else{
    root =  rootService;
}
layui.use(['element','jqform', 'layer', 'jqfrm', 'table'], function() {

    $ = layui.jquery;
    layer = layui.layer;
    form = layui.jqform;
    frm = layui.jqfrm;
    element = layui.element;
    table = layui.table;
    ctx = top.global.ctx;

    //兼容ie浏览器撑开高度
    $('#tabContent').height($(window).height()-118);
    window.onresize = function(){
        $('#tabContent').height($(window).height()-118);
    };

    form.on('switch(sendMsg)', function (data) {
        if(data.elem.checked){
            $('#sendMsg').val('1');
        }else{
            $('#sendMsg').val('0');
        }
        return false;
    });

    $("#root").val(ctx);
    // 加密的taskId
    taskId = getUrlParam("taskId");
    // 经过RSA私钥加密过的key
    var signKey = getUrlParam("signKey");
    // 为了处理完流程后待办，云门户过来的待办直接关闭window
    flag = getUrlParam("flag");

    // 获取流程相关变量并赋值
    $.post(ctx + "/workflowrender/taskToDoDetail", {taskId:taskId,signKey:signKey},
        function (data) {
        console.log(data)
            if (data.httpCode == '200') {
                if(data.root){
                    root=data.root;
                    $("#root").val(root);
                }
                $("#isCounterSign").val(data.isCounterSign);
                $("#taskDefinitionKey").val(data.taskDetail.taskDefinitionKey);
                $("#name").val(data.taskDetail.name);
                $("#processInstanceId").val(data.taskDetail.processInstanceId);
                $("#processDefinitionId").val(data.taskDetail.processDefinitionId);
                $("#processDefinitionKey").val(data.taskDetail.processDefinitionKey);
                $("#title").val(data.taskDetail.title);
                $("#executionId").val(data.taskDetail.executionId);
                $("#assigneeId").val(data.taskDetail.assigneeId);

                // 重新将takId赋值，这是次明文
                taskId = data.taskDetail.taskId;
                $("#taskId").val(taskId);

                $("#canQuickPush").val(data.canQuickPush);
                $("#buttonBack").val(data.buttonBack);
                $("#buttonTurn").val(data.buttonTurn);
                $("#buttonQuick").val(data.buttonQuick);
                $("#buttonBreak").val(data.buttonBreak);
                $("#isSendRecord").val(data.isSendRecord);

                // 操作提交还是回退，默认提交，目的是为了获取下环节处理人不同接口
                var activeBtn = true;

                form.init({
                    form: "#nextPanelForm"
                });
                processDefinitionId = $('#processDefinitionId').val();
                processInstanceId = $('#processInstanceId').val();
                taskDefinitionKey = $('#taskDefinitionKey').val();
                processDefinitionKey = $('#processDefinitionKey').val();
                taskId = $('#taskId').val();
                taskName = $('#name').val();
                executionId = $('#executionId').val();

                taskVo = {
                    processDefinitionId: processDefinitionId
                    , processInstanceId: processInstanceId
                    , taskDefinitionKey: taskDefinitionKey
                    , processDefinitionKey: processDefinitionKey
                    , taskId: taskId
                    , executionId: executionId
                };

                // 下环节选择绑定事件
                form.on('select(refreshNextAssignee)', function (data) {
                    var selectedIndex = data.elem.options.selectedIndex;
                    var linkName = data.elem.options[selectedIndex].label;
                    var taskDefinitionKey = data.value.split(',')[0];
                    refreshAssignee(processDefinitionKey, taskDefinitionKey, linkName);
                });

                // 加载主业务页面及自定义页面,以及流转历史
                loadTaskPath(ctx, processInstanceId, taskId, taskDefinitionKey, processDefinitionId, $);

                // 是否为会签节点
                var isCounterSign = $('#isCounterSign').val();
                // 分别处理回退、简退、转派及中止按钮显隐
                var buttonBack = $('#buttonBack').val();
                var buttonQuick = $('#buttonQuick').val();
                var buttonTurn = $('#buttonTurn').val();
                var buttonBreak = $('#buttonBreak').val();

                // 据是否为会签节点处理按钮事件及显隐
                if (isCounterSign == 'true') {
                    // 通过按钮事件添加
                    $('#passButton').show();
                    $('#passButton').click(function () {
                        $(this).attr("disabled",'disabled')
                        addCommentForVote(true, layer, $);
                    });
                    // 拒绝按钮事件添加及名称
                    $('#backButton').text("拒绝");
                    $('#backButton').show();
                    $('#backButton').click(function () {
                        addCommentForVote(false, layer, $);
                    });
                } else {
                    // 当前环节是否可中止
                    if (buttonBreak == 'true') {
                        $('#stopButton').show();
                        $('#stopButton').click(function () {
                            addCommentForStop(layer, $);

                        });
                    }
                    // 当前环节是否可转派
                    if (buttonTurn == 'true') {
                        $('#turnButton').show();
                        $('#turnButton').click(function () {
                            addCommentForTurn(layer, $);
                        });
                    }
                    // 当前环节是否可回退
                    if (buttonBack == 'true') {
                        $('#backButton').show();
                        $('#backButton').click(function () {
                            addComment(false, layer, $);
                        });
                    }
                    // 当前环节是否可进行简退操作（必然为非首环节）
                    if (buttonQuick == 'true') {
                        $('#quickButton').show();
                        $('#quickButton').click(function () {
                            addCommentForQuick(layer, $);
                        });
                    }

                    // 是否为简退的退回首环节以进行直接推进操作
                    var canQuickPush = $('#canQuickPush').val();
                    if (canQuickPush == 'true') {
                        $('#passButton').show();
                        // 开始环节执行简退推进的事件绑定
                        $('#passButton').click(function () {
                            addCommentForStart(layer, $);
                        });
                    } else {
                        $('#passButton').show();
                        // 正常环节推进事件绑定
                        $('#passButton').click(function () {
                            addComment(true, layer, $);
                        });

                    }
                }

            } else {
                // 后台异常
                layer.alert(data.msg, function(idx){
                    layer.close(idx);
                    toTaskToDoList($);
                });
            }

        }, "json");

    /**
     * 根据任务ID获取实际任务办理页面，然后加载自定义tab，包括流转历史
     * @param ctx
     * @param processInstanceId
     * @param taskId
     * @param taskDefinitionKey
     * @param processDefinitionId
     * @param $
     * @param layer
     * @param element
     */
    function loadTaskPath(ctx, processInstanceId, taskId, taskDefinitionKey, processDefinitionId, $, layer, element) {
        // 请求得到实际业务路径
        $.post(ctx + "/workflowrest/tasktodopath/" + processInstanceId + "/" + taskDefinitionKey + "/" + taskId, null,
            function (data) {
                // data = JSON.parse(data);
                // 返回成功即继续处理，不成功报原因
                if (data.retCode == 1) {
                    // 获取url串及后续参数并赋值给公共参数对象
                    var url = top.global.ctxStatic + data.dataRows[0].url;
                    $('#param').val(url.substring(url.indexOf("?") + 1));
                    // 环节类型赋值
                    var startLink = data.dataRows[0].startLink;
                    var endLink = data.dataRows[0].endLink;
                    $('#startLink').val(startLink);
                    $('#endLink').val(endLink);

                    // 业务信息使用iframe形式加载待办页面
                    iframe.src = url;
                    //1秒延迟
                    setTimeout(function () {
                        $('#bottom-btn').show()
                    },1000)

                    // 业务信息使用div形式加载待办页面
                    //$("#businessIframe").load(url);

                } else if (data.retCode == 0) {
                    layer.alert(data.retValue);
                    setTimeout(function () {
                        $('#bottom-btn').show()
                    },1000)
                }
                // 主页面加载完成后加载自定义标签
                loadCustomTabs(ctx, processInstanceId, $('#param').val(), processDefinitionId, taskDefinitionKey, $, "2");
            }, "json");
    }

    var index;
    this.layerIndex = index;

    /**
     * 普通环节点击“通过”或“回退”按钮，添加评论
     * @param pass true 通过 false 退回
     * @param layer
     * @param $
     */
    function addComment(pass, layer, $) {

        // 增加各业务js判断,业务侧可能有非空的一些js校验，通过此方式即可判断
        var businMakeSure = true;
        if(pass){
            if(iframe.contentWindow.passValidate){
                businMakeSure = iframe.contentWindow.passValidate();
            }
        }else{
            if(iframe.contentWindow.backValidate){
                businMakeSure = iframe.contentWindow.backValidate();
            }
        }

        //todo 年度考核积分点击下一步失败，考核那里需要重新测试！
        if(businMakeSure!=undefined && !businMakeSure){
            return;
        }

        activeBtn = pass;
        if (pass) {
            $("#comment").val("");
            // 提交
            $("#handleType").val(1);
        } else {
            $("#comment").val("");
            // 回退
            $("#handleType").val(2);
        }
        refreshLink(layer, $);

    }
    // 会签环节点击“通过”或“拒绝”按钮，添加评论
    function addCommentForVote(pass, layer, $) {
        // 隐藏环节选择及受理人选择
        // 据按钮类型选择处理方式
        hideLinkDiv();
        if (pass) {
            $("#comment").val("");
            $("#handleType").val(1);
        } else {
            $("#comment").val("");
            $("#handleType").val(2);
        }
        openLinkForNoLink('300px', layer, $, 1);
    }

    //点击“中止”按钮，仅弹出意见填写窗口
    function addCommentForStop(layer, $) {

        // 增加各业务js判断,业务侧可能有非空的一些js校验，通过此方式即可判断
        var businMakeSure = true;
        if(iframe.contentWindow.stopValidate){
            businMakeSure = iframe.contentWindow.stopValidate();
        }

        if(businMakeSure!=undefined && !businMakeSure){
            return;
        }

        hideLinkDiv();
        $("#comment").val("");
        openLinkForNoLink('200px', layer, $, 2);
    }

    // 点击“简退”按钮，仅弹出意见填写窗口
    function addCommentForQuick(layer, $) {
        hideLinkDiv();
        $("#comment").val("");
        openLinkForNoLink('200px', layer, $, 3);
    }

    //简退环节后的首环节“推进”按钮，仅弹出意见填写窗口
    function addCommentForStart(layer, $) {
        hideLinkDiv();
        $("#comment").val("");
        openLinkForNoLink('200px', layer, $, 4);
    }

    // 点击“转派”按钮，仅弹出处理人选择及意见填写窗口
    function addCommentForTurn(layer, $) {
        // 处理人选择按钮方法重置
        $("#linkDiv").hide();
        $("#assigneeDiv").show();
        $("#sendmsgDiv").show();
        $("#comment").val("");


        // 增加各业务js判断,业务侧可能有非空的一些js校验，通过此方式即可判断
        // 校验完成之后重新触发
        // var btn = $('#turnButton', parent.document);
        // btn.click();
        var businMakeSures = true;
        //转派校验
        if(iframe.contentWindow.transferVerification){
            businMakeSures = iframe.contentWindow.transferVerification();
        }

        if(businMakeSures!=undefined && !businMakeSures){
            return;
        }

        refresTurnAssignne(layer,$);
    }

    //刷新转派的下一环节处理人
    function refresTurnAssignne(layer,$){
        $("#assignee").empty();
        var refreshAssigneeUrl = iframe.contentWindow.refreshAssigneeUrl();
        $.post(root + "/" + refreshAssigneeUrl + "/refreshTurnAssignee/", taskVo,
            function (data) {
                // 返回成功即继续处理
                if (data) {
                    if(data!=null && data.length>1){
                        $("#assignee").append("<option value=''>--请选择下一步处理人--</option>");
                    }
                    $.each(data, function (i, obj) {
                        if(obj.checkFlag=='1'){
                            $("#assignee").append("<option selected value='" + obj.value + "'>" + obj.label + "</option>");
                        }else{
                            $("#assignee").append("<option value='" + obj.value + "'>" + obj.label + "</option>");
                        }
                    });
                    form.render('select', 'assignee');
                    openLinkForNoLink('300px', layer, $, 5);
                }
            }, "json");
    }

    /**
     * 刷新环节下拉
     * @param layer
     * @param $
     */
    function refreshLink(layer, $) {
        $("#assigneeInputBlockDiv").hide();
        $("#assigneeBlockDiv").show();
        $("#assignee").empty();
        var processData = iframe.contentWindow.loadProcessData();
        // 清空环节
        $("#link").empty();
        var handleType = $("#handleType").val();
        $.post(ctx + "/workflowrest/tasklink/" + taskId + "/" + handleType + "/branch", processData,
            function (data) {
                data = JSON.parse(data);
                var success = data.retCode;
                // 返回成功即继续处理，不成功报原因
                if (success == 1) {
                    var link = data.dataRows;
                    if(link != null && link.length>1){
                        $("#link").append("<option value=''>--请选择下一步处理环节--</option>");
                    }
                    $.each(link, function (i, obj) {
                        if(obj.checkFlag=='1'){
                            $("#link").append("<option selected value='" + obj.value + "'>" + obj.label + "</option>");
                        }else{
                            $("#link").append("<option value='" + obj.value + "'>" + obj.label + "</option>");
                        }

                    });
                    form.render();

                    var taskDefinitionKey = $("#taskDefinitionKey").val();
                    var linkObj = $("#link")[0];
                    var selectedIndex = linkObj.options.selectedIndex;
                    var link_1 = linkObj.options[selectedIndex].value;
                    var linkName = linkObj.options[selectedIndex].label;
                    var linkAndWithdraw_1 = link_1.split(",");
                    taskDefinitionKey = linkAndWithdraw_1[0];
                    // 最后一环节无需通过时无需选择环节与处理人
                    if (iframe.contentWindow.project_todo) {
                        //流程环节判断用到的参数
                        var pDKey      = $("#processDefinitionKey").val();
                        var tDKey      = $("#taskDefinitionKey").val();
                        var link_1     = $("#link").val();
                        //流程推进用到的参数
                        var root       = $("#root").val();
                        var pInsId     = $("#processInstanceId").val();
                        var taskId     = $('#taskId').val();
                        var handleType = $('#handleType').val();
                        iframe.contentWindow.project_todo.refreshLinkCustom(pDKey,tDKey,root,pInsId,taskId,handleType,link_1,$,index,layer,form);
                    } else {
                        // 最后一环节无需通过时无需选择环节与处理人
                        if (link.length == 0 || (link.length == 1 && "END" == link[0].value.split(",")[2] )|| (link.length == 1 && (link[0].value.split(",")[2].indexOf("endevent") != -1))) {
                            hideLinkDiv();
                            endPushProcess($,1,layer);//流程结束不需要弹出意见框
                        } else {
                            showLinkDiv();
                            var handleType = $('#handleType').val();
                            openLink('270px', handleType,layer, $);
                            // 刷新人员，提交
                            refreshAssignee(processDefinitionKey, $("#link").val().split(",")[0], linkName);
                        }
                        if (link.length > 0) {
                            //是否显示推荐人
                            if ($('#isUseReferenceMan').val() == 'true') {
                                historyUser($('#processDefinitionId').val(), $('#taskDefinitionKey').val(), handleType, $("#link").val().split(",")[0]);
                            }
                        }
                    }
                } else if (success == 0) {
                    // 未找到匹配的可选环节，异常情况
                    layer.alert("未找到可流转环节，异常请处理！");
                }
            });
    }

    /**
     * 人员下拉列表,只用于流程提交或回退使用，用activeBtn来标识提交 true和回退 false
     * @param processDefinitionKey
     * @param taskDefinitionKey
     * @param linkName
     */
    function refreshAssignee(processDefinitionKey, taskDefinitionKey, linkName) {
        $("#assigneeInputBlockDiv").hide();
        $("#assigneeBlockDiv").show();
        // 环节没定，人员不获取
        if (taskDefinitionKey == '') {
            return;
        }
        //解决下环节多余一个时，选择下环节为结束环节时，不再刷新人员  qinsp  20210629
        if(taskDefinitionKey.indexOf("endevent") != -1){
            $("#assigneeDiv").hide();
            $("#sendmsgDiv").hide();
            return;
        }else{
            $("#assigneeDiv").show();
            $("#sendmsgDiv").show();
        }
        // 更新最新路径
        $("#assignee").empty();
        // 提交操作
        if (activeBtn) {
            var processData = iframe.contentWindow.loadProcessData();
            var refreshAssigneeUrl = iframe.contentWindow.refreshAssigneeUrl();
            var flag=false;
            // 获取实例ID环节LINK_ID
            processData = $.extend(processData, taskVo);

            $.post(root + "/" + refreshAssigneeUrl + "/refreshNextAssignee/" + processDefinitionKey + "/" + taskDefinitionKey, processData,
                function (data) {
                    // 返回成功即继续处理
                    if (data) {
                        if(data!=null && data.length>1){
                            $("#assignee").append("<option value=''>--请选择下一步处理人--</option>");
                        }
                        $.each(data, function (i, obj) {
                            console.info(obj);
                            // 无效人员
                            var disAble = "";
                            if(obj.disable == '0'){
                                disAble = "disabled=\"\"";
                                flag=true;
                            }
                            if(obj.checkFlag=='1'){
                                $("#assignee").append("<option selected value='" + obj.value + "'" + disAble + " >" + obj.label + "</option>");
                            }else{
                                $("#assignee").append("<option value='" + obj.value + "'" + disAble + " >" + obj.label + "</option>");
                            }
                        });
                        form.render();
                        if( flag|| data==null || data==''){
                            renderNoNextLinkUser($, ctx,refreshAssigneeUrl, processDefinitionKey, taskDefinitionKey, processData, layer, linkName, form);
                        }
                    }
                }, "json");
        } else {
            var processDefinitionId = $('#processDefinitionId').val();
            $.post(ctx + "/workflowrest/getIsCountersign/" + processDefinitionId + "/" + taskDefinitionKey,
                function (data) {
                    data = JSON.parse(data);

                    //退回环节为会签环节时，根据角色取人   2019-5-31 qinsp
                    var processData = iframe.contentWindow.loadProcessData();
                    var refreshAssigneeUrl = iframe.contentWindow.refreshAssigneeUrl();
                    // 获取实例ID环节LINK_ID
                    processData = $.extend(processData, taskVo);

                    if (data == "2") {

                        $.post(root + "/" + refreshAssigneeUrl + "/refreshNextAssignee/" + processDefinitionKey + "/" + taskDefinitionKey, processData,
                            function (data) {
                                // 返回成功即继续处理
                                if (data) {
                                    if(data!=null && data.length>1){
                                        $("#assignee").append("<option value=''>--请选择--</option>");
                                    }
                                    $.each(data, function (i, obj) {
                                        console.info(obj);
                                        // 无效人员
                                        var disAble = "";
                                        if(obj.disable == '0'){
                                            disAble = "disabled=\"\"";
                                        }
                                        if(obj.checkFlag=='1'){
                                            $("#assignee").append("<option selected value='" + obj.value + "'" + disAble + " >" + obj.label + "</option>");
                                        }else{
                                            $("#assignee").append("<option value='" + obj.value + "'" + disAble + " >" + obj.label + "</option>");
                                        }
                                    });
                                    form.render();
                                }
                            }, "json");
                    }else{

                        var identityFlag = processData.identityFlag;
                        //退回按照各业务进行定制取人规则
                        if (identityFlag == "identity") {

                            $.post(root + "/" + refreshAssigneeUrl + "/refreshBackAssignee/" + processDefinitionKey + "/" + taskDefinitionKey, processData,
                                function (data) {
                                    // 返回成功即继续处理
                                    if (data) {
                                        if(data!=null && data.length>1){
                                            $("#assignee").append("<option value=''>--请选择--</option>");
                                        }
                                        $.each(data, function (i, obj) {
                                            console.info(obj);
                                            // 无效人员
                                            var disAble = "";
                                            if(obj.disable == '0'){
                                                disAble = "disabled=\"\"";
                                            }
                                            if(obj.checkFlag=='1'){
                                                $("#assignee").append("<option selected value='" + obj.value + "'" + disAble + " >" + obj.label + "</option>");
                                            }else{
                                                $("#assignee").append("<option value='" + obj.value + "'" + disAble + " >" + obj.label + "</option>");
                                            }
                                        });
                                        form.render();
                                    }
                                }, "json");
                        }else {
                            //正常退回取环节处理人
                            $.post(ctx + "/workflowrest/refreshBackAssignee/" + processDefinitionKey + "/" + taskDefinitionKey, taskVo,
                                function (data) {
                                    // 返回成功即继续处理
                                    if (data) {
                                        // 无效人员
                                        var disAble = "";
                                        if (data.disable == '0') {
                                            disAble = "disabled=\"\"";
                                        }
                                        $("#assignee").append("<option value='" + data.value + "'" + disAble + " >" + data.label + "</option>");
                                        form.render();
                                    }
                                }, "json");
                        }
                    }
                }, "json");

        }
    }

//点击返回按钮，回到默认按钮并情况处理人信息
    function selectButton() {
        $("#in-footer").hide();
        clearAssignee();
    }

// 推动流程
    function pushProcess($, index, layer) {
        // 需提交处理的参数
        var taskDefinitionKey = '';
        var withdraw = 0;
        var assignee = '';

        // 获取目标环节定义及是否可撤回标志
        var link = $("#link").val();
        var linkName = $("#link option:selected").text();
        if (!$("#linkDiv").is(":hidden")) {
            var linkAndWithdraw = link.split(",");
            taskDefinitionKey = linkAndWithdraw[0];
            withdraw = linkAndWithdraw[1];

            if (taskDefinitionKey.length == 0) {
                layer.msg('请选择环节！');
                return;
            }
        } else {
            taskDefinitionKey = link.split(",")[0];
        }
        if (!$("#assigneeDiv").is(":hidden")) {
            assignee = $("#assignee").val();
            if (assigneeInput) {
                assignee = assigneeInput;
            }
            if (assignee == null || assignee == '') {
                layer.msg('请选择处理人！');
                return;
            }
        }

        var comment = $("#comment").val();
        if (comment.length == 0 ) {
            layer.msg('请填写审批意见！');
            return;
        }
        var regu = "^[ ]+$";
        var re = new RegExp(regu);
        if (re.test(comment) ) {
            layer.msg('审批意见不能填写空格！');
            return;
        }
        layer.confirm('是否确认提交？', {icon: 3, title: '提示'}, function (indexs) {
            layer.close(indexs);

            if (assigneeNoRole) {
                var userName = $("#assigneeInput").val();
                layer.confirm('确认为<span style="color: red;">' + userName + '</span>添加<span style="color: red;">' + assigneeNeedRoleName + '</span>的角色权限吗？',
                    {icon: 3, title: '提示'}, function (roleIndex) {
                        layer.close(roleIndex);
                        layer.open({
                            type: 3
                        });
                        automaticallyAddRole($, ctx);
                        //流程移动化标识
                        flowMobileInfoInit(
                            {
                                procInstId:$('#processInstanceId').val(),
                                taskId:$('#taskId').val(),
                                linkKey:$('#taskDefinitionKey').val(),
                                linkName:'',
                                mobileProcess:"N"
                            }
                        ).finally(function(result) {
                            // 调用推进方法，通过及回退均调用此方法，如参分别为（目标环节定义，目标处理人，流程实例ID， 任务ID， 用户意见，处理类型， 是否可撤回 ）
                            iframe.contentWindow.modal_pass(root, taskDefinitionKey, assignee, $('#processInstanceId').val(), $('#taskId').val(), comment,
                                $('#handleType').val(), withdraw, $, index, layer, $('#sendMsg').val(),flag);
                        });

                    }, function (roleIndex) {
                        layer.close(roleIndex);
                        layer.open({
                            type: 3
                        });
                        //流程移动化标识
                        flowMobileInfoInit(
                            {
                                procInstId:$('#processInstanceId').val(),
                                taskId:$('#taskId').val(),
                                linkKey:$('#taskDefinitionKey').val(),
                                linkName:'',
                                mobileProcess:"N"
                            }
                        ).finally(function(result) {
                            // 调用推进方法，通过及回退均调用此方法，如参分别为（目标环节定义，目标处理人，流程实例ID， 任务ID， 用户意见，处理类型， 是否可撤回 ）
                            iframe.contentWindow.modal_pass(root, taskDefinitionKey, assignee, $('#processInstanceId').val(), $('#taskId').val(), comment,
                                $('#handleType').val(), withdraw, $, index, layer, $('#sendMsg').val(),flag);
                        });

                    });
            } else {
                layer.open({
                    type: 3
                });
                //流程移动化标识
                flowMobileInfoInit(
                    {
                        procInstId:$('#processInstanceId').val(),
                        taskId:$('#taskId').val(),
                        linkKey:$('#taskDefinitionKey').val(),
                        linkName:'',
                        mobileProcess:"N"
                    }
                ).finally(function(result) {
                    // 调用推进方法，通过及回退均调用此方法，如参分别为（目标环节定义，目标处理人，流程实例ID， 任务ID， 用户意见，处理类型， 是否可撤回 ）
                    iframe.contentWindow.modal_pass(root, taskDefinitionKey, assignee, $('#processInstanceId').val(), $('#taskId').val(), comment,
                        $('#handleType').val(), withdraw, $, index, layer, $('#sendMsg').val(),flag);
                });

            }
        });
    }

    // 提交时，有后台业务操作，点击关闭时，刷新flag   20190710  qinsp
    function refreshFlag() {

        if(iframe.contentWindow.refreshFlagData) {
            iframe.contentWindow.refreshFlagData();
        }
    }

    //流程结束调用
    function endPushProcess($, index, layer){
        // 获取目标环节定义及是否可撤回标志
        var link = $("#link").val();
        // 需提交处理的参数
        var taskDefinitionKey = link.split(",")[0];
        var withdraw = 0;
        var assignee = '';
        var comment = "通过";/*$("#comment").val();*/

        // 增加各业务js判断,业务侧可能有一些js校验，通过此方式即可判断
        var businMakeSure = true;
        if(iframe.contentWindow.endPushValidate){
            //流程移动化标识
            flowMobileInfoInit(
                {
                    procInstId:$('#processInstanceId').val(),
                    taskId:$('#taskId').val(),
                    linkKey:$('#taskDefinitionKey').val(),
                    linkName:'',
                    mobileProcess:"N"
                }
            ).finally(function(result) {
                businMakeSure = iframe.contentWindow.endPushValidate($('#root').val(), taskDefinitionKey, assignee, $('#processInstanceId').val(), $('#taskId').val(), comment, $('#handleType').val(), withdraw, $, index, layer);
            });
           }else{
            layer.open({
                content: '是否确认结束流程？'
                ,icon: 3
                ,title: '提示'
                ,btn: ['确定', '取消']
                ,yes: function(indexs, layero){
                    layer.close(indexs);
                    layer.open({
                        type: 3
                    });
                    //流程移动化标识
                    flowMobileInfoInit(
                        {
                            procInstId:$('#processInstanceId').val(),
                            taskId:$('#taskId').val(),
                            linkKey:$('#taskDefinitionKey').val(),
                            mobileProcess:"N"
                        }
                    ).finally(function(result) {
                        // 调用推进方法，通过及回退均调用此方法，如参分别为（目标环节定义，目标处理人，流程实例ID， 任务ID， 用户意见，处理类型， 是否可撤回 ）
                        iframe.contentWindow.modal_pass($('#root').val(), taskDefinitionKey, assignee, $('#processInstanceId').val(), $('#taskId').val(), comment, $('#handleType').val(), withdraw, $, index, layer,'',flag);
                    });

                }
                ,btn2: function(index, layero){
                    refreshFlag();
                }
                ,cancel: function(){
                    refreshFlag();
                }
            });
        }

        // if(businMakeSure!=undefined && !businMakeSure){
        //     return;
        // }



    }

    /**
     * 会签环节专用流程推动
     * @param layer
     * @param $
     */
    function pushProcessForVote(layer, $) {
        var comment = $("#comment").val();
        if (comment.length == 0) {
            layer.msg('请填写审批意见！');
            return;
        }
        var regu = "^[ ]+$";
        var re = new RegExp(regu);
        if (re.test(comment) ) {
            layer.msg('审批意见不能填写空格！');
            return;
        }

        layer.confirm('是否确认提交？', {icon: 3, title: '提示'}, function (indexs) {
            layer.close(indexs);
            // 调用推进方法，会签环节同意及拒绝均调用此方法，如参分别为（目标环节定义，目标处理人，流程实例ID， 任务ID， 用户意见，处理类型， 是否可撤回 ），非必要参数传空串
            // var iframe=document.getElementById('businessIframe');
            layer.open({
                type: 3
            });
            //流程移动化标识
            flowMobileInfoInit(
                {
                    procInstId:$('#processInstanceId').val(),
                    taskId:$('#taskId').val(),
                    linkKey:$('#taskDefinitionKey').val(),
                    mobileProcess:"N"
                }
            ).finally(function(result) {
                // 调用中止方法
                iframe.contentWindow.modal_pass($('#root').val(), '', '', $('#processInstanceId').val(), $('#taskId').val(), comment, $('#handleType').val(), '', $, layer,flag);
            });
            });
    }

    /**
     * 流程中止，即仅首环节可以流程作废
     * @param layer
     * @param $
     * @param index
     */
    function stopProcess(layer, $, index) {
        var comment = $("#comment").val();
        if (comment.length == 0) {
            layer.msg('请填写审批意见！');
            return;
        }
        var regu = "^[ ]+$";
        var re = new RegExp(regu);
        if (re.test(comment) ) {
            layer.msg('审批意见不能填写空格！');
            return;
        }
        layer.confirm('【中止】确认后，当前流程将在本环节结束，是否确认？', {
            icon: 3,
            title: '提示'
        }, function (indexs) {
            layer.close(indexs);
            layer.open({
                type: 3
            });
            // 调用中止方法
            // 20180721 将中止方法写到公共中，如果各业务想重写，请覆写modal_stop这个方法
            // 业务实现中止
            if (iframe.contentWindow.modal_stop) {
                //流程移动化标识
                flowMobileInfoInit(
                    {
                        procInstId:$('#processInstanceId').val(),
                        taskId:$('#taskId').val(),
                        linkKey:$('#taskDefinitionKey').val(),
                        mobileProcess:"N"
                    }
                ).finally(function(result) {
                    iframe.contentWindow.modal_stop(root, $('#processInstanceId').val(), $('#taskId').val(), $("#comment").val(), $, index, layer);
                });

            } else {
                //流程移动化标识
                flowMobileInfoInit(
                    {
                        procInstId:processInstanceId,
                        taskId:taskId,
                        linkKey:$('#taskDefinitionKey').val(),
                        mobileProcess:"N"
                    }
                ).finally(function(result) {
                    modal_stop(root,processInstanceId, taskId, comment, $, index, layer);
                });

            }
        });
    }

    /**
     * 系统默认中止请求
     * @param root
     * @param processInstanceId
     * @param taskId
     * @param comment
     * @param $
     * @param index
     * @param layer
     */
    function modal_stop(root, processInstanceId, taskId, comment, $, index, layer) {

        //alert("流程实例ID：" + processInstanceId + "_当前任务ID：" + taskId + "_用户意见：" + comment);
        $.post(ctx + "/workflowrest/breakProcess", {
            "processInstanceId": processInstanceId,
            "taskId": taskId,
            "comment": comment
        }, function (data) {
            data = JSON.parse(data);
            //alert(data.sign + "（业务开发人员自定义提示消息有无及内容）");
            // 成功后回调模态窗口关闭方法
            layer.close(index);
            parent.toTaskToDoList($);
        });
    }

    /**
     * 流程简退-退回操作，即不存在业务数据变动的环节回退任务至首环节
     * @param layer
     * @param $
     */
    function quickBackProcess(layer, $) {

        // 抽取参数
        var root = $('#root').val();
        var processInstanceId = $('#processInstanceId').val();
        var taskId = $('#taskId').val();
        var comment = $("#comment").val();
        if (comment.length == 0) {
            layer.msg('请填写审批意见！');
            return;
        }
        var regu = "^[ ]+$";
        var re = new RegExp(regu);
        if (re.test(comment) ) {
            layer.msg('审批意见不能填写空格！');
            return;
        }
        layer.confirm('【简退】确认后，当前任务将回退给流程发起人，是否确认？', {icon: 3, title: '提示'}, function (indexs) {
            layer.close(indexs);
            layer.open({
                type: 3
            });
            //流程移动化标识
            flowMobileInfoInit(
                {
                    procInstId:processInstanceId,
                    taskId:taskId,
                    linkKey:$('#taskDefinitionKey').val(),
                    linkName:'',
                    mobileProcess:"N"
                }
            ).finally(function(result) {
                // 调用简退-回退方法
                $.post(ctx + "/workflowrest/pushprocesstostart/" + processInstanceId + "/" + taskId, {
                    "comment": comment
                }, function (data) {
                    data = JSON.parse(data);
                    if (data.retCode == 1) {
                        layer.alert("任务已回退至流程发起人！");
                        // 成功后回调模态窗口关闭方法
                        layer.closeAll();
                        toTaskToDoList($);
                    }
                });
            });

        });
    }

    /**
     * 流程简退-首环节推进操作，即不存在业务数据变动的首环节推进至之前回退
     * @param layer
     * @param $
     */
    function quickPushProcess(layer, $) {

        // 抽取参数
        var root = $('#root').val();
        var processInstanceId = $('#processInstanceId').val();
        var taskId = $('#taskId').val();
        var comment = $("#comment").val();
        if (comment.length == 0) {
            layer.msg('请填写审批意见！');
            return;
        }
        var regu = "^[ ]+$";
        var re = new RegExp(regu);
        if (re.test(comment) ) {
            layer.msg('审批意见不能填写空格！');
            return;
        }
        layer.confirm('确认后，当前任务将提交至流程简退退回人，是否确认？', {icon: 3, title: '提示'}, function (indexs) {
            layer.close(indexs);
            layer.open({
                type: 3
            });
            //流程移动化标识
            flowMobileInfoInit(
                {
                    procInstId:processInstanceId,
                    taskId:taskId,
                    linkKey:$('#taskDefinitionKey').val(),
                    linkName:'',
                    mobileProcess:"N"
                }
            ).finally(function(result) {
                // 调用简退-回退方法
                $.post(ctx + "/workflowrest/pushprocessreturnback/" + processInstanceId + "/" + taskId, {
                    "comment": comment
                }, function (data) {
                    data = JSON.parse(data);
                    if (data.retCode == 1) {
                        layer.alert("任务已提交至流程退回人！");
                        // 成功后回调模态窗口关闭方法
                        layer.closeAll();
                        toTaskToDoList($);
                    }
                });
            });

        });
    }

    /**
     * 任务转派
     * @param layer
     * @param $
     */
    function transferTask(layer, $) {

        // 抽取参数
        var root = $('#root').val();
        var processInstanceId = $('#processInstanceId').val();
        var taskId = $('#taskId').val();
        var assignee = $("#assignee").val();
        var comment = $("#comment").val();

        if (assignee == null || assignee == '') {
            layer.msg('请选择处理人！');
            return;
        }
        if (comment.length == 0) {
            layer.msg('请填写审批意见！');
            return;
        }
        var regu = "^[ ]+$";
        var re = new RegExp(regu);
        if (re.test(comment) ) {
            layer.msg('审批意见不能填写空格！');
            return;
        }
        var assigneeObj = $("#assignee")[0];
        var selectedIndex = assigneeObj.options.selectedIndex;
        var assigneeName = assigneeObj.options[selectedIndex].label;

        // 增加各业务js判断,业务侧可能有非空的一些js校验，通过此方式即可判断
        var businMakeSure = true;

        if(iframe.contentWindow.transferValidate){
            //流程移动化标识
            flowMobileInfoInit(
                {
                    procInstId:processInstanceId,
                    taskId:taskId,
                    linkKey:$('#taskDefinitionKey').val(),
                    linkName:'',
                    mobileProcess:"N"
                }
            ).finally(function(result) {
                    businMakeSure = iframe.contentWindow.transferValidate(ctx,$,layer,processInstanceId,taskId,assignee,assigneeName,comment);
            });
        }else{
            // 示例中只会转派给当前登录人，业务侧需要自行拓展
            layer.confirm("确认转派给【"+assigneeName+"】代为处理？", {icon: 3, title: '提示'}, function (indexs) {
                layer.close(indexs);
                var loading3 = layer.open({
                    type: 3
                });
                //流程移动化标识
                flowMobileInfoInit(
                    {
                        procInstId:processInstanceId,
                        taskId:taskId,
                        linkKey:$('#taskDefinitionKey').val(),
                        linkName:'',
                        mobileProcess:"N"
                    }
                ).finally(function() {
                    // 调用转派方法
                    $.post(ctx + "/workflowrest/transfertask/" + processInstanceId + "/" + taskId + "/" + assignee, {
                        "comment": comment
                    }, function (data) {
                        data = JSON.parse(data);
                        if (data.retCode == 1) {
                            // 成功后回调模态窗口关闭方法
                            layer.msg("转派完成！",{icon: 1});
                            setTimeout(function(idx){
                                toTaskToDoList($);
                            },800);
                        } else {
                            layer.close(loading3);
                            layer.confirm(data.msg, {icon: 2, title: "提示"}, function (index2) {
                                layer.close(index2);
                            });
                        }
                    });
                });

            });
        }

        // if(businMakeSure!=undefined && !businMakeSure){
        //     return;
        // }



    }

//清空下环节处理人已选择内容
    function clearAssignee() {
        $("#assignee").val('');
        $("#assigneeName").val('');
        $("#assigneeInput").val("");
        assigneeInput = "";

        //处理选择结束节点的信息
        if ("END" == $("#link").val().split(",")[2]) {
            hideLinkDiv();
        } else {
            showLinkDiv();
        }
    }

// 提供业务主页用户关闭模态窗口的按钮
    function modal_close() {
        $('#returnList').click();
    }

    function showLinkDiv(){
        $("#linkDiv").show();
        $("#assigneeDiv").show();
        $("#sendmsgDiv").show();
    }

    function hideLinkDiv() {
        $("#linkDiv").hide();
        $("#assigneeDiv").hide();
        $("#sendmsgDiv").hide();
    }

    /**
     * 根据选择的环节进行过滤该环节曾经办过的人员，按时间的顺序进行倒序排序
     * @param processDefinitionId 流程定义
     * @param taskDefinitionKey    本机环节的link_key
     * @param handleType    流程的走向 1 通过2 退回 3转派
     * @param selectTaskDefinitionKey 所选择的流程环节的link_key
     * @returns 经过滤查询所有的人员
     */
    function historyUser(processDefinitionId, taskDefinitionKey, handleType, selectTaskDefinitionKey) {
        var root = $('#root').val();
        $.post(ctx + "/workflowrest/getHistoryUser/" + processDefinitionId + "/" + taskDefinitionKey + "/" + handleType + "/" + selectTaskDefinitionKey, null, function (data) {
            var html;
            for (var i = 0; i < data.length; i++) {
                html += "<tr><td>"
                    + data[i].userName
                    + "</td><td>"
                    + "<button type='button' class='btn btn-info btn-xs'  onclick='setUser(\"" + data[i].userId + "\",\"" + data[i].userName + "\")'>选择</button>"
                    + "</td></tr>";
            }
            $("#historyUser").html(html);
        });
    }

    function setUser(userId, userName) {
        $("#assignee").val(userId);
        $("#assigneeName").val(userName);
    }

    function openLink(hi, handleType, layer, $) {
        var handlName='';
        if(handleType == "1"){
            handlName="提交"
            $('#hodeType').text('【同意】')
            $('#comment').css('text-indent','4.5em')
        }
        if(handleType == "2"){
            handlName="退回"
            $('#hodeType').text('【不同意】')
            $('#comment').css('text-indent','5.5em')
        }

        var idx = layer.open({
            type: 1
            , title: "流程"+handlName
            , id: 'showStart' //防止重复弹出
            , content: $("#showPage")
            , area: ['650px', '480px']
            , shade: 0.4
            , maxmin: false
            , resize: false
            , cancel: function (index, layero) {//取消事件
                $('#showPage').hide();
            }
        });


        // 绑定提交按钮
        // $('#taskSubmit').click(function () {
        //     pushProcess($, idx, layer);
        // });
        $('#taskSubmit').off('click').on('click', function () {
            pushProcess($, idx, layer);
        });
        // 绑定关闭按钮
        $('#taskClose').click(function () {
            refreshFlag();
            layer.close(idx);
            $("#showPage").hide();
        });
    }



    /**
     * 打开窗口
     * @param hi
     * @param type 1 会签，2 中止 3 简退 4 减退后第一环节提交 5 转派
     */
    function openLinkForNoLink(hi, layer, $, type) {
        $("#sendmsgDiv").show();
        if(type == '5'){
            $("#sendmsgDiv").hide();//转派暂时先屏蔽发短信
        }
        var handlName='';
        if(type == "1"){
            handlName="会签"
            $('#hodeType').text('【会签】')
        }
        if(type == "2"){
            handlName="中止"
            $('#hodeType').text('【中止】')
        }
        if(type == "3"){
            handlName="简退"
            $('#hodeType').text('【简退】')
        }
        if(type == "4"){
            handlName="简退后第一环节提交"
            $('#hodeType').text('【简退后第一环节提交】')
        }
        if(type == "5"){
            handlName="转派"
            $('#hodeType').text('【转派】')
        }
        var idx = layer.open({
            type: 1
            , title: "流程"+handlName
            , offset: "auto" //具体配置参考：http://www.layui.com/doc/modules/layer.html#offset
            // , id: 'showStart' //防止重复弹出
            , area: ['650px', '480px']
            , content: $("#showPage")
            , maxmin: false
            , resize: false
            , shade: 0.4 //不显示遮罩
            , cancel: function (index, layero) {//取消事件
                $('#showPage').hide();
            }

        });
        // 绑定提交按钮
        // $('#taskSubmit').click(function () {
        //     if (type == 1) {
        //         pushProcessForVote(layer, $);
        //     } else if (type == 2) {
        //         stopProcess(layer, $, idx);
        //     } else if (type == 3) {
        //         quickBackProcess(layer, $);
        //     } else if (type == 4) {
        //         quickPushProcess(layer, $);
        //     } else if (type == 5) {
        //         transferTask(layer, $);
        //     }
        // });
        $('#taskSubmit').off('click').on('click', function () {
            if (type == 1) {
                pushProcessForVote(layer, $);
            } else if (type == 2) {
                stopProcess(layer, $, idx);
            } else if (type == 3) {
                quickBackProcess(layer, $);
            } else if (type == 4) {
                quickPushProcess(layer, $);
            } else if (type == 5) {
                transferTask(layer, $);
            }
        });
        // 绑定关闭按钮
        $('#taskClose').click(function () {
            layer.close(idx);
            $("#showPage").hide();
        });
    }

    element.on('tab(custom_tab)', function(data) {
        var id = data.elem.context.id;
        if ("flowImage" == id) {
            // 流程图展示，展示的是个性化流程图
            flowchart(ctx, $, iframe);
        }
    });

    $("#assigneeInput").on("click", function () {
        layer.open({
            type: 2,
            id: 'userCheckedModal',
            title: '流程提交',
            content: 'views/modular/system/tree/foreseeRangeUserTree.html?isMultiple=false',
            area: ['600px', '480px'],
            shade: 0.4,
            resize: false
        });
    });

    window.checkedUserAssignment = function(userTreeModalIndex, userArray) {
        var userCodeArray = [],
            userNameArray = [];
        $.each(userArray, function (index, obj) {
            userCodeArray.push(obj.id);
            userNameArray.push(obj.name);
        });
        assigneeInput = userCodeArray.toString();
        $("#assigneeInput").val(userNameArray.toString());
        layer.close(userTreeModalIndex);
    }

});

//调用关闭待办方法
function toTaskToDoList($){
    if(flag != 'cloud'){
        var indexs=parent.layer.getFrameIndex(window.name);
        parent.layer.close(indexs);
    }else{
        window.top.close();
    }
}

/**
 * 渲染无下一环节人情况
 * @param $ jQuery对象
 * @param rootPath 项目根目录
 * @param refreshAssigneeUrl 请求后端路径前缀
 * @param processDefinitionKey 流程定义KEY
 * @param taskDefinitionKey 环节定义KEY
 * @param processData 提交数据对象
 * @param layer layui弹出层对象
 * @param linkName 所选环节名称
 * @param form layui的表单对象
 */
function renderNoNextLinkUser($, rootPath,refreshAssigneeUrl, processDefinitionKey, taskDefinitionKey, processData, layer, linkName, form) {
    var assigneeSwitch;
    assigneeInput = "";
    assigneeNeedRoleName = "";
    assigneeNeedRoleCode = "";
    assigneeNoRole = false;
    processData.nextTaskDefinitionKey = taskDefinitionKey;
    $("#assigneeInput").val("");
    $.ajax({
        url: rootPath + "/workflowrest/getFlowLinkAssigneeCheckSwitch",
        dataType: "json",
        type: "POST",
        contentType: "application/json;charset=UTF-8",
        async: false,
        data: JSON.stringify(processData),
        success: function(res) {
            assigneeSwitch = res;
        }
    });

    if (assigneeSwitch) {
        $("#assigneeBlockDiv").hide();
        $("#assigneeInputBlockDiv").show();
    }

    $.post(root + "/" + refreshAssigneeUrl + "/refreshNextAssigneeSelect/" + processDefinitionKey + "/" + taskDefinitionKey, processData,
        function (data) {
            if (data.needRoles) {
                var needRoles = data.needRoles;
                var roleArray = needRoles.split(",");
                var titleMessage = "推送至<span style='color: red;'>" + linkName + "</span>环节无法选择到处理人";
                var message = "下环节：<span style='color: red;'>" + linkName + "</span>，无拥有 <span style='color: red;'>" + needRoles + "</span> 角色的人员，" +
                    "请通过填写模板在<span style='color: red;'>智慧门户中-沃运营平台-新建工单（M域 账号权限管理）</span>中申请角色。";
                if (assigneeSwitch) {
                    var appendParagraph = "您也可以单击下方<span style='color: red;'>选择人员</span>自行选择下一环节人进行下环节业务处理。";
                    $("#appendParagraph").html(appendParagraph);
                    $("#noAssigneeCheckDiv").show();
                }
                if (assigneeSwitch && 1 === roleArray.length) {
                    assigneeNeedRoleName = needRoles;
                    assigneeNeedRoleCode = data.ruleCode;
                    assigneeNoRole = true;
                }
                $("#noRoleTitle").html(titleMessage);
                $("#noRoleMessage").html(message);
                var roleApplyIndex = layer.open({
                    type: 1,
                    title: '流程提交',
                    content: $("#roleApplyPanel"),
                    area: ['600px', '350px'],
                    shade: 0.4,
                    resize: false,
                    success: function (layero, index) {
                        $.ajax({
                            url: rootPath + "/prj/helpDoc/selectRoleTemplates",
                            type: 'POST',
                            dataType: 'JSON',
                            contentType: 'application/json;charset=UTF-8',
                            success: function (res) {

                                $("#helpDocList").empty();
                                var helpDocs = res.data;
                                $.each(helpDocs, function (docIndex, docObj) {
                                    var docHtml = "<li><a href='" + rootPath + "/files/downLoad/" + encrypt(docObj.attachmentId) + "' title='点击下载' " +
                                        "style='text-decoration: underline;'>" +
                                        docObj.docName + "</a></li>";
                                    $("#helpDocList").append(docHtml);
                                });
                            }
                        });
                    }
                });

                $("#noAssigneeCheckBtn").on("click", function () {
                    layer.close(roleApplyIndex);
                    layer.open({
                        type: 2,
                        id: 'userCheckedModal',
                        title: '流程提交',
                        content: 'views/modular/system/tree/foreseeRangeUserTree.html?isMultiple=false',
                        area: ['600px', '480px'],
                        shade: 0.4,
                        resize: false
                    });
                });
            } else {
                if (assigneeSwitch) {
                    $("#noRoleCheckAssigneeDiv").show();
                }
                var noRoleIndex = layer.open({
                    type: 1,
                    title: '流程提交',
                    content: $("#noRolePanel"),
                    area: ['500px', '350px'],
                    shade: 0.4,
                    resize: false
                });
                $("#noRoleCheckBtn").on("click", function () {
                    layer.close(noRoleIndex);
                    layer.open({
                        type: 2,
                        title: '流程提交',
                        content: 'views/modular/system/tree/foreseeRangeUserTree.html?isMultiple=false',
                        area: ['600px', '480px'],
                        shade: 0.4,
                        resize: false
                    });
                });
            }
        }, "json");
}

/**
 * 自动添加角色
 * @param $ jQuery对象
 * @param rootPath 项目根目录
 */
function automaticallyAddRole($, rootPath) {
    $.ajax({
        url: rootPath + "/system/user/addRoleOnly",
        dataType: "json",
        type: "POST",
        contentType: "application/json;charset=UTF-8",
        async: false,
        data: JSON.stringify({
            roleCode: assigneeNeedRoleCode,
            userPost: assigneeInput
        }),
        success: function(res) {}
    });
}
