.layui-common-body {
    padding: 4px;
    min-height: 100%;
    background-color: #eee;
    box-sizing: border-box;
}
.layui-table-view .layui-table {

}

.must-icon:before {
    content: "*";
    color: #C20000;
    font-size: 14px;
    margin-right: 5px;
}
.no-drop{
    cursor: no-drop;
}

.color{color:#000 !important;}

.text-red{
    color:#C20000 !important;
}
.border-box{
    box-sizing: border-box;
}
.cursor{
    cursor: pointer;
}
.width50{
    float: left;
    width: 50% !important;
}
.padding0{
    padding: 0 !important;
}
.pd50{
    padding-bottom: 50px;
}
.padding-left10{
    padding-left: 10px;
}
.padding-top-bottom10{
    padding:10px 0 10px  !important;
}
.border0{
    border-width: 0 !important;
}
.bottom0 {
    margin-bottom: 0 !important;
}
.position{
    position: relative;
}
/* 无数据 */
.new-nodata{
    text-align: center;
}
.new-nodata img{
    margin:60px 0 20px;
}
.new-nodata p{
    font-size: 14px;
    color: #c3cbd6;
}
    /*英文换行  */
.word-break-all{
    word-break: break-all;
}
/*文字不被选中*/
.noSelect {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.height100 {
    height: 100%;
}
/*便于标签识别换行情况*/
.white-space {
    white-space: pre-line;
}

.tex-right {
    text-align: right;
}

.flex {
    display: flex;
    align-items: center;
}

.flex-end {
    justify-content: flex-end;
}

.flex-between {
    justify-content: space-between;
}

.flex-center {
    justify-content: center;
}

.position-btn {
    width: 100%;
    position: fixed;
    bottom: 0px;
    padding:10px 20px;
    left: 0;
    text-align: right;
    box-sizing: border-box;
    background: #fff;
}
.body-btn {
    width: 100%;
    position: absolute;
    bottom: 0px;
    padding:10px 20px;
    left: 0;
    text-align: right;
    box-sizing: border-box;
    background: #fff;
}

.layui-form-item.layui-form-item-sm i.text-required {
    font-weight: 900;
    display: inline-block;
    margin-right: 4px;
}

.new-table .layui-table-view .layui-table td, .new-table .layui-table-view .layui-table th {
    height: 48px;
    line-height: 48px;
    border-left: 0;
    border-right: 0;
    font-size: 14px;
    font-weight: normal;
}

.new-table.border .layui-table-view .layui-table td, .new-table.border .layui-table-view .layui-table th {
    border-left: 1px solid #e5e5e5;
    border-right: 1px solid #e5e5e5;
}

.new-style .depart_li_div {
    width: 250px;
    padding-right: 10px;
    box-sizing: border-box;
    float: left;
}

.new-style .depart_li_width {
    width: auto;
    max-width: 100%;
    padding-right: 10px;
    box-sizing: border-box;
    float: left;
}

.new-style .depart_li_width .depart_li {
    height: auto;
    position: relative;
    padding-right: 30px;
}

.new-style .depart_li_width .depart_li .icon {
    float: right;
    cursor: pointer;
    position: absolute;
    right: 10px;
    top: 0;
}

.new-style .depart_li {
    height: 30px;
    line-height: 30px;
    margin: 0 0 12px 0;
    display: inline-block;
    padding: 0 6px 0 12px;
    border-radius: 2px;
    box-sizing: border-box;
    position: relative;
}

.new-style .depart_li_blue1 {
    background-color: #e6f7ff;
    color: #40a9ff;
}

.new-style .depart_li_orange {
    background-color: #f7eee7;
    color: #fd8f3c;
}

.new-style .depart_li_green {
    /*background-color: #e6f2e1;*/
    /*color: #52c41a;*/
    margin-right: 14px;
    height: auto;
}

.new-style .depart_li_blue2 {
    background-color: #40a9ff;
    color: #fff;
}

.new-style .depart_li_grey {
    background: #F4F4F4;
    border-radius: 4px;
    color:#000;
}

.background-blue{
    background-color: #8fa3ff;
}

.background-red{
    background-color: #fc9d9d;
}

.new-style .ment_list {
    position: relative;
    float: right;
    width: calc(100% - 290px);
}

.new-style .checkbox-box {
    margin-right: 15px;
    margin-bottom: 10px;
}

.new-style .layui-unselect.layui-form-checkbox {
    margin: 0;
    height: 32px !important;
    line-height: 32px !important;
    background-color: #ffffff;
    border-radius: 2px;
    border: solid 1px #d9d9d9 !important;
    padding: 0 26px;
}
.new-style .laytable-cell-checkbox .layui-unselect.layui-form-checkbox {
    margin: 0;
    height: auto!important;
    line-height: normal!important;
    border: none!important;
    padding: 0;
    background: none;
}

.new-style .layui-unselect.layui-form-checkbox.layui-form-checked {
    border-color: #40a9ff !important;
}

.new-style .layui-unselect.layui-form-checkbox.layui-form-checked span {
    color: #40a9ff;
}

.new-style .laytable-cell-checkbox .layui-unselect.layui-form-checkbox.layui-form-checked {
    border-color: #C20000 !important;
}

.new-style .laytable-cell-checkbox .layui-unselect.layui-form-checkbox.layui-form-checked span {
    color: #C20000;
}
.layui-form-checked.layui-checkbox-disbaled i {
    color: #fff !important;
}
.new-style #specDiv .layui-unselect.layui-form-checkbox {
    padding: 0;
    min-width: 170px;
    text-align: center;
}

.new-style #specDiv .layui-unselect.layui-form-checkbox span {
    float: none;
    line-height: 30px;
}
.new-style #specDiv .layui-checkbox-disbaled[lay-skin="primary"] span{
    color:#000;
}

.new-style.new-radio .layui-unselect.layui-form-radio {
    margin: 0;
    height: 32px !important;
    line-height: 32px !important;
    background-color: #ffffff;
    border-radius: 2px;
    border: solid 1px #d9d9d9 !important;
    padding: 0 10px;
}

.new-style.new-radio .layui-unselect.layui-form-radio.layui-form-radioed {
    border-color: #C20000 !important;
}

.new-style.new-radio .layui-unselect.layui-form-radio.layui-form-radioed div {
    color: #C20000;
}

.new-style.new-radio .layui-form-radio .layui-icon {
    display: none;
}

.new-style .layui-form-checkbox[lay-skin="primary"] span {
    padding: 0;
    line-height: 32px;
}

.new-style .layui-form-checkbox .layui-icon {
    display: none;
}
.new-style .laytable-cell-checkbox .layui-form-checkbox .layui-icon {
    display: inline-block;
}

.depart_li_div_before {
    position: relative;
}

.depart_li_div_before:before {
    position: absolute;
    content: '';
    left: -25px;
    top: 15px;
    width: 25px;
    height: 100%;
    border-left: 1px solid #d9d9d9;
}

.depart_li_1:before {
    position: absolute;
    content: '';
    right: -30px;
    top: 15px;
    width: 25px;
    height: 1px;
    background: #d9d9d9;
}

.depart_li_div .depart_li_2:before {
    position: absolute;
    content: '';
    left: -25px;
    top: 15px;
    width: 22px;
    height: 45px;
    border-top: 1px solid #d9d9c9;
}

.depart_li_div_before:last-child:before {
    position: absolute;
    content: '';
    left: -25px;
    top: 23px;
    width: 25px;
    height: 0;
    border-left: 1px solid #d9d9d9;
}

.depart_li_2:after {
    position: absolute;
    content: '';
    right: -48px;
    top: 15px;
    width: 40px;
    height: 1px;
    background: #d9d9d9;
}

.depart_li .layui-input {
    display: inline-block;
    width: 70px;
}

.width200 {
    width: 200px
}

.width200-right {
    width: calc(100% - 200px)
}

.new-style .depart_li .icon {
    float: right;
    cursor: pointer;
}

.new-style .depart_li .depart_li_text {
    width: 90%;
}

.new-table .layui-table-main tr td {
    color: #373d41;
}

.new-table .layui-table-page > div {
    text-align: left;
}

.new-table .layui-table-fixed-r {
    border-width: 0;
    box-shadow: #e6e6e6 -10px 0 14px;
}

.new-table .layui-table-fixed-l {
    box-shadow: #e6e6e6 2px 0 14px;
}

.new-table .layui-table-cell {
    padding: 0 10px;
}

.new-table .layui-border-box.layui-table-view {
    border-radius: 0;
    border: 0;
    border-bottom: 1px solid #ddd;
}

.new-table .layui-table thead tr th {
    color: #73777a;
}

.new-table .layui-table tbody tr:hover, .new-table .layui-table-hover, .new-table .layui-table-click {
    background: #f0f2f5;
}

.new-table .layui-table thead tr, .new-table .layui-table-click {
    background-color: #f0f2f5;
}

.new-table .layui-table[lay-even] tr:nth-child(even) {
    background: #f0f2f5;
}

.new-style {
    font-size: 14px;
}

.new-style .layui-form-checkbox[lay-skin="primary"]:hover i {
    border-color: #C20000;
}

.new-style .layui-form-checked[lay-skin="primary"] i {
    border-color: #C20000;
    background-color: #C20000;
    color: #fff;
}

.new-style .layui-btn {
    border: 1px #C20000 solid;
    border-radius: 4px;
    background: none;
    color: #C20000;
}

.new-style .layui-btn:hover {
    background: #C20000;
    color: #fff;
}

.form-conmon-more .new-style .layui-form-item {
    margin-bottom: 10px;
}

.new-style .layui-form-item.layui-form-item-sm .layui-form-label {
    height: 32px;
    line-height: 32px;
    font-weight: inherit;
    color: #595959;
    word-break: break-all;;
}

.search-box {
    padding: 20px 0 10px;
}

.new-style.special-input .layui-select-title .layui-input {
    max-width: 142px;
    margin-top: 10px;
}

.new-style .layui-form-radio {
    margin-top: 4px;
}

.new-style .layui-form-label {
    width: 120px;
}

.new-style .layui-form-item-sm .layui-form-label-br {
    width: 120px;
    float: left;
    line-height: 16px;
    overflow: inherit;
    white-space: initial;
    text-align: left;
    color: #a9b0b4;
    padding: 0 12px 0 15px;
    font-weight: inherit;
    border-radius: 4px 0px 0px 4px;
    box-sizing: border-box;
}

.new-style .layui-input-block {
    margin-left: 120px;
}

.new-style .layui-form-item.layui-form-item-sm .layui-input {
    height: 32px;
    font-size: 14px;
    background: #fff;
    border: 1px solid #cecece;
    border-radius: 4px;
}

.new-style .layui-table .layui-input {
    height: 100% !important;
    margin-top: 0;
}

.new-style .layui-input {
    height: 32px;
    font-size: 14px;
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 2px;
    text-align: left;
}

/*新的下拉列表 new-select*/
.color9 {
    color: #999;
}

.new-select .layui-form-selected dl {
    display: flex;
    flex-wrap: wrap;
    left: -88px;
    max-height: 200px;
    top: 30px;
    bottom: auto;
    min-width: 230px;
}

.tagmodel-popup {
    text-align: left;
}

.area-ul-p {
    height: 30px;
    line-height: 30px;
}

.checkbox-li {
    padding: 0 10px;
    box-sizing: border-box;
}

.new-select .layui-form-select dl {

}

.new-select .layui-form-select dl dd {
    display: inline-block;
    font-size: 14px;
    color: #000;
    min-width: 78px;
    height: 24px;
    line-height: 27px;
    background: #f5f5f5;
    border: 1px solid #eeeeee;
    text-align: center;
    margin: 5px;
    border-radius: 4px;
    cursor: pointer;
}

.new-select .layui-form-select dl dd.layui-this {
    background: rgb(230, 247, 255);
    border: 1px solid rgb(24, 144, 255);
    color: rgb(24, 144, 255);
}

.noselect {

    -webkit-touch-callout: none; /* iOS Safari */

    -webkit-user-select: none; /* Chrome/Safari/Opera */

    -khtml-user-select: none; /* Konqueror */

    -moz-user-select: none; /* Firefox */

    -ms-user-select: none; /* Internet Explorer/Edge */

    user-select: none;
    /* Non-prefixed version, currently

not supported by any browser */

}

#ihEchart1, #ihEchart2 {
    width: 100%;
    height: 245px;
    z-index: 9999;
}

.area-selector.custom_select, .date-selector.custom_select {
    width: 140px;
    height: 28px;
    background-color: #f5f5f5;
    border-radius: 4px;
    border: solid 1px #eeeeee;
    color: #000;
    margin-left: 10px;
    font-size: 14px;
}

.rightone {
    padding: 14px 0;
}

.rightone span {
    display: inline-block;
    width: 70px;
}

.rightone .trtTitle {
    font-size: 14px;
    color: #ffffff;
}

.rightone .trm {
    margin: 15px 0;
    color: #ffffff;
    text-align: center;
    display: flex;
    justify-content: space-evenly;
}

.rightone .layui-form-radio {
    margin: 0;
}

.rightone .layui-form-radioed > i, .rightone .layui-form-radio > i:hover {
    color: #ffffff;
}

.rightone .trb {
    color: #ffffff;
}

.rightone .trb i {
    opacity: 0.8;
}

.rightone .trmTitle,
.rightone .trbTitle {
    /* font-family: MicrosoftYaHei; */
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 0px;
    color: #ffffff;
    opacity: 0.5;
}

.area-selector.custom_select > .tagmodel-popup {
    right: -156px;
}

.layui-laydate .laydate-btns-clear {
    display: none;
}

.audit-list-left ul li a, .audit-list-left ul li a {
    padding: 7px 15px;
    border-radius: 4px;
}

.audit-left-title {
    background: #eee9e9;
}

.audit-list-left ul li, .audit-list-left ul li {
    margin: 12px 0 8px 12px;
    display: inline-block;
    cursor: pointer;
}

.audit-list-left ul li a, .audit-list-right ul li a {
    color: #333;
    background: #fff;
    border: 1px #e4e4e4 solid;

}

.audit-list-left ul li a.active, .audit-list-left ul li a.active {
    background: #C20000;
    border: 1px #C20000 solid;
    color: #fff
}

.width100 {
    width: 100%;
}

.width40 {
    width: 40%;
    display: inline-block;
}

.width60 {
    width: 60%;
    display: inline-block;
}

.padding20 {
    padding: 20px;
    box-sizing: border-box;
}

.padding10-20 {
    padding: 10px 20px;
    box-sizing: border-box;
}

.padding20Left {
    padding: 0 20px;
    box-sizing: border-box;
}

.paddingLeft20Top10 {
    padding: 10px 20px;
    box-sizing: border-box;
}

.layui-common-box {
    padding: 4px;
    box-sizing: border-box;
}

.layui-common-big {
    padding: 8px;
    box-sizing: border-box;
}

.layui-common-card {
    background-color: #fff;
    border-radius: 4px;
    /*box-shadow: 0px 0px 10px 0px rgba(155, 11, 9, 0.1);*/
}

.layui-common-header {
    padding: 0 20px;
    box-sizing: border-box;
}

.layui-form-header {
    padding: 0;
}

.layui-search-new.new-style .layui-form-label {
    padding: 0 6px 0 0;
}

.no-border {
    border: 0 !important;
}

.layui-common-card-header, .layui-common-card-headers {
    height: 45px;
    line-height: 45px;
    padding: 0 0 0 18px;
    font-size: 16px;
    position: relative;
    border-bottom: 1px solid #EEEEEE;
    text-align: center;
}

.layui-form-card-header {
    height: 45px;
    line-height: 45px;
    padding-left: 18px;
    font-size: 16px;
    position: relative;
    text-align: center;
}

.layui-form-card-header:before {
    position: absolute;
    content: '';
    height: 15px;
    left: 0;
    top: 15px;
    width: 5px;
    border-radius: 4px;
    background: #C20000;
}

.layui-common-card-headers {
    padding: 0;
}

.layui-card-header-center {
    display: inline-block;
    width: 60%;
}

.layui-common-card-header-center {
    padding: 0 50px 0 18px;
}

.layui-common-card-header:before {
    position: absolute;
    content: '';
    height: 15px;
    left: 0;
    top: 15px;
    width: 5px;
    border-radius: 4px;
    background: #C20000;
}

.layui-card-header-title {
    font-size: 16px;
    float: left;
    font-weight: bold;
    display: flex;
    align-items: center;
}

.layui-card-header-select {
    min-width: 60px;
    font-size: 16px;
    display: inline-block;
    float: left;
    font-weight: bold;
    position: relative;
    color: #888888;
    margin-right: 40px;
    cursor: pointer;
}

.layui-card-header-select.active {
    color: #000000;
}

.layui-card-header-select.active:before {
    position: absolute;
    content: '';
    bottom: 0;
    width: 60%;
    height: 6px;
    left: 20%;
    border-radius: 2px;
    background: #C20000;
}

.open-button {
    width: 64px;
    height: 20px;
    background: #888;
    position: absolute;
    right: 5px;
    z-index: 999999;
    color: #fff;
    font-size: 14px;
    border-top-left-radius: 20px;
    border-bottom-left-radius: 20px;
    top: 18px;
    padding-left: 4px;
    box-sizing: border-box;
    cursor: pointer;
}

.open-button2 {
    left: 0;
    top: 50%;
    border-top-right-radius: 20px;
    border-bottom-right-radius: 20px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.achieveBoxG .center-btn {
    display: inline-block;
    width: 100%;
    text-align: center;
    padding: 22px 0 8px;
}

.achieveBoxG .center-btn img {
    width: 60px;
    cursor: pointer;
}

.achieveBoxG span {
    display: inline-block;
    width: 100%;
    text-align: center;
    cursor: pointer;
    font-size: 14px;
}

.right-btn-box {
    float: right;
    height: 100%;
    line-height: 45px;
}

.right-select-li {
    margin: 0;
    display: inline-block;
    font-size: 12px;
}

.right-select-li .layui-form-select input {
    background: #f5f5f5;
}

.right-btn-box .right-btn-icon {
    margin-left: 10px;
    cursor: pointer;
}

.right-btn-box .right-btn-icon .iconfont {
    color: #888888;
}

.right-btn-box .right-btn-icon.active .iconfont {
    color: #C20000;
}

/*预警任务处理区*/
.early-warning-data {
    margin-bottom: 10px;
}

.early-warning-data-li {
    width: 100%;
    margin: 4px 0;
}

.ewd-li {
    float: left;
    padding: 4px;
    box-sizing: border-box;
    height: 90px;
}

.ewd-li-box {
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    border: solid 1px #eeeeee;
    height: 100%;
    padding: 8px;
    box-sizing: border-box;
}

.ewd-li-1 {
    width: 83px;
}

.ewd-li-1 .ewd-li-1-circular {
    width: 68px;
    height: 68px;
    display: inline-block;
    line-height: 68px;
    border-radius: 50%;
    text-align: center;
    font-size: 18px;
    color: #ffffff;
    margin-top: 8px;
}

.circular-1 {
    background-image: linear-gradient(-30deg,
    #c20000 0%,
    #ffa39d 100%);
}

.circular-2 {
    background-image: linear-gradient(-30deg,
    #40a9ff 0%,
    #91d5ff 100%);
}

.ewd-li-right {
    float: right;
    width: calc(100% - 83px);
}

.ewd-li-2 {
    width: 30%;
}

.ewd-li-3 {
    width: 50%;
}

.ewd-li-4 {
    width: 20%;
}

.ewd-li-5 {
    width: 100%;
    padding: 0;
}
.ewd-li-6 {
    padding: 0;
}

.ewd-li-5 .ewd-li-box,.ewd-li-6 .ewd-li-box{
    padding: 10px 20px;
}

.ewd-li-5-left {
    height: 100%;
    border-right: 1px solid #d9d9d9;
}

.ewd-li-5-right {
    height: 100%;
    padding-left: 20px;
    box-sizing: border-box;
}

.ewd-li-5-top {
    width: 100%;
    font-size: 14px;
    line-height: 24px;
    color: #73777a;
    margin-bottom: 10px;
}

.ewd-li-5-num {
    font-size: 20px;
    line-height: 24px;
    color: #181818;
}
.ewd-li-6-num {
    font-size: 18px;
    line-height: 24px;
    color: #333333;
}
.ewd-li-6-title{
    width: 100%;
    font-size: 14px;
    line-height: 24px;
    color: #898D8F;
    margin-bottom: 10px;
}

.ewd-li-5-box-img {
    margin-right: 10px;
}

.ewd-li-5-box-b {
    padding-left: 10px;
    font-size: 18px;
}

.ewd-li-top {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    height: 40px;
}

.ewd-li-5-box {
    line-height: 24px;
}

.ewd-li-5-percentage {
    float: left;
    font-size: 14px;
}

.ewd-li-5-per {
    float: left;
    width: 26%;
    margin: 3px 10px 0 10px;
    height: 18px;
    background-color: #f5f5f5;
    border-radius: 2px;
}

.ewd-li-5-span {
    height: 18px;
    display: inline-block;
    border-radius: 2px;
}

.ewd-li-5-span-1 {
    background-color: #c20000;
}

.ewd-li-5-span-2 {
    background-color: #ffa940;
}

.ewd-li-5-span-3 {
    background-color: #ff8787;
}

.ewd-li-5-span-4 {
    background-color: #8da2fe;
}

.ewd-li-top .ewd-li-top-left .iconfont {
    font-size: 20px;
}

.ewd-li-top-left {
    display: flex;
    align-items: center;
}


textarea::-webkit-input-placeholder {
    /* WebKit browsers */
    color: #9e9e9e !important;
}
textarea:-moz-placeholder {
    /* Mozilla Firefox 4 to 18 */
    color: #9e9e9e !important;
}
textarea::-moz-placeholder {
    /* Mozilla Firefox 19+ */
    color: #9e9e9e !important;
}
textarea::-ms-input-placeholder {
    /* Internet Explorer 10+ */
    color: #9e9e9e !important;
}

.nodara {
    width: 100%;
    height: 100%;
    min-height: 130px;
    color: #b5b5b5;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ewd-li-top .ewd-li-top-left .icon-processed {
    font-size: 16px;
}

.ewd-li-top .ewd-li-top-left span {
    font-size: 14px;
    color: #000;
    margin-left: 4px;
    line-height: 14px;
}

.ewm-conter-li-box {
    display: flex;
    border-radius: 2px;
    overflow: hidden;
}

.ewd-li-box .layui-form-radio, .early-warning-model .layui-form-radio {
    margin: 0;
}

.layui-form-radioed > i, .layui-form-radio > i:hover {
    color: #C20000
}

.right-btn-box .layui-form-radio {
    margin-top: 0;
    padding: 0;
}

.ewd-li-top-right span {
    font-size: 22px;
    font-weight: bold;
    color: #333333;
}

.ewd-li-bottom {
    width: 100%;
    display: flex;
    height: 28px;
    justify-content: space-between;
    align-items: center;
}

.ewd-li-bottom .ewd-li-bottom-li-label {
    font-size: 12px;
    color: #888888;
}

.ewd-li-bottom .ewd-li-bottom-li-num {
    font-size: 14px;
    color: #f5212d;
}

.ewd-li-bottom .ewd-li-bottom-li-model {
    font-size: 12px;
    background-color: #eeeeee;
    border-radius: 4px;
    border: solid 1px #cccccc;
    color: #888888;
    padding: 2px 12px;
}

.ewd-li-bottom .ewd-li-bottom-li-value {
    font-size: 14px;
    color: #03ac2b;
}

.layui-quarantine {
    width: 100%;
    height: 13px;
    background-color: #eeeeee;
}

.early-warning-model-box {
    width: 100%;
    height: 466px;
    position: relative;
}

.early-warning-model-list {
    width: 100%;
    height: 466px;
    position: relative;
}

.early-warning-model-box:before {
    position: absolute;
    content: '\7cbe\51c6\5ea6';
    height: calc(100% - 20px);
    left: 50%;
    top: 10px;
    width: 1px;
    background: #cccccc;
    display: flex;
    justify-content: center;
    padding-top: 30px;
    box-sizing: border-box;
    color: #888888;
}

.early-warning-model-box:after {
    position: absolute;
    content: "\91cd\8981\6027";
    width: calc(100% - 20px);
    top: 50%;
    left: 10px;
    height: 1px;
    background: #cccccc;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-right: 30px;
    box-sizing: border-box;
    color: #888888;
}

.ewm-box-ul {
}

.ewm-box-ul-li {
    height: 233px;
    box-sizing: border-box;
    position: relative;
}

.ewm-box-ul-li1 {
    padding: 20px 0 20px 20px;
}

.ewm-box-ul-li2 {
    padding: 20px 10px 20px 0;
    position: relative;
}

.ewm-box-ul-li3 {
    padding: 35px 0 20px 20px;
}

.ewm-box-ul-li4 {
    padding: 35px 10px 20px 0;
}

.ewm-box-ul-li1:after {
    content: "";
    position: absolute;
    left: -7px;
    top: 5px;
    width: 0;
    height: 0;
    border-width: 0 8px 8px;
    border-style: solid;
    border-color: transparent transparent #888888;
}

.ewm-box-ul-li2:after {
    content: "";
    width: 8px;
    height: 8px;
    background: #fff;
    display: inline-block;
    position: absolute;
    left: 0;
    bottom: -5px;
    border-radius: 50%;
    border: 1px solid #888888;
}

.ewm-box-ul-li3:after {
    content: "";
    position: absolute;
    right: -6px;
    top: -7px;
    width: 0;
    height: 0;
    border-width: 8px 8px 8px;
    border-style: solid;
    border-color: transparent transparent transparent #888888;
}

.ewm-box-ul-li4:after {
    content: "";
    width: 8px;
    height: 8px;
    background: #fff;
    display: inline-block;
    position: absolute;
    right: -5px;
    bottom: 0;
    border-radius: 50%;
    border: 1px solid #888888;
}

.ewm-conter-list {
    height: 180px;
    overflow: auto;
    padding-right:10px;
}

.ewm-conter-li {
    width: 100%;
    height: 20px;
    margin: 10px 0;
    cursor: pointer;
}

.ewm-conter-li.active .ewm-conter-li-title {
    color: #C20000;
}

.ewm-conter-li-title {
    padding: 0;
    float: left;
    line-height: 20px;
    width: 32%;
    font-size: 13px;
}

.ewm-conter-li-right {
    float: right;
    width: 68%;
}

.ewm-conter-li-data {
    width: 46px;
    float: left;
    line-height: 20px;
    padding-left: 2px;
    box-sizing: border-box;
}

.ewm-conter-li-data .iconfont {
    font-size: 14px;
    vertical-align: middle;
}

.ewm-conter-li-num {
    font-size: 14px;
    color: #333333;
    vertical-align: top;
}

.ewm-conter-li-speed {
    float: right;
    width: calc(100% - 46px);
}

.ewm-conter-li-value {
    box-sizing: border-box;
    float: left;
    height: 20px;
    color: #ffffff;
    font-size: 14px;
    line-height: 20px;
}

/*******************  */


.icon-lan {
    color: #70aaf2;
}
.icon-lv {
    color: #09b532;
}
.ewm-conter-li-1-value2 {
    text-align: center;
    background-color: #85b6f3;
    border-radius: 2px 0px 0px 2px;
}

.ewm-conter-li-2-value2 {
    text-align: center;
    background-color: #7edb94;
    border-radius: 0px 2px 2px 0px;
}
.ewm-conter-li-value1 {
    text-align: center;
    background-image: linear-gradient(90deg,
    rgba(3, 172, 43, 0.8) 0%,
    rgba(3, 172, 43, 0.3) 100%);
    border-radius: 2px 0px 0px 2px;
}

.ewm-conter-li-value2 {
    text-align: center;
    background-image: linear-gradient(90deg,
    rgba(250, 139, 22, 0.8) 0%,
    rgba(250, 139, 22, 0.3) 100%);
    border-radius: 0px 2px 2px 0px;
}

.drill-model-list {
}

.drill-model-li {
    display: flex;
    align-items: center;
    height: 38px;
}

.drill-model-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
}

.drill-model-span1 {
    color: #73777a;
}

.drill-model-span2 {
    color: #181818;
    margin-right: 8px;
}

.ewm-conter-li-values1 {
    text-align: left;
    background: #ffb180;
    padding-left: 6px;
}

.ewm-conter-li-values2 {
    padding-left: 8px;
    text-align: left;
    background: #5ad8a6;
}

/*******************  */
.ewm-conter-fixed {
    position: absolute;
}

.ewm-conter-fixed-title {
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    background-color: #555555;
    border: solid 2px #cccccc;
    display: inline-block;
    border-radius: 50%;
    font-size: 14px;
    color: #ffffff;
    position: relative;
    z-index: 999;
}

.ewm-conter-fixed:hover .ewm-conter-fixed-data {
    display: inline-block;
}

.ewm-conter-fixed-data {
    display: none;
    height: 20px;
    background-color: #888888;
    border-radius: 10px 9px 9px 10px;
    border: solid 1px #cccccc;
    width: 110px;
    text-align: center;
    position: absolute;
    right: 15px;
    top: 4px;
    z-index: 0;
    line-height: 20px;
}

.ewm-conter-fixed1 {
    left: 10px;
    bottom: 10px;
}

.ewm-conter-fixed1 .ewm-conter-fixed-data {
    left: 15px;
    top: 4px;
}

.ewm-conter-fixed2 {
    right: 10px;
    bottom: 10px;
}

.ewm-conter-fixed2 .ewm-conter-fixed-data {
    right: 15px;
    top: 4px;
}

.ewm-conter-fixed3 {
    left: 10px;
    top: 10px;
}

.ewm-conter-fixed3 .ewm-conter-fixed-data {
    left: 15px;
    top: 4px;
}

.ewm-conter-fixed4 {
    right: 10px;
    top: 10px;
}

.ewm-conter-fixed4 .ewm-conter-fixed-data {
    right: 15px;
    top: 4px;
}

.ewm-conter-fixed-data .iconfont {
    color: #fff;
    font-size: 10px;
    margin-left: 5px;
}

.right-btn-box .layui-form-switch {
    margin-top: -2px;
}

.ewm-conter-fixed-data span {
    color: #fff;
}

.layui-roll-box {
    width: 100%;
    padding: 0 20px;
    box-sizing: border-box;
}

.layui-roll-list {

}

.layui-roll-li {
    height: 45px;
    line-height: 45px;
    border-bottom: dashed 1px #cccccc;
}

.remind-title {
    float: left;
    padding: 0;
    width: calc(100% - 80px);
}

.remind-title .iconfont.icon-level1 {
    font-size: 18px;
}

.remind-title span {
    font-size: 14px;
    color: #555555;
}

.remind-number {
    float: left;
    width: 100px;
    font-size: 14px;
    color: #888888;
}

.remind-time {
    text-align: right;
    float: left;
    width: 80px;
    font-size: 14px;
    color: #888888;
}

.ewm-data-exhibition {
    box-sizing: border-box;
    display: inline-flex;
    flex-wrap: wrap;
}

#weCard2 .ewm-data-exhibition {
    min-width: 100%;
}

.table-body-tr {
    margin: 10px 0;
    height: 32px;
}

.ewm-data-box {
    overflow: auto;
    box-sizing: border-box;
    height: 440px;
}

/* 修改-------- */
#tableHeader, .data-table-header {
    display: flex;
    align-items: center;
}

.data-table-header {
    min-height: 52px;
    /* line-height: 52px; */
    width: 100%;
}

.data-table-th {
    float: left;
    /* min-height: 52px; */
    text-align: center;
    font-size: 14px;
    color: #999999;
    padding: 0;

    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
}

.table-box-data {
    float: right;
    width: 95%;
}

.ewm-data-flex .table-box-data {
    width: 100%;
}

#weCard2 .ewm-data-box .table-box-benchmarking {
    min-width: 100%;
}

.ewm-data-flex .table-box-benchmarking {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
    border-left-width: 0;
}

.ewm-data-box .table-box-benchmarking {
    border-right-width: 0;
}

.data-table-th1 {
    width: 5%;
    float: left;
}

.data-table-benchmarking {
    width: 100%;
    height: 30px;
}

.benchmarking-title {
    font-size: 14px;
    color: #03ac2b;
    line-height: 30px;
    text-align: center;
}

.table-box-benchmarking {
    height: 30px;
    line-height: 30px;
    color: #03ac2b;
    background-color: #e5f7e9;
    /* border-radius: 16px; */
    border-radius: 4px;
    border: solid 1px #03ac2b;
    box-sizing: border-box;
}

.data-table-li {
    float: left;
    text-align: center;
}

.inline-flex {
    display: inline-flex;
}

.data-table-tbody {
    width: 100%;
}

.data-table-tbody-td {
    height: 33px;
    line-height: 33px;
    text-align: center;
    color: #C20000;
    position: relative;
}

.data-table-tbody-td:before {
    position: absolute;
    content: '';
    height: 1px;
    right: 0;
    top: 15px;
    width: 50%;
    border-bottom: 1px dashed #fff0f0;
}

.ewm-data-flex .table-box-tr {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
    border-left-width: 0;
}

.table-box-tr {
    background-color: #f5f5f5;
    /* border-radius: 16px; */
    border-radius: 4px;
    border: solid 1px #eeeeee;
    box-sizing: border-box;
}

.data-table-td {
    font-size: 14px;
    color: #333333;
    line-height: 30px;
    text-align: center;
    float: left;
    min-height: 30px;
}

.data-table-th2 {
    width: 11%;
}

.data-table-th3 {
    width: 11%;
}

.data-table-th4 {
    width: 10%;
}

.data-table-th5 {
    width: 11%;
}

.data-table-th6 {
    width: 14%;
}

.data-table-th7 {
    width: 14%;
}

.data-table-th7 img {
    width: 18px;
}

.data-table-th9 {
    text-align: left;
    display: flex;
    align-items: center;
    padding-left: 66px;
    box-sizing: border-box;
}

.data-table-th9 .icon-orange {
    font-size: 20px;
}

.data-table-th10 {
    width: 150px;
    text-align: left
}

.data-table-2 .data-table-th9 {
    width: 8%;
    text-align: left
}

.data-table-2 .data-table-th10 {
    width: 150px;
    text-align: right
}

.data-table-th9 span {
    margin-left: 4px;
    display: inline-block;
    min-width: 55px;
    height: 20px;
    line-height: 20px;
    background-color: #dddddd;
    border-radius: 4px;
    border: solid 1px #cccccc;
    font-size: 12px;
    color: #888888;
    text-align: center;
}

.icon-processed, .icon-green {
    color: #03ac2b;
}

.icon-grey {
    color: #a9b0b4;
}

.icon-blue {
    color: #1890ff;
}

.icon-red {
    color: #C20000 !important;
}

.icon-newred {
    color: #C20000;
}

.icon-orange {
    color: #fa8b16;
}

.icon-level {
    color: #1890ff;
}

.icon-level1 {
    /*background: linear-gradient(90deg,#f5212d 0%,#ff7774 100%);*/
    /*-webkit-background-clip: text;*/
    /*color: transparent;*/
    color: #f5212d;
}

.icon-level2 {
    /*background: linear-gradient(90deg,#ff8400 0%,#ffa940 100%);*/
    /*-webkit-background-clip: text;*/
    /*color: transparent;*/
    color: #ff8400;
}

.icon-level3 {
    /*background: linear-gradient(90deg,#ffd800 0%,#ffe971 100%);*/
    /*-webkit-background-clip: text;*/
    /*color: transparent;*/
    color: #ffd800;
}

.bg-level {
    background-color: #1890ff;
}

.bg-level1 {
    background-color: #f5212d;
}

.bg-level2 {
    background-color: #ff8400;
}

.bg-level3 {
    background-color: #ffd800;
}

.bg-level4 {
    background-color: #d5d5d5;
}

.early-warning-model-list {
}

.ewm-table {
    width: 100%;
    border-radius: 4px;
    border: solid 1px #eeeeee;
    overflow: hidden;
    margin-top: 15px;
}

.ewm-table-thead {
    width: 100%;
    background-color: #eeeeee;
}

.ewm-table-thead .ewm-table-th {
    height: 34px;
    line-height: 34px;
    font-size: 12px;
    color: #888888;
}

.ewm-table-th {
    text-align: center;
    height: 34px;
    float: left;
}

.ewm-table-tbody {
    height: 410px;
    overflow: auto;
}

.ewm-table-tr {
    height: 48px;
    line-height: 48px;
    background-color: #ffffff;
    cursor: pointer;
}

.ewm-table-tr.active .ewm-table-td {
    color: #C20000;
}

.ewm-table-tr:nth-child(even) {
    background-color: #f5f5f5;
}

.ewm-table-tr:nth-child(odd) {
    background-color: #fff;
}

.ewm-table-td {
    text-align: center;
    height: 48px;
    float: left;
    font-size: 14px;
    color: #333333;
}

.layui-inline .layui-input {
    background: #f5f5f5;
    height: 25px;
    width: 150px;
    color: #999999;
    font-size: 14px;
    padding: 0;
}

.layui-inline {
    border: 1px solid #ddd;
    border-radius: 4px;
}

.layui-table-box .layui-inline {
    border: none;
}

.layui-common-card-content {
    position: relative;
}

.layui-common-card-content .layui-form-radio {
    margin-top: 0;
}

.echartsLabel {
    position: absolute;
    width: 100%;
    text-align: center;
    top: 90px;
}

.echartsLabelp1 {
    font-size: 44px;
    font-weight: bold;
    color: #333333;
}

.echartsLabelp2 {
    font-size: 14px;
    color: #888888;
}

.ewm-data-flex {
    width: 160px;
    top: 0;
    right: 0;
    background: #fff;
    position: absolute;
}

.ewm-table-th1 {
    width: 6%
}

.ewm-table-th2 {
    width: 14%
}

.ewm-table-th3 {
    width: 10%
}

.ewm-table-th4 {
    width: 24%;
}

.ewm-table-tbody .ewm-table-th4 {
    display: flex;
    align-items: center;
}

.ewm-table-th5 {
    width: 7%
}

.ewm-table-th6 {
    width: 11%
}

.ewm-table-th7 {
    width: 10%
}

.ewm-table-th8 {
    width: 10%
}

.ewm-table-th9 {
    width: 15%
}

.ewm-table-th4 img, .ewm-table-th9 img {
    display: inline-block;
    width: 20px;
}

.ewm-table-th4 .span {
    font-size: 12px;
    margin: 0 8px;
}

.ewm-table-th4 .layui-row {
    display: inline-block;
    width: calc(100% - 60px);
}

.mc-info-box {
    padding: 8px 0;
}

.mc-info-title {
    cursor: pointer;
    height: 28px;
    line-height: 28px;
    background-color: #fff0f0;
    border-radius: 4px;
    border: solid 1px #f5212d;
    font-size: 14px;
    color: #f5212d;
    padding: 0 8px;
    display: inline-block;
}

.mc-info-list {
    width: 100%;
    margin-top: 20px;
}

.mc-info-li {
    margin: 10px 0;
}

.mc-info-label {
    font-size: 14px;
    color: #888888;
    width: 36%;
    float: left;
}

.mc-info-value {
    font-size: 14px;
    width: 60%;
    float: left;
    color: #333333;
}

.mc-info-right {
    text-align: center;
    padding: 0 10px;
}

.mc-info-name {
    display: inline-block;
    width: 100%;
    font-size: 14px;
    color: #000000;
}

.mc-info-img {
    width: 100%;
    padding: 4px;
    box-sizing: border-box;
    border-radius: 4px;
    border: solid 1px #cccccc;
    margin-top: 12px;
}

.mc-info-img img {
    width: 100%;
}

.ewm-data-value {
    padding: 15px 30px 0 0;
    box-sizing: border-box
}

.ewm-data-value-div {
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 0 20px;
    box-sizing: border-box;
    margin-top: 6px;
}

.ewm-data-value-header {
    height: 45px;
    line-height: 45px;
}

.data-left-title {
    float: left;
}

.data-left-title .iconfont {
    font-size: 14px;
    color: #C20000;
}

.data-left-title span {
    font-size: 14px;
    color: #000000;
}

.data-right-title {
    float: right;
    font-size: 14px;
    color: #888888;
}

.data-left-title span {
}

.ewm-data-value-from {
    border-bottom: 1px solid #eee;
    padding: 6px 0;
}

.ewm-from-label {
    float: left;
    height: 34px;
    line-height: 34px;
    font-size: 14px;
    color: #999999;
    display: inline-block;
    width: 100px;
}

.ewm-from-value {
    float: right;
    width: calc(100% - 100px);
    height: 34px;
    line-height: 34px;
    padding: 0;
}

.ewm-data-table {
    border-radius: 4px;
    border: solid 1px #eeeeee;
    overflow: hidden;
    width: 100%;
    margin: 10px 0 30px;
}

.ewm-data-table-td {
    width: 25%;
    float: left;
    height: 36px;
    line-height: 36px;
    border-right: 1px solid #eee;
    border-bottom: 1px solid #eee;
    font-size: 14px;
    box-sizing: border-box;
}

.ewm-data-table-2 {
    width: 20%;
    float: left;
    height: 36px;
    line-height: 36px;
    border-right: 1px solid #eee;
    border-bottom: 1px solid #eee;
    font-size: 14px;
    box-sizing: border-box;
}

.ewm-data-table-td .iconfont {
    font-size: 14px;
}

.ewm-data-table-header .ewm-data-table-td span {
    color: #888888;
}

.ewm-data-table-td:last-child {
    border-right-width: 0;
}

.ewm-data-table-body .ewm-data-table-td span {
    color: #000;
}

.ewm-data-td-grey {
    background: #f5f5f5;
}

.ewm-data-span{
    display: inline-block;
    width: 28px;
    height: 14px;
    border-radius: 2px;
}

.ewm-data-span-1{
    background-color: #ffa940;
}

.ewm-data-span-2{
    background-color: #c20000;
}

.ewm-data-span-3{
    background-color: #49adff;
}

.ewm-data-span-4{
    background-color: #5ee07d;
}

.handle-btn {
    width: 78px;
    height: 24px;
    line-height: 24px;
    background-image: linear-gradient(0deg,
    #f5212d 0%,
    #ff7774 100%);
    border-radius: 4px;
    text-align: center;
    display: inline-block;
    margin-top: 3px;
    cursor: pointer;
}

.handle-btn .iconfont {
    color: #ffffff;
}

.handle-btn span {
    color: #ffffff;
}

.order-button.order-button-update .iconfont, .order-button.order-button-more .iconfont {
    display: inline-block;
    color: #C20000;
    font-weight: 100;
    height: 44px;
    line-height: 44px;
    font-size: 22px;
    cursor: pointer;
    margin-left: 10px;
}

.header-tab-ul {
    width: 100%;
    text-align: center;
}

.swiper-wrapper.header-tab-ul {
    justify-content: center;
}

.header-tab-ul .header-tab-li {
    font-size: 14px;
    line-height: 40px;
    display: inline-block;
    color: #888888;
    margin: 0 10px;
    cursor: pointer;
    width: auto;
}

.header-tab-li.active {
    color: #000;
    border-bottom: 4px solid #C20000;
}

.handle-left-title {
    height: 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.handle-left-title span {
    font-size: 12px;
    color: #999999;
}

.handle-left-title .iconfont {
    font-size: 12px;
}

.handle-left-list {
    width: 100%;
    /* flex-wrap: wrap;
  justify-content: flex-start;
  display: flex; */
    height: 170px;
    overflow: auto;
}

.handle-left-li {
    float: left;
    cursor: pointer;
    margin: 5px 7px;
    padding: 0 4px 0 6px;
    box-sizing: border-box;
    width: 103px;
    height: 26px;
    line-height: 26px;
    background-color: #f5f5f5;
    border-radius: 4px;
    border: solid 1px #eeeeee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.handle-left-li.active {
    background-color: #fff0f0;
    border: solid 1px #ff0000;
}

.handle-left-li span {
    font-size: 14px;
    color: #333333;
}

.handle-left-li.active span {
    font-size: 14px;
    color: #C20000;
}

.handle-left-li .handle-left-li-icon {
}

.handle-left-li-icon .icon1 {
    font-size: 18px;
    vertical-align: middle;
}

.handle-left-li-icon .icon2 {
    font-size: 14px;
}

.handle-left-li-type {
    width: 60px;
    height: 18px;
    border-radius: 4px;
    line-height: 18px;
    text-align: center;
    border: solid 1px;
    font-size: 12px;
}

.handle-left-li-type.icon-green {
    background-color: #e1eee4;
    border: solid 1px #03ac2b;
}

.handle-left-li-type.icon-red {
    background-color: #fff0f0;
    border: solid 1px #f5212d;
}

.handle-left-li-type.icon-orange {
    background-color: #fff7e6;
    border: solid 1px #fa8b16;
}

.handle-left-li-type.icon-blue {
    background-color: #e6f7ff;
    border: solid 1px #1890ff;
}

.right-form-label {
    float: left;
    width: 70px;
    line-height: 45px;
    color: #333333;
}

.right-form-data {
    float: left;
    line-height: 45px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.table-width {
    min-width: calc(100% + 10px);
}

.right-form-data .layui-form-radio {
    margin-top: 0;
}

.right-form .right-form-data .layui-form-radio {
    margin-top: 10px;
}

.right-form-li .layui-textarea {
    height: 240px;
    background-color: #f5f5f5;
    border-radius: 4px;
}

.right-form-btn {
    height: 45px;
    line-height: 45px;
    text-align: center;
    padding-top: 10px;
    box-sizing: border-box;
}

.right-form-btn .bottom-btn {
    display: inline-block;
    width: 128px;
    height: 40px;
    line-height: 40px;
    background-color: #f5212d;
    border-radius: 4px;
    font-size: 16px;
    color: #ffffff;
    cursor: pointer;
    border: none;
}

.right-form-btn .bottom-btn.disabled {
    background-color: #999;
}

.city-data-li {
    border-radius: 4px;
    border: solid 1px #eeeeee;
    min-width: 88px;
    height: 28px;
    line-height: 28px;
    background: #f5f5f5;
    color: #333333;
    padding: 0 8px;
    box-sizing: border-box;
    font-size: 14px;
    margin: 5px 10px 5px 0;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    float: left;
}

.open-button[data-open='true'] i, .open-button.open-button2[data-open='false'] i {
    transform: rotate(180deg);
    display: inline-block;
}

.open-button.open-button2[data-open='true'] i {
    transform: rotate(0deg);
    display: inline-block;
}

::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

/* 预警模型管理 */

.pt15 {
    padding-top: 15px;
}

.header-condition-box {
    display: flex;
    align-items: center;
    padding-left: 40px;
}

.condition-item {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font-size: 14px;
    width: 270px;
}

.condition-item > span {
    display: inline-block;
    width: 160px;
}

.input-bg {
    background-color: #f5f5f5
}

.layui-input {
    color: #282828;
}

.model-select {
    font-size: 14px;
}

.search-btn-box {
    width: 100px;
    height: 30px;
    background-color: #e6f7ff;
    border-radius: 4px;
    border: solid 1px #1890ff;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #1890ff;
    cursor: pointer;
}

.after-line::after {
    content: "";
    width: 4px;
    height: 240px;
    background: #eee;
    display: inline-block;
    position: absolute;
    right: 5px;
    top: 10px;
}

.condition-box {
    display: flex;
    padding: 0 20px;
    height: 90px;
    padding-bottom: 10px;
    flex-wrap: wrap;
    align-content: flex-start;
    overflow-y: auto;
}

.condition-box > li.condition-item {
    display: inline-block;
    width: 50%;
    line-height: 24px;
}

.condition-box > li.condition-item span {
    width: 100px;
    height: 22px;
    line-height: 22px;
    display: inline-block;
    border-radius: 4px;
    margin: 3px 0;
    text-align: center;
}

.model_topic {
    border: solid 1px #40a9ff;
    color: rgb(64, 169, 255);
    background-color: rgb(230, 247, 255);
}

.monitor_group {
    border: solid 1px #ccc;
    background-color: #f5f5f5;
    color: #666;
}

.business_domain {
    color: #ffa940;
    background-color: #fff7e6;
    border: solid 1px #ffa940;
}

.condition-title {
    color: #999999;
    padding-left: 20px;
    margin: 8px 0;
}

.divid-content {
    display: flex;
    justify-content: center;
}

.divid-line {
    width: 98%;
    height: 7px;
    background-color: #eee;
}

/* .query-model-result-box {

} */
.left-content-box {
    width: 20%;
    float: left;
    box-sizing: border-box;
}

.right-content-box {
    width: 80%;
    float: left;
    box-sizing: border-box;
}

.structure-map-box {
    width: 75%;
    float: left;
    box-sizing: border-box;
    position: relative;
}

.structure-image-box {
    height: 20px;
    margin: 0 15px;
    margin-top: 15px;
}

#rule-box {
    width: 256px;
    height: 195px;
    padding: 10px 5px 0 20px;
    overflow-y: auto;
    margin-top: 4px;
}

.rule-content-box {
    width: 25%;
    float: left;
    box-sizing: border-box;
}

.query-model-result-box > li {
    display: flex;
    align-items: center;
    padding: 0 10px;
    margin-bottom: 4px;
    height: 31px;
    background: #fff;
}

.query-model-result-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
}

.query-model-result-item.active {
    background: #fff0f0;
}

.query-model-result-label {
    min-width: calc(100% - 28px);
}

.query-model-result-item.active .title, .query-model-result-item.active .modelLevel, .query-model-result-label > .iconfont {
    color: #f5212d;
}


.query-model-result-label > .title {
    max-width: calc(100% - 20px);
    display: inline-block;
    position: relative;
}

.model-template-li .query-model-result-label > .title {
    max-width: 240px;
}

.query-model-result-label > .iconfont {
    display: inline-block;
    width: 30px;
}

.query-model-result-label > .iconfont:last-child {
    text-align: center;
    display: inline-block;
}

.query-model-result-label  .newlabel{
    display: inline-block;
    width: 20px;
    height: 10px;
    margin-top: -6px;
}

.float-right {
    float: right;
}

.float-left {
    float: left;
}

.model-bg-1 {
    width: 20px;
    height: 16px;
    display: inline-block;
    background: url('../../../resource/images/bigdata/6.renyoumoxing.png') 50% 0 no-repeat;
}

.model-bg-2 {
    width: 20px;
    margin-right: 10px;
    height: 16px;
    display: inline-block;
    background: url('../../../resource/images/bigdata/4.1chongmingming.png') 0 0 no-repeat;
}

.model-bg-3 {
    width: 20px;
    height: 16px;
    display: inline-block;
    background: url('../../../resource/images/bigdata/5.1shanchu.png') 0 0 no-repeat;
}

.query-model-result-item.active .model-bg-2 {
    background: url('../../../resource/images/bigdata/4.2chongmingming.png') 0 0 no-repeat;
}

.query-model-result-item.active .model-bg-3 {
    background: url('../../../resource/images/bigdata/5.2shanchu.png') 0 0 no-repeat;
}

.query-model-result-title {
    color: #999999;
}

.query-model-result-mask {
    float: right;
}

.rule-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 7px 0;
}

.left-cmp-item, .right-cmp-item {
    width: 75px;
    height: 24px;
    background-color: #f5f5f5;
    border-radius: 4px;
    border: solid 1px #eeeeee;
    color: #333333;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.mid-cmp-item > span {
    font-size: 12px;
}

.mid-cmp-item > span:nth-child(2) {
    text-align: center;
}

.mid-cmp-item > span:first-child, .mid-cmp-item > span:last-child {
    width: 16px;
    text-align: center;
}

.rule-item.rule-condition {
    justify-content: center;
}

.rule-item {
    padding: 7px 0 7px 5px;
}

.rule-condition::before {
    content: "";
    display: inline-block;
    width: 12px;
    height: 10px;
    border-left: 1px solid #cdcdcd;
    border-top: 1px solid #cdcdcd;
    border-top-left-radius: 100%;
    position: absolute;
    top: -5px;
    left: 20px;
}

.rule-condition::after {
    content: "";
    display: inline-block;
    width: 12px;
    height: 10px;
    border-bottom: 1px solid #cdcdcd;
    border-left: 1px solid #cdcdcd;
    border-bottom-left-radius: 100%;
    position: absolute;
    top: 23px;
    left: 20px;
}

.rule-condition {
    position: absolute;
    left: -32px;
    top: 25px;
}

.rule-condition > div {
    width: 20px;
    height: 20px;
    /* background-color: #fa8b16; */
    border-radius: 4px;
    /* color: #fff; */
    color: #999;
    text-align: center;
    position: absolute;
    left: 10px;
    top: 6px;
}

.rule-item-box {
    position: relative;
    margin-top: 30px;
}

.rule-item-box:nth-child(1) {
    margin-top: 0;
}

.rule-item-box .relation {
    text-align: center;
    position: absolute;
    top: -26px;
    width: 100%;
}

.rule-item-box .line {
    position: absolute;
    top: 8px;
    width: 100%;
    height: 0;
    border: 0.5px solid rgb(155, 155, 155);
    z-index: 1;
    opacity: 0.1;
}

.warning-level-content {
    display: flex;
    justify-content: center;
    padding: 15px 10px 0 20px;
    /* margin-bottom: 15px; */
}

.warning-level {
    text-align: center;
    padding: 10px 0;
    position: relative;
    justify-content: center;
    display: flex;
    flex-wrap: wrap;
    width: 20%;
    min-height: 70px;
}

/* width: 20%; */
/* .warning-level-content > .warning-level:nth-child(1), .warning-level-content > .warning-level:nth-child(2), .warning-level-content > .warning-level:nth-child(3) {
  width: 16%;
}
.warning-level-content > .warning-level:nth-child(4), .warning-level-content > .warning-level:nth-child(5) {
  width: 26%;
} */
.warning-level .title {
    width: 56px;
    height: 13px;
    font-size: 14px;
    color: #888;
    padding-right: 5px;
}

.warning-level .warn, .warning-level .level {
    display: inline-block;
    border-radius: 4px;
    font-size: 12px;
    text-align: center;
}

.warning-level .level {
    width: 35px;
    height: 16px;
    background-color: #e5f7e9;
    border: solid 1px #25b240;
    color: #25b240;
}

.warning-level .warn {
    width: 24px;
    height: 16px;
    text-align: center;
    background-color: #fff7e6;
    border: solid 1px #fa8b16;
    color: #fa8b16;
}

.warning-level .content {
    font-size: 16px;
    color: #000;
    padding-top: 2px;
    width: 94%;
    min-height: 30px;
}

.warning-level::after {
    content: "";
    display: inline-block;
    height: 90px;
    position: absolute;
    right: 0;
    top: 0;
    border-right: 4px dotted #ccc;
}

.warning-level:last-child::after {
    content: none;
}

#LEVEL5-CONTENT {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
}

.pre-warn-box {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 10px 0;
    padding-right: 21%;
    /* padding: 0 0 20px 0; */
}

.pre-warn {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    width: 208px;
    height: 68px;
    background-color: #f5f5f5;
    border-radius: 4px;
    border: solid 1px #cccccc;
}

.pre-warn-line-box {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    width: 230px;
}

.pre-warn-line {
    display: flex;
    width: 100%;
    justify-content: center;
    background-color: #f5f5f5;
    padding: 3px 0;
    margin-top: 10px;
    border-radius: 4px;
    border: solid 1px #cccccc;
    cursor: pointer;
}

.pre-warn > p {
    width: 100%;
}

.pre-warn > p.pre-warn-rule {
    padding: 0 15px 0 20px;
    display: flex;
    justify-content: space-between;
    cursor: pointer;
}

.pre-warn > p:first-child {
    text-align: center;
    color: #888;
    font-size: 14px;
}

.pre-warn-level {

}

.same-level-warn {
    width: 24%;
    height: 68px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-color: #f5f5f5;
    border: solid 1px #cccccc;
    margin-top: 15px;
}

.same-level-warn.active, .pre-warn-line.active {
    background-color: #fff0f0;
    border: solid 1px #f5212d;
    color: #f5212d;
}

.riskDataContent .layui-table-view .layui-table th {
    height: 74px;
}
.riskDataContent.riskDataContent1 .layui-table-view .layui-table th {
    height: 48px;
}

.layui-table-sort .layui-edge {
    left: 0;
}

.riskDataContent .layui-table-view {
    border-radius: 4px;
}

.riskDataContent .layui-table-view .layui-table td, .riskDataContent .layui-table-view .layui-table th, .riskDataContent .layui-table-view {
    border-color: #eee;
}

.oneGreen.riskDataContent .layui-table tbody tr:first-child {
    background: #e5f7e9;
}

/* .riskDataContent .layui-table tbody tr:last-child {
  background: #fff0f0!important;
} */
.oneGreen.riskDataContent .layui-table-main tbody tr:first-child td {
    color: #03ac2b !important;
}

/* .riskDataContent .layui-table-main tbody tr:last-child td {
  color: #c20000!important;
} */
.riskDataContent .layui-table[lay-even] tr:nth-child(2n) {
    background: #f9f9f9;
}

.riskDataContent th .layui-table-cell {
    line-height: normal;
    height: auto;
    white-space: normal;
    padding: 0 10px;
}

.riskDataContent th .layui-table-cell > span {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
}

.selector-box {
    background-image: linear-gradient(-30deg, #f5212d 0%, #ff6d6e 100%);
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    padding: 0 18px;
}

.tagmodel-search-condition {
    margin-right: 0px;
}

.area-selector, .date-selector {
    width: 270px;
    height: 28px;
    background-color: #fee2e3;
    border-radius: 4px;
    color: #ff7774;
    text-align: center;
    font-size: 14px;
}
.area-selector{
    flex:1
}
.date-selector {
    width: 100%;
}

.area-box {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ff8a8b;
    padding: 15px 0;
}

.area-label, .area-icon, .datapicker-label {
    color: #fff;
    width: 36px;
}

.area-icon {
    padding-left: 10px;
}

.datepicker-box {

}

.area-selector > .tagmodel-popup {
    width: 415px;
    right: 0;
}

.check-group {
    display: flex;
    align-items: center;
    color: #fff;
    padding: 10px 0;
}

.check-group .layui-form-radioed > i, .check-group .layui-form-radio > i:hover, .check-group .layui-form-radioed > i {
    color: #fff;
}

.changeTime {
    display: flex;
    align-items: center;
    flex:1;
}

/*中控*/
.central-control-p {
    text-align: center;
    font-size: 14px;
    line-height: 25px;
    color: #aaaaaa;
    margin-top: 5px;
}

.central-control-list {
    margin-top: 5px;
}

.central-control-li {
    text-align: center;
    cursor: pointer;
}

.central-control-disable {
    opacity: 0.7;
    cursor: no-drop !important;
}

/*禁止点击*/
.pointer-events{
    pointer-events: none;
}


.central-control-img {
    margin-bottom: 10px;
}

.central-control-img img {
    width: 60px;
}

.central-control-li span {
    font-size: 14px;
    color: #555555;
}

/* .check-group div:first-child {

}
.check-group div:last-child {

} */
.datapicker-icon {
    font-size: 16px;
    color: #fff;
    display: inline-block;
    width: 38px;
    padding-left: 10px;
}

.right-conf {
    float: right;
    margin-top: 11px;
}

.change-tab-box {
    display: inline-block;
    float: right;
    font-size: 14px;
}

.change-tab-box li {
    float: left;
}

.noCondition {
    width: 100%;
    text-align: center;
    height: 50px;
    padding-top: 30px;
    color: #999;
}

.activity-area-box {
    height: 460px;
    overflow: hidden;
}

.activity-area-box li {
    border-bottom: dashed 1px #cccccc;
    padding-top: 9px;
    height: 48px;
    font-size: 14px;
}

.activity-area-box li > p:first-child {
    color: #555555;
    padding: 3px 0 2px 0;
}

.activity-area-box li > p:last-child {
    text-align: right;
    color: #888888;
}

.change-tab-box {
    display: flex;
    align-items: center;
    font-size: 14px;
}

.tab-box {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.tab-box > li, .change-tab-box > li {
    color: #888888;
    cursor: pointer;
}

.tab-box > li {
    width: 100px;
}

.tab-box > li, .change-tab-box > li {
    border-bottom: 4px solid #fff;
}

.tab-box > li.active, .change-tab-box > li.active {
    border-color: #f5212d;
}

.tab-icon {
    vertical-align: middle;
}

.none {
    color: #999999;
}

.noneJaudit {
    color: #999999;
}

#PROV_STR {
    display: inline-block;
    width: 205px;
    height: 100%;
    text-align: center;
}

.layui-edge.select-input-icon {
    right: 5%;
}

#warn-input, #type-input {
    padding-left: 5px !important;
}

.mid-cmp-2fields {
    display: inline-block;
    width: 150px !important;
    text-align: left !important;
}

.mid-cmp-3fields {
    width: 55px !important;
}

.area-box .right-conf {
    position: absolute;
    right: 0;
}

.btn-right {
    margin-right: 10px;
}

.underline {
    text-decoration: underline;
}

.swiper-container {
    width: 100%;
}

div.swiper-button-next-btn, div.swiper-button-prev-btn {
    top: 80px;
    height: 250px;
    width: 2.5%;
    outline: none !important;
}

div.swiper-button-next-btn, div.swiper-button-prev-btn {
    top: 80px;
    height: 150px;
    width: 2.5%;
    outline: none !important;
}

div.swiper-button-next-btn .iconfont, div.swiper-button-prev-btn .iconfont {
    font-size: 60px;
}

.swiper-button-next-btn {
    right: -24px;
    left: auto;
}

.swiper-button-prev-btn {
    left: -24px;
    right: auto;
    transform: rotate(180deg);
    -ms-transform: rotate(180deg); /* IE 9 */
    -moz-transform: rotate(180deg); /* Firefox */
    -webkit-transform: rotate(180deg); /* Safari 和 Chrome */
    -o-transform: rotate(180deg); /* Opera */
}

.swiper-button-next-btn, .swiper-button-prev-btn {
    position: absolute;
    top: 50%;
    width: calc(var(--swiper-navigation-size) / 44 * 27);
    height: var(--swiper-navigation-size);
    margin-top: calc(-1 * var(--swiper-navigation-size) / 2);
    z-index: 10;
    color: #007aff;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.swiper-button-next-btn.swiper-button-disabled, .swiper-button-prev-btn.swiper-button-disabled {
    opacity: .35;
    color: #007aff;
    pointer-events: none;
}

.swiper-slide.index-home-li {
    width: 20%;
    height: 225px;
    border-right: 4px dotted #cccccc;
    text-align: center;
}

.index-home-li-box {
    display: inline-block;
    width: 100%;
    text-align: center;
    height: 180px;
    overflow: auto;
}

.index-home-li-header {
    font-size: 14px;
    line-height: 38px;
    width: 100%;
    text-align: center;
}

.index-home-header-name {
    color: #333333;
    display: inline-block;
    cursor: pointer;
}

.index-home-header-name:hover {
    color: #f5212d;
}

.index-home-select {
    width: 80px;
    height: 30px;
    display: inline-block;
}

.index-home-select .layui-input {
    height: 20px;
}

.index-home-header-value {
    color: #f5212d;
}

.index-home-li-list {
    width: auto;
    text-align: left;
    display: inline-block;
}

.index-home-li-data {
    margin-bottom: 8px;
    font-size: 14px;
    display: flex;
    align-items: center;
}

.index-home-data-prov {
    float: left;
    width: 48px;
}

.index-home-data-img {
    width: 14px;
    float: left;
    margin: 0 4px 0 0;
}

.index-home-data-value {
    float: left;
    min-width: 26px;
    color: #888888;
}

.index-home-data-percentage {
    float: left;
    width: 76px;
    height: 16px;
    border-radius: 4px;
    background: #e5e5e5;
}

.index-home-perspan {
    height: 16px;
    background-image: linear-gradient(90deg,
    #69c0ff 0%,
    #91d5ff 100%);
    border-radius: 4px;
    display: inline-block;
}

.ztList .swiper-container {
    margin: 0;
}

.index-home-echarts {
    width: 100%;
    height: 100%;
}

.model-btn.model-btn-away {
    color: #000;
    width: 70px;
}

.model-btn.model-btn-empty {
    width: 88px;
    background: rgba(0, 0, 0, 0);
    border: solid 1px #d9d9d9;
    border-radius: 2px;
    color: #373d41;
}

.transmit-li-big .model-btn-empty {
    position: absolute;
    bottom: 145px;
}

.top-search-li {
    display: flex;
    align-items: center;
    height: 46px;
}

.index-left-box {
    padding: 10px 20px;
    box-sizing: border-box;
}

.index-average-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 5px;
}

.index-average-name {
    color: #666666;
    font-size: 14px;
}

.index-average-span {
    padding: 0 10px;
    height: 24px;
    background-color: #f8fffb;
    border-radius: 2px;
    border: solid 1px #1890ff;
    font-size: 14px;
    color: #1890ff;
    text-align: center;
    line-height: 24px;
}

.index-list-box {
    margin-top: 10px;
    padding: 10px;
    height: 248px;
    background-color: #ffffff;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    border: solid 1px #f5f5f5;
    box-sizing: border-box;
}

.index-list-head {
    font-size: 18px;
    line-height: 24px;
    color: #000000;
    margin-bottom: 10px;
}

.index-list {
    position: relative;
    max-height: 210px;
    overflow-y: auto;
    padding: 0 10px;
}

.index-list.finance-data {
    padding-left: 30px;
}

.finance-data .list-tubiao {
    position: absolute;
    left: -22px;
}

.index-list-li-2 {
    width: calc(100% - 34px);
}

.index-list-li:before {
    position: absolute;
    content: '';
    height: calc(100% + 9px);
    left: 4px;
    top: 8px;
    width: 1px;
    border-right: 1px dotted #999;
}

.finance-data .index-list-li:before {
    position: absolute;
    content: '';
    height: 100%;
    left: -18px;
    top: 8px;
    width: 1px;
    border-right: 1px dotted #999;
}

.index-list-li {
    margin: 10px 0;
    position: relative
}

.index-list-li-1 {
    position: relative;
}

.finance-data .index-list-li-2 {
    width: calc(100% - 34px);
}

.list-tubiao {
    float: left;
    line-height: 14px;
    position: relative;
    z-index: 99999;
    background: #fff;
}

.list-type {
    float: left;
    display: inline-block;
    text-align: center;
    border-radius: 4px;
    width: 16px;
    height: 16px;
    font-size: 14px;
    line-height: 16px;
    color: #ffffff;
    margin: 0 20px;
}

.list-right-value {
    display: flex;
    float: right;
    justify-content: space-between;
    width: calc(100% - 70px);
    line-height: 16px;
}

.list-prov {
    font-size: 16px;
}

.table-prov {
    font-size: 14px;
    line-height: 20px;
    color: #40a9ff;
    text-decoration: underline;
    cursor: pointer;
}

.O_table {
    width: 68px;
    height: 24px;
    text-align: center;
    display: inline-block;
    line-height: 24px;
    /* color: #fa8b16; */
    background-color: #fa8b16;
}

.Y_table {
    width: 68px;
    height: 24px;
    text-align: center;
    display: inline-block;
    line-height: 24px;
    /* color: #eac600; */
    background-color: #eac600;
}
.R_table {
    width: 68px;
    height: 24px;
    text-align: center;
    display: inline-block;
    line-height: 24px;
    /* color: #; */
    background-color: #f5212d;
}
.list-value {
    font-size: 16px;
}

.index-list-li-2 {
    margin: 10px 0;
    float: right;
    width: calc(100% - 70px);
    background: #f5f5f5;
    border-radius: 2px;
    box-sizing: border-box;
}

.index-list-2-top {
    background-color: #dbeeff;
    border-radius: 2px;
}

.finance-list-data .index-header-1.finance-li.finance-li-img {
    text-align: left;
    padding-left: 4px;
    box-sizing: border-box;
}

.index-list-2-li {
    width: 100%;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #ddd;
    height: 35px;
    line-height: 35px;
    box-sizing: border-box;
    padding: 0 10px;
}

.index-list-2-data .finance-li {
    color: #666666;
}

.list-2-type {
    display: inline-block;
    text-align: center;
    border-radius: 4px;
    width: 16px;
    height: 16px;
    font-size: 14px;
    line-height: 16px;
    color: #ffffff;
}

.list-2-prov {
    color: #666;
}

.list-2-value {
}

.index-list-header {
    padding-left: 40px;
    border-bottom: 1px solid #e5e5e5;
}

.index-list-header span {
    height: 36px;
    line-height: 36px;
    font-size: 14px;
    color: #999999;
}

.index-header-1{width:20%;float: left; text-align: center;}
.index-header-2{width:20%;float: left; text-align: center;}
.index-header-3{width:20%;float: left; text-align: center;}
.index-header-4{width:20%;float: left; text-align: center;}
.index-header-5{width:20%;float: left; text-align: right;}

.finance-list {

}


.finance-list .finance-li {
    height: 28px;
    line-height: 28px;

}

.caliBer-title {
    border: 1px solid #1890ff;
    color: #1890ff;
    border-radius: 4px;
    padding: 10px;
    box-sizing: border-box;
    background: #e6f7ff;
}

.finance-li .list-prov, .finance-li {
    color: #1890ff;
    font-size: 14px;
}

.finance-list-data .finance-li {
    color: #000;
}

.finance-li-1 {
    display: flex;
    align-items: center;
}

.finance-li-1 .list-type {
    margin: 0 4px;
}

.finance-li-img {
    float: left;
    width: 40px;
}

.finance-li-right-data {
    width: calc(100% - 40px);
    float: right;
}

/*大数据开始*/
.bigdata-top {
    height: 128px;
    padding: 10px 0;
    box-sizing: border-box;
}

.bigdata-top-left {
    float: left;
    width: 334px;
    box-sizing: border-box;
    border-right: solid 1px #e5e5e5;
    height: 100%;
}

.bigdata-top-left-img {
    width: 114px;
    height: 102px;
    margin-left: 48px;
}

.bigdata-top-left-data {
    min-width: 110px;
    float: right;
    margin-top: 20px;
}

.bigdata-top-left-data .bigdata-top-left-span {
    font-size: 14px;
    color: #999999;
    display: block;
}

.bigdata-top-left-data .bigdata-top-left-number {
    font-size: 48px;
    color: #000000;
    font-weight: bold;
    display: block;
}

.bigdata-top-right {
    width: calc(100% - 334px);
    float: right;
    height: 100%;
}

.bigdata-top-right .bigdata-nav-ul {
    height: 100%;
    overflow-y: auto;
}

.bigdata-top-right .bigdata-nav-ul .bigdata-nav-li {
    float: left;
    display: flex;
    align-items: center;
    height: 54px;
    margin: 0 12px;
    cursor: pointer;
}

.bigdata-nav-li .bigdata-nav-name {
    width: 120px;
    height: 32px;
    background-color: #f5f5f5;
    border-radius: 2px;
    border: solid 1px #e5e5e5;
    display: inline-block;
    font-size: 16px;
    text-align: center;
    line-height: 32px;
    overflow: hidden;
}

.bigdata-nav-li .bigdata-nav-num {
    display: inline-block;
    font-size: 24px;
    color: #000;
    margin-left: 6px;
    width: 54px;
}

.bigdata-nav-li.active .bigdata-nav-name {
    color: #C20000;
    border: solid 1px #c20000;
    background-color: #fff0f0;
}

.bigdata-nav-li.active .bigdata-nav-num {
    color: #C20000;
}

.bigdata-header {
}

.bigdata-header-tab {
    float: left;
    width: 310px;
    margin-top: 6px;
}

.bigdata-header-tab-btn {
    float: left;
    width: 102px;
    height: 40px;
    line-height: 40px;
    background-color: #e5e5e5;
    font-size: 18px;
    color: #999999;
    margin-right: 1px;
    cursor: pointer;
}

.bigdata-header-tab-btn.active {
    background-color: #f5212d;
    color: #fff;
}

.scroll-box {
    width: 400px;
    height: 36px;
    overflow: hidden;
    display: inline-block;
    float: left;
    padding-right: 10px;
}

.scroll-box ul {
    list-style: none;
    height: 100%;
}

.scroll-box ul li {
    width: 400px;
    height: 36px;
    box-sizing: border-box;
    line-height: 36px;
    text-align: left;
    display: flex;
    justify-content: space-between;
}

.scroll-box ul li span {
    font-size: 14px;
    color: #888888;
}

.scroll-box ul li .span1 {
    width: 300px;
}

.bigdata-header-padding {
    height: 100%;
    border-right: 1px solid #ddd;
}

.bigdata-header-news {
    float: left;
    height: 34px;
    padding: 6px 0;
}

.bigdata-header-news-name {
    float: left;
    margin: 0 12px;
    height: 100%;
    line-height: 36px;
}

.right-more {
    margin: 0 10px;
    float: right;
    color: #ddd;
    line-height: 38px;
    height: 35px;
    cursor: pointer;
}

.bigdata-header-model-list {
    float: right;
}

.bigdata-header-model-li {
    display: flex;
    align-items: center;
    height: 36px;
    float: left;
    margin: 0 10px 0 20px;
}

.bigdata-header-model-li img {
    width: 24px;
    height: 24px;
    display: inline-block;
    margin: 0 15px;
}

.bigdata-header-model-name {
    color: #999999;
    font-size: 14px;
}

.bigdata-header-model-num {
    color: #000;
    font-size: 14px;
}

.bigdata-header-model {
    float: left;
    height: 34px;
    padding: 6px 0;
    width: calc(100% - 900px);
}

.bigdata-header-model .bigdata-header-padding {
    border-width: 0;
}

.bigdata-content {
    padding: 20px 0;
}

.bigdata-content-1 {
    float: left;
    width: 370px;
    height: 600px;
    padding: 0 20px;
    box-sizing: border-box;
}

.bigdata-content-1-1 {
    width: 100%;
    display: flex;
    justify-content: space-between;
}

.bigdata-content-1-1 span {
    font-size: 16px;
    color: #000000;
}

.bigdata-content-1-2 {
    margin: 10px 0;
}

.bigdata-content-1-2-input {
    float: left;
    width: 280px;
    height: 30px;
    background-color: #f5f5f5;
    border-radius: 2px;
    border: solid 1px #e5e5e5;
    line-height: 28px;
    padding: 0 10px;
    box-sizing: border-box;
}

.bigdata-content-1-2-input i {
    cursor: pointer;
}

.bigdata-content-1-2-scatter {
    float: right;
    width: 28px;
    height: 28px;
    border: 1px solid #1890ff;
    background-color: #e6f7ff;
}

.bigdata-content-1-2-input input {
    background-color: #f5f5f5;
    border-width: 0;
}

.bigdata-content-1-3 {
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    border-radius: 2px;
    border: solid 1px #e5e5e5;
    height: 527px;
    overflow-y: auto;
}

.bigdata-content-model-list {
    box-sizing: border-box;
}

.bigdata-content-model-li {
    height: 38px;
    line-height: 38px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
}

.border-bottom {
    border-bottom: 1px solid #eee;
}

.model-li-1 {
    height: 100%;
    width: 36px;
    float: left;
    text-align: center;
}

.model-li-2 {
    float: left;
    text-align: center;
    width: 34px;
    min-height: 10px;
    font-size: 14px;
    color: #999999;
}

.model-li-3 {
    float: left;
    text-align: left;
    width: 210px;
    font-size: 14px;
    color: #000;
    padding-left: 8px;
}

.model-li-4 {
    float: right;
    margin-right: 4px;
}

.model-li-4 img {
    width: 26px;
}

.bigdata-content-model-li.active {
    background-color: #fff0f0;
}

.bigdata-content-model-li.active .model-li-2 {
    color: #ffa39d;
}

.bigdata-content-model-li.active .model-li-3 {
    color: #C20000;
}

.bigdata-content-2 {
    float: left;
    width: calc(100% - 380px);
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    border-radius: 2px;
    border: solid 1px #e5e5e5;
    height: 600px;
    padding: 10px 0;
    box-sizing: border-box;
}

.bigdata-content-2-1 {
    width: 40%;
    float: left;
    height: 100%;
    border-right: 1px solid #ddd;
    box-sizing: border-box;
}

.bigdata-content-2-2 {
    width: 60%;
    float: left;
    height: 100%;
}

.bigdata-model-info {
    width: 100%;
    padding: 20px;
    box-sizing: border-box;
}

.model-info-li {
    width: 100%;
    margin-top: 14px;
}

.model-info-li-label {
    float: left;
    line-height: 22px;
    width: 110px;
    color: #999999;
    font-size: 14px;
}

.model-info-li-value {
    line-height: 22px;
    display: inline-block;
    float: left;
    width: calc(100% - 120px);
}

.labelItem .dataSelect.text-right {
    text-align: right;
    padding-right: 10px;
    box-sizing: border-box;
}

.bigdata-detail {
}

.bigdata-detail .border-top {
    padding: 0 20px;
    box-sizing: border-box;
    width: 100%;
    margin-bottom: 20px;
}

.bigdata-detail .border-top span {
    display: inline-block;
    width: 100%;
    border-top: 1px solid #ddd;
}

.bigdata-echart-label {
    float: left;
}

.chart-label-li {
    margin-left: 20px;
    display: flex;
    align-items: center;
    float: left;
    height: 44px;
}

.label-li-yuan {
    width: 12px;
    height: 12px;
    display: inline-block;
    border-radius: 50%;
}

.label-li-name {
    margin-left: 4px;
    font-size: 12px;
}

.bigdata-echart {
    width: 100%;
    height: 270px;
    padding: 10px;
    box-sizing: border-box;
}

/*模型模板*/
.width20 {
    width: 20%;
    float: left;
    box-sizing: border-box;
}

.width30 {
    width: 30%;
    float: left;
    box-sizing: border-box;
}
.width25{
    width: 25%;
}
.width21-5{
    width: 21.5%;
    float: left;
    box-sizing: border-box;
}
.width60 {
    width: 40%;
    float: left;
    box-sizing: border-box;
}
.width40 {
    width: 40%;
    float: left;
    box-sizing: border-box;
}

.width80 {
    width: 80%;
    float: left;
    box-sizing: border-box;
}

.modelTemp-top {
    padding: 15px 10px;
    box-sizing: border-box;
    width: 100%;
}

.modelTemp-top-img {
    width: 100px;
    display: inline-block;
}

.modelTemp-top-img img {
    height: 98px;
    width: 98px;
}

.modelTemp-top-data {
    float: right;
    display: inline-block;
    width: calc(100% - 110px);
}

.modelTemp-top-p {
    height: 50px;
    line-height: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modelTemp-top-p span {
    font-size: 20px;
    line-height: 24px;
    color: #000000;
}

.modelTemp-top-p.active span {
    color: #C20000;
}

.modelTemp-top-right {
    width: 100%;
    float: right;
}

.modelTemp-top-right .modelTemp-nav-ul {
    height: 100%;
    overflow-y: auto;
}

.modelTemp-top-right .modelTemp-nav-ul .modelTemp-nav-li {
    float: left;
    display: flex;
    align-items: center;
    height: 54px;
    cursor: pointer;
}

.modelTemp-nav-li.active .modelTemp-nav-name {
    color: #C20000;
    border: solid 1px #c20000;
    background-color: #fff0f0;
}

.modelTemp-nav-li.active .modelTemp-nav-num {
    color: #C20000;
}

.modelTemp-nav-li .modelTemp-nav-name {
    width: 52px;
    height: 32px;
    background-color: #f5f5f5;
    border-radius: 2px;
    border: solid 1px #e5e5e5;
    display: inline-block;
    font-size: 14px;
    text-align: center;
    line-height: 32px;
    overflow: hidden;
}

.modelTemp-nav-li .modelTemp-nav-name.nav-name-center {
    width: 72px;
}

.modelTemp-nav-li .modelTemp-nav-num.nav-num-center {
    width: 40px;
}

.modelTemp-nav-li .modelTemp-nav-num {
    display: inline-block;
    font-size: 16px;
    color: #000;
    margin-left: 6px;
}

.top-grade-li {
    float: left;
    width: 50%;
    box-sizing: border-box;
    height: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    box-sizing: border-box;
}

.top-grade-li.active {
    color: #C20000;
}

.top-grade-li.active span:first-child {
    color: #C20000;
}

.top-grade-li span:first-child {
    color: #999;
}

.top-grade-li2 {
    border-right: 1px dashed #ddd;
    border-bottom: 1px dashed #ddd;
}

.top-grade-li1 {
    border-bottom: 1px dashed #ddd;
}

.top-grade-li4 {
    border-right: 1px dashed #ddd;
}

.top-grade-li3 {
}

.model-template-title {
    padding: 10px 4px 10px 20px;
    border-bottom: 1px solid #eee;
}

.model-img-li {
    width: 182px;
    float: left;
    padding: 10px 5px;
    box-sizing: border-box;
}

.model-img-li.model-img-li-label {
    width: 33.3%;
}

.model-img-li-div {
    width: 100%;
    height: 200px;
    border-radius: 2px;
}

.model-img-li-div1 {
    background: url('../../../../resource/images/bigdata/imglist1.png') 0 100% no-repeat;
    background-size: 100%;
}

.model-img-li-div2 {
    background: url('../../../../resource/images/bigdata/imglist2.png') 0 100% no-repeat;
    background-size: 100%;
}

.model-img-li-div3 {
    background: url('../../../../resource/images/bigdata/imglist3.png') 0 100% no-repeat;
    background-size: 100%;
}

.model-img-li-div4 {
    background: url('../../../../resource/images/bigdata/imglist4.png') 0 100% no-repeat;
    background-size: 100%;
}

.model-img-li-div5 {
    background: url('../../../../resource/images/bigdata/imglist5.png') 0 100% no-repeat;
    background-size: 100%;
}

.model-img-li-div6 {
    background: url('../../../../resource/images/bigdata/imglist6.png') 0 100% no-repeat;
    background-size: 100%;
}

.model-img-li-div11 {
    border: 1px solid #ddd;
    background: url('../../../../resource/images/bigdata/imglist11.png') 0 100% no-repeat;
    background-size: 100%;
}

.model-img-li-div12 {
    border: 1px solid #ddd;
    background: url('../../../../resource/images/bigdata/imglist12.png') 0 100% no-repeat;
    background-size: 100%;
}

.model-img-li-div13 {
    border: 1px solid #ddd;
    background: url('../../../../resource/images/bigdata/imglist13.png') 0 100% no-repeat;
    background-size: 100%;
}

.model-img-list {
    padding: 0 5px;
    box-sizing: border-box;
    width: 1102px;
}

.model-img-list-box {
    width: 100%;
    overflow-x: auto;
}

.model-template-list .modelLevel {
    color: #666666;
    opacity: 0.5;
}

.model-img-li-text {
    width: 100%;
    padding: 20px 10px;
    box-sizing: border-box;
}

.model-img-text-name {
    font-size: 18px;
    line-height: 24px;
    color: #333333;
}

.model-img-text-title {
    font-size: 14px;
    line-height: 24px;
    color: #999999;
}

.model-ranking-search {
    width: 100%;
    padding-left: 20px;
    box-sizing: border-box;
    line-height: 40px;
    height: 40px;
}

.model-ranking-list {
    padding: 0 10px;
    box-sizing: border-box;
}

.model-ranking-li {
    border-bottom: 1px dashed #ddd;
    height: 50px;
    line-height: 50px;
    display: flex;
    justify-content: space-between;
}

.model-ranking-li-left {
    width: 80%;
}

.model-ranking-li-right {
    width: 20%;
}

.model-ranking-li-left .person-data-img {
    width: 30px;
    display: inline-block;
}

.model-ranking-li-left .person-data-img img {
    width: 28px;
}

.model-ranking-li-left .person-data-img span {
    display: inline-block;
    width: 25px;
    height: 25px;
    background: #ffa39d;
    text-align: center;
    line-height: 25px;
    border-radius: 50%;
    color: #fff;
}

.model-ranking-li-left span {
    font-size: 14px;
}

.model-ranking-li-right .iconfont {
    color: #999;
}

.model-ranking-li-right span {
    font-size: 14px;
    color: #999;
    display: inline-block;
    width: 25px;
    text-align: right;
}

.query-model-result-box {
    overflow: auto;
}

.question-ul {
}

.question-header {
    line-height: 38px;
    color: #a9b0b4;
}

.question-header li:nth-child(1) {
    padding-left: 34px;
    box-sizing: border-box;
}

.question-li {
    line-height: 40px;
    border-bottom: 1px dashed #cccccc;
}

.question-li-1 {
    float: left;
    width: 62%;
}

.question-li-2 {
    float: left;
    width: 14%;
}

.question-li-3 {
    float: left;
    width: 24%;
}

.question-li-4 {
    float: right;
    width: 24%;
    display: flex;
    justify-content: space-between;
}

.question-li-ranking {
    width: 20px;
    height: 20px;
    line-height: 22px;
    color: #fff;
    display: inline-block;
    border-radius: 50%;
    text-align: center;
}

.question-li-ranking1 {
    background-color: #73777a;
}

.question-li-ranking2 {
    background-color: #a9b0b4;
}

.question-li-text {
    margin-left: 10px;
    color: #555555;
}

.question-li-2 .iconfont {
    color: #ff4e4f;
}

.question-li-3 .iconfont, .question-li-4 .iconfont {
    color: #ffa940;
}

.echart-text-box {
    position: absolute;
    width: calc((100% - 40px) / 2);
    height: 100px;
    top: 56px;
    z-index: 9;
}

.echart-text-box p {
    text-align: center;
    line-height: 32px;
}

.echart-text-p {
    color: #73777a;
}

.echart-text-num {
    color: #181818;
    font-size: 20px;
}

.right-legend-box {
    width: calc(50% - 20px);
    position: absolute;
    right: 20px;
    top: 0;
    height: 100%;
    overflow: auto;
    display: flex;
    align-items: center;
    z-index: 99;
}

.right-legend-box ul {
    width: 100%;
}

.right-legend-box li {
    width: 100%;
    display: inline-block;
    line-height: 24px;
    color: #73777a;
}

.right-legend-box li .li-1 {
    float: left;
    width: 40%;
}

.right-legend-box li .li-1 .span-radius {
    display: inline-block;
    width: 8px;
    height: 8px;
    background-color: #ffb180;
    border-radius: 50%;
    margin-right: 6px;
}

.right-legend-box li .li-2 {
    float: left;
    width: 30%;
}

.right-legend-box li .li-3 {
    float: left;
    width: 30%;
}

.drill-icon1 {
    margin-right: 10px;
    font-size: 20px;
    width: 20px;
}

.drill-icon2 {
    margin-right: 10px;
    width: 20px;
    font-size: 16px;
    padding-left: 2px;
    box-sizing: border-box;
}

.transmit-top-li {
    width: 12.5%;
    float: left;
}

.transmit-matters {
    margin-top: 40px;
}

.transmit-matters .transmit-p1 {
    font-size: 14px;
    line-height: 24px;
    color: #373d41;
}

.transmit-matters .transmit-p2 {
    font-size: 14px;
    line-height: 20px;
    color: #73777a;
    max-height: 102px;
    overflow: hidden;
}

#transmitList {
    min-height: 210px;
}

.transmit-li-box {
    position: relative;
    border: 1px solid #d9d9d9;
    height: 200px;
    cursor: pointer;
}

.transmit-li-big {
    position: relative;
    border: 1px solid #eee9e9;
    height: 382px;
    cursor: pointer;
}

.transmit-li-big:hover {
    box-shadow: 0px 0px 10px 0px rgba(179, 0, 0, 0.2);
    border-radius: 2px;
    border: solid 1px #b30000;
}

.transmit-li-big:hover .model-btn.model-btn-empty {
    background-color: #c20000;
    border: 1px solid #c20000;
    color: #fff;
}

/*内控体系*/
.transmit-li-nktx {
    background: url('../../../../resource/images/infoFilling/1.1.png') 0 100% no-repeat;
    background-size: 100% 100%;
}

/*审计整改工作报告*/
.transmit-li-sjzg {
    background: url('../../../../resource/images/infoFilling/1.2.png') 0 100% no-repeat;
    background-size: 100% 100%;
}

/*审计工作开展情况*/
.transmit-li-sjgz {
    background: url('../../../../resource/images/infoFilling/1.3.png') 0 100% no-repeat;
    background-size: 100% 100%;
}

/*四级人才绩效考核结果
及省内积分结果*/
.transmit-li-sjrc {
    background: url('../../../../resource/images/infoFilling/1.4.png') 0 100% no-repeat;
    background-size: 100% 100%;
}

/*审计问题整改台账更新*/
.transmit-li-sjwt {
    background: url('../../../../resource/images/infoFilling/1.5.png') 0 100% no-repeat;
    background-size: 100% 100%;
}

/*审计问题整改工作方案*/
.transmit-li-sjfa {
    background: url('../../../../resource/images/infoFilling/1.6.png') 0 100% no-repeat;
    background-size: 100% 100%;
}

/*年度重大风险评估报告*/
.transmit-li-ndzd {
    background: url('../../../../resource/images/infoFilling/1.7.png') 0 100% no-repeat;
    background-size: 100% 100%;
}

/*内审调查表*/
.transmit-li-nsdc {
    background: url('../../../../resource/images/infoFilling/1.8.png') 0 100% no-repeat;
    background-size: 100% 100%;
}

.transmit-li-box.active {
    border-color: #C20000;
}

.transmit-li-img {
    width: 120px;
}

.transmit-li-img, .transmit-big-img {
    position: absolute;
    right: 10px;
    bottom: 10px;
}

.new-text-red {
    color: #C20000;
}

.transmit-li-content {
    padding: 20px 16px 0;
}

.transmit-li-big .transmit-li-p1 {
    font-size: 24px;
    line-height: 24px;
}

.transmit-li-big .transmit-li-content {
    padding: 30px;
}

.transmit-li-p1 {
    font-size: 18px;
    color: #333333;
    line-height: 28px;
}

.transmit-li-p2 {
    font-size: 14px;
    color: #999999;
    line-height: 24px;
}

.report-list-box {
    width: 100%;
    height: 540px;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
    border-radius: 2px;
    border: solid 1px #f0f0f0;
    padding: 20px;
    box-sizing: border-box;
}

.report-list {
}

.report-li {
    padding-left: 30px;
    box-sizing: border-box;
    width: 100%;
    height: 124px;
}

.report-li-1 {
    height: 36px;
    line-height: 36px;
    width: 100%;
    position: relative;
    cursor: pointer;
}

.report-li-1:before {
    position: absolute;
    content: '';
    left: -30px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #fff;
    border: solid 1px #C20000;
}

.active .report-li-1:before {
    position: absolute;
    content: '';
    left: -30px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #C20000;
    border: solid 1px #C20000;
}

.report-li-1-state {
    float: left;
    width: 68px;
    height: 20px;
    line-height: 20px;
    border-radius: 2px;
    text-align: center;
    font-size: 12px;
}

.report-li-1-name {
    padding-left: 20px;
    box-sizing: border-box;
    color: #181818;
}

.report-li-1-state1 {
    background-color: #e6f2e1;
    border: solid 1px #52c41a;
    color: #52c41a;
}

.report-li-1-state2 {
    background-color: #f7ece4;
    border: solid 1px #fd8f3c;
    color: #fd8f3c;
}

.active .report-li-1-name {
    color: #C20000;
}

.report-li-1-name-width {
    width: 45%;
}

.report-li-1-class {
    width: 30%;
    color: #73777a;
}

.report-li-1-right {
    position: absolute;
    right: 0;
    top: 0;
    color: #73777a;
    cursor: pointer;
}

.report-li-2 {
    background-color: #f0f2f5;
    padding: 8px 20px;
    position: relative;
}

.report-li-2:before {
    position: absolute;
    content: '';
    height: 105px;
    left: -26px;
    top: -12px;
    width: 1px;
    border-right: 1px solid #d9d9d9;
}

.report-li-2-text {
    line-height: 24px;
    color: #73777a;
}

.tran-select {
    margin-right: 10px;
}

.card-box-search {
    width: 100%;
    display: flex;
    align-items: center;
    margin: 10px 0;
    background-color: #f5f5f5;
    border-radius: 2px;
    border: solid 1px #d9d9d9;
}

.card-box-search .layui-input {
    width: 80%;
    height: 28px;
    background-color: #f5f5f5;
    border-radius: 2px;
    border-width: 0;
}

.card-box-search .icon {
    font-size: 20px;
    color: #666666;
    margin-left: 10px;
    cursor: pointer;
}

.new-style.special-input .tran-select .layui-select-title .layui-input {
    margin-top: 0;
}

/*指标*/
.expression-list {
    margin: 10px 0;
    height: 28px;
    display: flex;
    justify-content: space-between;
}

.expression-li {
    min-width: 28px;
    padding: 0 10px;
    box-sizing: border-box;
    height: 26px;
    background-color: #888888;
    border-radius: 5px;
    display: inline-block;
    font-size: 14px;
    color: #fff;
    line-height: 26px;
    text-align: center;
    cursor: pointer;
}

.expression-li:active {
    background-color: #C20000;
}

.expression-text {
    border: 1px solid #DDD;
    height: 234px;
    font-size: 14px;
    width: 100%;
}

.expression-index-list {
    height: 234px;
    overflow-y: auto;
}

.expression-index-list .expression-index-li {
    display: block;
    height: 24px;
    line-height: 24px;
}

/*附件*/
.vio-file-box {
    border: 1px solid #d9d9d9;
}

.vio-file-border {
    border-right: 1px solid #d9d9d9;
}

.vio-file-box .vio-file-div {
    display: flex;
    width: 100%;
    border-bottom: 1px solid #d9d9d9;
}

.vio-file-box .vio-file-div:last-child {
    border-bottom-width: 0;
}

.vio-file-div .vio-file-type {
    background-color: #f0f2f5;
    color: #73777a;
    min-height: 48px;
    padding: 0 10px;
    box-sizing: border-box;
}

.vio-file-div .vio-file-type .text-red {
    font-weight: 900;
    margin-top: 6px;
    margin-right: 6px;
}

.vio-file-div .vio-file-download {
    justify-content: center;
}

.vio-file-div .vio-file-content {
    min-height: 48px;
}

.vio-file-download .vio-file-down {
    padding: 0 4px;
}

.vio-file-download .vio-file-down {
    border-right: 1px solid #d9d9d9;
}

.vio-file-download .vio-file-down:last-child {
    border-right-width: 0;
}

.vio-file-content .vio-file-list .vio-file-li {
    padding-left: 10px;
    box-sizing: border-box;
    border-bottom: 1px solid #d9d9d9;
    min-height: 48px;
}

.vio-file-content .vio-file-list .vio-file-li:last-child {
    border-bottom-width: 0;
}

.vio-file-user, .vio-file-time {
    height: 48px;
    display: flex;
    align-items: center;
}

.process-box {
    display: flex;
    position: relative;
    height: 64px;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #d9d9d9;
}

.process-box:before {
    position: absolute;
    content: '';
    left: 0;
    top: 31px;
    width: 100%;
    z-index: 10;
    height: 1px;
    border-bottom: 1px solid #d9d9d9;
}

.process-box .process-li {
    padding: 0 16px;
    background: #fff;
    z-index: 999;
    position: relative;
}

.process-li .process-number {
    width: 32px;
    height: 32px;
    background-color: #d9d9d9;
    display: inline-block;
    text-align: center;
    line-height: 32px;
    border-radius: 50%;
    color: #73777a;
    font-size: 14px;
    margin-right: 16px;
}

.process-li .process-name {
    letter-spacing: 1px;
    color: #a9b0b4;
    font-size: 14px;
}

.process-li.green .process-number {
    background-color: #ffe2e4;
    color: #C20000;
}

.process-li.green .process-name {
    color: #C20000;
}

.process-li.select:before {
    position: absolute;
    content: '';
    bottom: -16px;
    width: 100%;
    left: 0;
    height: 4px;
    background-color: #C20000;
}

.process-li.active .process-number {
    background-color: #C20000;
    color: #fff;
}

.process-li.active .process-name {
    color: #C20000;
}

.verify-top-title {
    color: #a9b0b4;
    padding: 10px 0;
    text-align: center;
    border-bottom: 1px solid #d9d9d9;
}

.verify-bottom-title {
    color: #a9b0b4;
    padding: 10px 0;
    text-align: center;
    border-top: 1px solid #d9d9d9;
}

.transfer-box {
}

.transfer-li {
    margin-bottom: 10px;
}

.transfer-li-info {
    height: 45px;
    padding: 0 10px;
    line-height: 45px;
    background-color: #ffffff;
    border-radius: 2px;
    border: solid 1px #d9d9d9;
}

.transfer-li-info span {
    font-size: 14px;
    color: #373d41;
}

.transfer-li-img {
    text-align: center;
    line-height: 45px;
}

.transfer-li-edit {
    line-height: 45px;
}

.transfer-li-edit .icon {
}

.check-company-list {
}

.check-company-li {
    margin-bottom: 20px;
}

.check-company-header {
    width: 100%;
    height: 32px;
    background-color: #e6f7ff;
    line-height: 32px;
    padding: 0 20px;
    box-sizing: border-box;
}

.check-company-header-name {
    color: #1890ff;
}

.check-company-header-btn {
    color: #1890ff;
}

.check-company-header-btn1 .iconfont {
    transform: rotate(90deg);
    display: inline-block;
}

.check-company-header-btn2 .iconfont {
    transform: rotate(-90deg);
    display: inline-block;
}

.check-company-conter {
    padding: 20px 0 0;
}

.prompt-input {
    height: 32px;
    border-radius: 2px;
    display: inline-flex;
    line-height: 32px;
    border: solid 1px #d9d9d9;
    padding: 0 10px;
    width: 135px;
    background: #fff;
    overflow: hidden;
}

.new-table-radio .layui-form-radio {
    margin-top: 0px;
    margin-right: 4px;
}

.new-table-radio .layui-form-radio > i {
    margin-right: 4px;
}

.table-checkbox.checkbox-box {
    margin-bottom: 0;
}

.new-style .table-checkbox .layui-unselect.layui-form-checkbox {
    margin-top: 2px;
}

.new-style .table-checkbox .layui-form-checkbox span {
    color: #000;
}

.new-style .table-checkbox .layui-form-checked span, .new-style .table-checkbox .layui-form-checked:hover span, .new-style .table-checkbox .layui-form-checkbox span {
    background: #fff;
}

.inline-block {
    display: inline-block;
}

.layui-icon-right {
    display: inline-block;
    transform: rotate(-90deg);
    top: 14px !important;
}

.modifyRecord{
    position: fixed;
    right:20px;
    top:0px;
    z-index: 9999;
}
.leader-content .layui-card {
    margin: 0;
    padding: 4px;
    box-sizing: border-box;
    background-color: transparent;
    box-shadow: none;
    width: 100%;
}

.layui-card-box {
    width: 100%;
    height: 100%;
    background-color: #fff;
    box-shadow: 0px 0px 10px 0px rgb(0 0 0 / 10%);
    padding: 8px 20px;
    box-sizing: border-box;
}

.p-title {
    display: block;
    font-size: 14px;
    line-height: 24px;
    color: #000000;
}
.p-title .iconfont{
    margin-right:4px;
}
.add-btn {
    display: inline-block;
    width: 88px;
    height: 28px;
    line-height: 28px;
    background-color: #C20000;
    border-radius: 2px;
    text-align: center;
    color: #ffffff;
    cursor: pointer;
}
.layui-common-card.layui-tab-brief .layui-tab-content .layui-tab-item {
    top: 46px;
}
.position-mark{
    position: absolute;
    background: #C20000;
    color: #fff;
    display: inline-block;
    width: 20px;
    height: 20px;
    line-height: 20px;
    border-radius: 50%;
    right: -10px;
    font-size: 12px;
    text-align: center;
}

.select-checkbox{
    width: 140px;
    height: 28px;
}
.checkbox-btn{
    cursor: pointer;
    text-align: center;
    width: 70px;
    height: 26px;
    display: inline-block;
    border: 1px solid #DBDBDB;
    line-height: 26px;
    color: #333333;
}
.checkbox-btn.active{
    border-color: #C20000;
    color:#C20000;
}
.checkbox-btn.checkbox-left{
    border-radius: 3px 0px 0px 3px;
}
.checkbox-btn.checkbox-right{

    border-radius:0px 3px 3px 0px;
}
.nodata p{
    font-size: 18px;
    margin-top:22px;
    letter-spacing: 6px;
}


.change_block {
    border: 1px solid #FADCDD;
    border-radius: 5px;
    height: 30px;
    line-height: 30px;
    display: inline-block;
    margin-top: 10px;
    margin-left: 20px;
}

.change_block .one_change_block {
    color: #000;
    font-size: 13px;
    padding: 0px 15px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    display: block;
    cursor: pointer;
    border-right: 1px solid #FADCDD;
    float: left;
}

.change_block .one_change_block:last-child {
    border-right: 0px;
}

.change_block_active_ {
    background-color: #FADCDD;
}
.change_block_active_R {
    background-color: #f5212d;
}
.change_block_active_O {
    background-color: #fa8b16;
}
.change_block_active_Y {
    background-color: #eac600;
}
.table_dotted{
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    position: absolute;
    left: 0%;
    top: 47%;
    transform: translate(-50%,-50%);


}
.R_table_dotted{
    background-color: #f5212d;
}
.O_table_dotted{
    background-color: #FF8E2B;
}

.Y_table_dotted{
    background-color: #FFD803;
}
.ovflowHidden_c{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.model_book{
    background-color: #fff;
    padding: 15px;
    box-shadow: 0px 0px 10px 0px rgb(155 11 9 / 10%);

    color: #333;
    width: 90px;
    position: absolute;
    z-index: 1000;
    left: 0px;

}
.model_book .one_book_btn{
    height: 40px;
    line-height: 40px;
    cursor: pointer;
    display: flex;
    align-items: center;
}
.model_book .one_book_btn .iconfont{
    color: #c20000;
    padding-right: 10px;
}

/*新指标 新样式*/
.operation-reminder {
    float: left;
    width: calc(100% - 430px);
    margin-left: 10px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #c20000;
    text-align: left;
    font-style: normal;
    height: 45px;
    display: flex;
    line-height: normal;
    align-items: center;
}


.new-style .new-unselect .layui-unselect.layui-form-checkbox {
    margin: 0;
    margin-top: 4px;
    height: auto !important;
    line-height: 32px !important;
    border-width: 0px !important;
    background: none;
    padding: 0;
}
.new-style .new-unselect .laytable-cell-checkbox .layui-unselect.layui-form-checkbox {
    margin: 0;
    height: auto!important;
    line-height: normal!important;
    border: none!important;
    padding: 0;
    background: none;
}

.new-style .new-unselect .layui-form-checkbox .layui-icon {
     display: inline-block;
}

.new-style .new-checkbox .layui-unselect.layui-form-checkbox {
    margin-top: 6px;
    height: auto !important;
    line-height: normal !important;
    border: none !important;
    margin-right: 0;
    background: none !important;
    padding: 0 ;
}
.new-style .new-checkbox  .layui-form-checkbox[lay-skin="primary"] span {
    float: right;
    padding: 0 15px 0 10px !important;
    line-height: 18px;
    background: none;
    color: #666;
}
.new-style .new-checkbox  .layui-form-checkbox .layui-icon {
     display: inline-block;
}

.new-style .new-unselect .layui-form-checkbox[lay-skin="primary"] span {
    float: right;
    padding: 0 10px;
    line-height: 32px;
    background: none;
    color: #666;
}

.new-style .new-unselect .layui-unselect.layui-form-checkbox.layui-form-checked span {
    color: #666;
}

.new-style .layui-form-label .text-red{
    margin-right:4px;
}
.layui-text{
    display: inline-block;
    color:#333;
    white-space: break-spaces;
}
.new-style .layui-text{
    padding: 5px 0;
    line-height: 23px;
    font-size: 14px;
    color: #666;
    white-space: pre-wrap;
}
.new-style .add-btn1{
    cursor: pointer;
    margin-top:8px;
    width: 100%;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 4px;
    border: 1px solid #c20000;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #c20000;
    font-style: normal;
    background: #fff;
}
.fold-icon{
    display: inline-block;
    color:#c20000;
    cursor: pointer;
    font-size: 24px;
}
.new-layui-common-card-header{
    width: 663px;
    line-height: 36px;
    height: 36px;
    padding-left: 28px;
    position: relative;
    background: linear-gradient(90deg, #FFE5E5 0%, #FFFFFF 100%);
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 15px;
    color: #333333;
    text-align: left;
    font-style: normal;
}
.new-layui-common-card-header:before {
    position: absolute;
    content: '';
    height: 8px;
    left: 10px;
    top: 15px;
    width: 8px;
    border-radius: 50%;
    background: #F5222D;
}
.flex-block{
    display: flex;
    justify-content: space-between;
}
[data-type="fold"][data-fold="true"] .fold-icon{
    transform: rotate(180deg);
}
[data-type="fold"][data-fold="false"] .layui-common-card-content{
    height: 0;
    overflow: hidden;
}
@media screen and ( max-width: 1850px ) {
    .bigdata-nav-li .bigdata-nav-name {
        width: 99px;
    }

    .model-img-li-div {
        height: 180px;
    }

    .model-img-li {
        width: 164px;
    }

    .model-img-list {
        width: 1000px;
    }

    .transmit-li-big {
        height: 345px;
    }

    .transmit-li-big .model-btn-empty {
        bottom: 100px;
    }
}

@media screen and ( max-width: 1680px ) {
    .modelTemp-nav-li .modelTemp-nav-num.nav-num-center {
        width: 35px;
    }

    .model-img-li-div {
        height: 162px;
    }

    .model-img-li {
        width: 148px;
    }

    .model-img-list {
        width: 898px;
    }

    .date-selector {
        width: 100%;
    }

    .chart-label-li {
        margin-left: 4px;
    }

    .area-selector.custom_select {
        width: 70px;
        font-size: 12px;
    }

    .bigdata-content-2-2 .area-selector.custom_select {
        width: 120px;
    }

    .bigdata-nav-li .bigdata-nav-name {
        width: 86px;
        font-size: 14px;
    }

    .bigdata-nav-li .bigdata-nav-num {
        font-size: 18px;
        width: 42px;
    }

    .bigdata-top-right .bigdata-nav-ul .bigdata-nav-li {
        margin: 0 8px;
    }

    .header-condition-box {
        padding-left: 0;
    }

    .btn-right {
        margin-right: 10px;
    }

    .query-model-result-label > .title {
        max-width: 110px;
    }

    .model-template-li .query-model-result-label > .title {
        width: 210px;
    }

    .query-model-result-label > .iconfont {
        width: 15px;
    }

    .condition-item > span {
        width: 120px
    }

    #rule-box {
        width: 230px;
    }

    .mid-cmp-2fields {
        width: 125px !important;
    }

    .mid-cmp-3fields {
        width: 30px !important;
    }

    .index-list-header span, .finance-li .list-prov, .finance-li, .list-type {
        font-size: 12px;
    }

    .bigdata-header-model-li {
        margin: 0 0px 0 10px;
    }

    .bigdata-header-model-li img {
        margin: 0 6px;
        width: 22px;
        height: 22px;
    }

    .bigdata-header-news-name {
        margin: 0 8px;
        font-size: 12px;
    }

    .bigdata-header-model-name {
        font-size: 12px;
    }

    .scroll-box ul li span {
        font-size: 12px;
    }

    .bigdata-header-tab-btn {
        font-size: 14px;
    }

    .bigdata-detail #labelDetail {
        padding: 10px 0 10px 20px;
        box-sizing: border-box;
    }

    .transmit-li-box {
        height: 190px;
    }

    #transmitList {
        min-height: 200px;
    }

    .transmit-li-big {
        height: 300px;
    }

    .transmit-li-img {
        width: 100px;
    }

    .transmit-big-img {
        width: 150px;
    }

    .transmit-li-content {
        padding: 20px 16px 0;
    }

    .transmit-li-p1 {
        font-size: 16px;
        line-height: 26px;
    }

    .transmit-li-p2 {
        font-size: 12px;
        line-height: 20px;
    }

    .transmit-li-big .transmit-li-p1 {
        font-size: 18px;
        line-height: 20px;
    }

    .transmit-matters {
        margin-top: 20px;
    }

    .transmit-matters .transmit-p2 {
        font-size: 12px;
        max-height: 80px;
    }

    .transmit-li-big .model-btn-empty {
        bottom: 80px;
    }

    .ewd-li-5-percentage {
        font-size: 12px;
    }
}

@media screen and ( max-width: 1600px ) {
    .ewd-li-5-top{
        font-size: 12px;
    }
    .ewd-li-5 .ewd-li-box {
        padding: 10px;
    }
    .date-selector, #PROV_STR {
        width: 195px;
    }

    .header-condition-box {
        padding-left: 0;
    }

    #rule-box {
        width: 210px;
        padding-right: 0;
    }

    .mid-cmp-2fields {
        width: 100px !important;
    }

    .layui-card-header-title {
        font-size: 14px;
    }

    .right-btn-box .layui-form-radio {
        margin-right: 2px;
    }

    .right-btn-box .layui-form-radio div {
        font-size: 12px;
    }

    .layui-form-radio * {
        font-size: 12px;
    }

    .layui-form-radio > i {
        margin-right: 2px;
        font-size: 12px;
    }

    .new-style .layui-form-radio > i {
        margin-right: 8px;
        font-size: 18px;
    }

    .new-style .layui-form-radio * {
        font-size: 14px;
    }

    .ewd-li-bottom .ewd-li-bottom-li-model {
        padding: 2px;
    }

    .ewd-li-top .ewd-li-top-left span {
        margin-left: 2px;
        font-size: 12px;
    }
    .ewd-li-top-right span{
        font-size: 14px;
    }
    .ewd-li{
        padding: 2px;
    }


  .ewd-li-box {
        padding: 5px;
    }

    .mid-cmp-3fields {
        width: 20px !important;
    }

    .left-cmp-item, .right-cmp-item {
        width: 70px;
    }

    .mc-info-label {
        width: 40%;
    }

    .area-selector .ovflowHidden {
        max-width: 100px;
    }


    .index-average-name {
        font-size: 12px;
    }

    .index-average-span {
        font-size: 12px;
    }

    .ewd-li-top .ewd-li-top-left span {
        margin-left: 2px;
        font-size: 12px;
    }

    .transmit-li-box {
        height: 150px;
    }

    #transmitList {
        min-height: 160px;
    }

    .transmit-li-big {
        height: 300px;
    }

    .transmit-li-img {
        width: 90px;
    }

    .transmit-big-img {
        width: 150px;
    }

    .transmit-li-content {
        padding: 10px;
    }

    .transmit-li-p1 {
        font-size: 12px;
        line-height: 20px;
    }

    .transmit-li-big .model-btn-empty {
        bottom: 60px;
    }
}

@media screen and ( max-width: 1480px ) {
    .chart-label-li .label-li-name {
        display: none;
    }
    .ewd-li-6-title{
     font-size: 12px;
    }
    .bigdata-content-2-2 .area-selector.custom_select {
        width: 110px;
    }

    .warning-level-content {
        margin-bottom: 0px;
    }

    .ewm-conter-li-data {
        width: 30%;
    }

    .ewm-conter-li-speed {
        width: 70%;
    }

    .ewd-li-bottom .ewd-li-bottom-li-num {
        font-size: 12px;
    }

    .bigdata-header-model {
        width: calc(100% - 620px);
    }

    .bigdata-header-tab-btn {
        width: 80px;
        height: 36px;
        line-height: 36px;
        font-size: 12px;
    }

    .bigdata-header-tab {
        width: 245px;
    }

    .scroll-box {
        width: 180px;
    }

    .scroll-box ul li {
        width: 180px;
    }

    .scroll-box ul li .span1 {
        width: 110px;
    }

    .transmit-li-big {
        height: 240px;
    }

    .transmit-li-img {
        width: 70px;
    }

    .transmit-li-big .transmit-li-p1 {
        font-size: 14px;
        line-height: 16px;
    }

    .transmit-matters {
        margin-top: 10px;
    }

    .transmit-li-big .transmit-li-content {
        padding: 15px;
    }
}

@media screen and ( max-width: 1440px ) {
    .ewd-li-6-title{
        font-size: 12px;
    }
    .index-average-name {
        font-size: 12px;
    }

    .index-average-span {
        font-size: 12px;
    }

    .rule-text {
        display: none;
    }

    .change-tab-box .tab-icon {
        padding: 0 5px;
    }

    .date-selector, #PROV_STR {
        width: 160px;
    }

    .search-btn-box {
        width: 75px;
    }

    .query-model-result-label > .title {
        width: 80px;
    }

    #rule-box {
        width: 190px;
    }

    .mid-cmp-2fields {
        width: 90px !important;
    }

    .left-cmp-item, .right-cmp-item {
        width: 55px;
    }

    .bigdata-nav-li .bigdata-nav-num {
        font-size: 14px;
        width: 34px;
    }

    .bigdata-nav-li .bigdata-nav-name {
        height: 26px;
        width: 65px;
        font-size: 12px;
        line-height: 27px;
    }

    .bigdata-top-right .bigdata-nav-ul .bigdata-nav-li {
        margin: 0 4px;
    }
    .ewm-data-span{
        width: 12px;
        height: 12px;
    }
}

@media screen and ( max-width: 1400px ) {
    .date-selector, #PROV_STR {
        width: 155px;
    }

    #rule-box {
        width: 180px;
    }

    .mid-cmp-2fields {
        width: 75px !important;
    }

    .left-cmp-item, .right-cmp-item {
        width: 50px;
    }

}

@media screen and ( max-width: 1200px ) {
    .new-style .depart_li_div {
        width: 170px;
    }
    .new-style .whetherRange  .ment_list {
        width: calc(100% - 290px) !important;
    }
    .new-style .ment_list {
        width: calc(100% - 210px) !important;
    }

    .depart_li.depart_li_blue1.float-left.depart_li_2 {
        width: 210px !important;
    }

    .depart_li_div.depart_li_div_before .float-right {
        width: calc(100% - 320px) !important;
    }
}
