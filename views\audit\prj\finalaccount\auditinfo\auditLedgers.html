<!--#include virtual ="include/header.html"-->
<style>
    .layui-form-pane .layui-form-switch, .layui-form-pane .layui-form-radio {
        margin-top: 2px;
        margin-left: 10px;
    }

    .tagmodel-popup {
        padding: 6px 0;
        text-align: center;
    }

    .checkbox-li {
        padding: 0 10px;
        box-sizing: border-box;
    }

    .tagmodel-popup {
        position: absolute;
        padding: 10px;
        box-sizing: border-box;
        width: 420px;
        box-shadow: 0 0 6px 0 #ddd;
        border-radius: 4px;
        background: #fff;
        z-index: 9999999;
    }
    .layui-form-pane .layui-form-switch, .layui-form-pane .layui-form-radio {
        margin-top: 2px;
        margin-left: 0px;
    }
</style>
<body>
<div class="layui-fluid larry-wrapper">
    <section class="panel panel-padding">
        <form class="layui-form layui-form-pane form-conmon form-conmon-more" id="finalAccountAuditInfo">
            <div class="transition-500ms search-condition">
                <h4 id="tipsInfo" style="color: red;margin-bottom: 10px;display: none">温馨提示：明细钻取存在台账冲减的，会存在数据不一致。</h4>
                <div class="layui-form-item layui-form-item-sm">
                    <div class="layui-row layui-col-space10 ">
                        <div class="layui-col-xs4">
                            <label class="layui-form-label">项目名称</label>
                            <div class="layui-input-block">
                                <input class="layui-input" id="projectName" lay-filter="projectName"
                                       name="projectName"
                                       placeholder="项目名称"/>
                            </div>
                        </div>
                        <div class="layui-col-xs4">
                            <label class="layui-form-label">项目编码</label>
                            <div class="layui-input-block">
                                <input class="layui-input" id="projectCode" lay-filter="projectCode"
                                       name="projectCode"
                                       placeholder="项目编码"/>
                            </div>
                        </div>
                        <div class="layui-col-xs4">
                            <label class="layui-form-label">任务编号</label>
                            <div class="layui-input-block">
                                <input class="layui-input" id="TASK_NUMBER" lay-filter="TASK_NUMBER"
                                       name="TASK_NUMBER"
                                       placeholder="任务编号"/>
                            </div>
                        </div>
                        <!--                        <div class="layui-col-xs4">-->
                        <!--                            <label class="layui-form-label">报审人-xxx</label>-->
                        <!--                            <div class="layui-input-block">-->
                        <!--                                <input class="layui-input" id="STAFF_NAME" lay-filter="STAFF_NAME"-->
                        <!--                                       name="STAFF_NAME"-->
                        <!--                                       placeholder="报审人"/>-->
                        <!--                            </div>-->
                        <!--                        </div>-->
                    </div>
                </div>
                <div class="layui-form-item layui-form-item-sm">
                    <div class="layui-row layui-col-space10 ">
                        <div class="layui-col-xs4">
                            <label class="layui-form-label">任务名称</label>
                            <div class="layui-input-block">
                                <input class="layui-input" id="TASK_NAME" lay-filter="TASK_NAME"
                                       name="TASK_NAME"
                                       placeholder="任务名称"/>
                            </div>
                        </div>
                        <div class="layui-col-xs3">
                            <div class="layui-form-item layui-form-item-sm">
                                <label class="layui-form-label">审计分部</label>
                                <div class="layui-input-block">
                                    <select id="auditDevisionName" lay-filter="auditDevisionName" name="auditDevisionName">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-xs1">
                            <div class="layui-form-item layui-form-item-sm">
                                <select id="auditDevisionArea" lay-filter="auditDevisionArea" name="auditDevisionArea">
                                </select>
                            </div>
                        </div>
                        <div class="layui-col-xs4">
                            <label class="layui-form-label">工单号</label>
                            <div class="layui-input-block">
                                <input class="layui-input" id="FINAL_ACCOUNT_CODE" lay-filter="FINAL_ACCOUNT_CODE"
                                       name="FINAL_ACCOUNT_CODE"
                                       placeholder="工单号"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item layui-form-item-sm">
                    <div class="layui-row layui-col-space12 ">
                        <div class="layui-col-md12 layui-col-sm12 refer-content">
                            <a class="btnMore" href="javascript:">
                                <span>更多搜索</span>
                                <i class="iconfont slide-down">&#xe603;</i>
                                <i class="iconfont slide-up noneJaudit">&#xe605;</i>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item layui-form-item-sm">
                    <div class="layui-row layui-col-space10 search-more  noneJaudit">
                        <div class="layui-col-xs4">
                            <label class="layui-form-label">审计方式</label>
                            <div class="layui-input-block">
                                <select id="auditTypeEnumId" lay-filter="auditTypeEnumId" name="auditTypeEnumId"
                                        style="width: 132px">
                                    <option></option>
                                    <option value="1">自审</option>
                                    <option value="2">委托</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-col-xs4">
                            <label class="layui-form-label">撤销状态</label>
                            <div class="layui-input-block">
                                <select id="ADJUST_FLAG" lay-filter="ADJUST_FLAG" name="ADJUST_FLAG"
                                        style="width: 132px">
                                    <option></option>
                                    <option value="1">已撤销</option>
                                    <option value="0">未撤销</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-col-xs4">
                            <label class="layui-form-label">现场复核与盘点</label>
                            <div class="layui-input-block">
                                <select id="FIELD_CHECK" lay-filter="FIELD_CHECK" name="FIELD_CHECK"
                                        style="width: 132px">
                                    <option></option>
                                    <option value="1">是</option>
                                    <option value="0">否</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item layui-form-item-sm">
                    <div class="layui-row layui-col-space10 search-more  noneJaudit">

                        <div class="layui-col-xs4">
                            <label class="layui-form-label">审结账期</label>
                            <div class="layui-input-block">
                                <input class="layui-input" id="AUDIT_MONTH" lay-filter="AUDIT_MONTH"
                                       name="AUDIT_MONTH" readonly
                                       placeholder=" 请选择审结账期范围">
                            </div>
                        </div>
                        <div class="layui-col-xs4">
                            <label class="layui-form-label">报审时间</label>
                            <div class="layui-input-block">
                                <input class="layui-input" id="SEND_AUDIT_DATE" lay-filter="SEND_AUDIT_DATE"
                                       name="SEND_AUDIT_DATE" readonly
                                       placeholder=" 请选择报审时间范围">
                            </div>
                        </div>
                        <div class="layui-col-xs4">
                            <label class="layui-form-label">接审时间</label>
                            <div class="layui-input-block">
                                <input class="layui-input" id="SEND_REPORT_DATE" lay-filter="SEND_REPORT_DATE"
                                       name="SEND_REPORT_DATE" readonly
                                       placeholder=" 请选择接审时间范围">
                            </div>
                        </div>

                    </div>
                </div>

                <div class="layui-form-item layui-form-item-sm">
                    <div class="layui-row layui-col-space10 search-more  noneJaudit">

                        <div class="layui-col-xs4">
                            <label class="layui-form-label">审结时间</label>
                            <div class="layui-input-block">
                                <input class="layui-input" id="AUDIT_COMP_DATE" lay-filter="AUDIT_COMP_DATE"
                                       name="AUDIT_COMP_DATE" readonly
                                       placeholder=" 请选择审结时间范围">
                            </div>
                        </div>
                        <div class="layui-col-xs4 ">
                            <div class="layui-form-item layui-form-item-sm">
                                <label class="layui-form-label">项目专业</label>
                                <div class="tagmodel-search-condition" onclick="professionCategoryClick( event )">
                                    <div class="layui-input-block">
                                        <div class="layui-select-title">
                                            <input class="layui-input layui-unselect" id="professionCategoryInput" placeholder="点击项目专业(可多选)"
                                                   readonly type="text" value="点击选择项目专业">
                                            <i class="layui-edge"></i>
                                        </div>
                                    </div>
                                    <div class="tagmodel-popup" id="tagmodelprofessionCategory" style="display: none">
                                        <div class="tagmodel-popup-title">-可多选-</div>
                                        <div class="checkbox-list" id="professionCategorySelect"></div>
                                        <div class="tagmodel-prov-btn">
                                            <span class="layui-new-btn new-confirm"
                                                  onclick="professionCategoryClick( event )">确认</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <div class="search-btn">
                <div class="layui-form-item layui-form-item-sm">
                    <a class="layui-btn btn-outline layui-btn-normal layui-btn-sm" id="searchBtn">
                        <i class="iconfont search-icon">&#xe60b;</i> 查询
                    </a>
                </div>
                <div class="layui-form-item layui-form-item-sm">
                    <button class="layui-btn btn-outline layui-btn-normal layui-btn-sm" id="resetBtn" type="reset">
                        <i class="iconfont search-icon">&#xe63a;</i> 重置
                    </button>
                </div>
            </div>
            <div>
                <table class="layui-table" id="infoTable" lay-filter="infoTable"></table>
            </div>
        </form>
    </section>
</div>
</body>
<!--#include virtual ="include/version.html"-->

<script id="profession-category-tpl" type="text/html">
    {{# layui.each(d, function(index, obj){ }}
    <div data-code='{{obj.code}}' data-index="{{index}}" class='checkbox-li {{obj.checked?"active":""}}'>
        {{obj.codeText}}
    </div>
    {{# }); }}
</script>

<script type="text/javascript">
    var auditDevision = getUrlParam("auditDevision")?getUrlParam("auditDevision"):'';
    var auditDevisionArea = getUrlParam("auditDevisionArea")?getUrlParam("auditDevisionArea"):'';
    var auditTypeEnumId = getUrlParam("auditTypeEnumId")?getUrlParam("auditTypeEnumId"):'';
    var stratYearMonth = getUrlParam("stratYearMonth")?getUrlParam("stratYearMonth"):'';
    var endYearMonth = getUrlParam("endYearMonth")?getUrlParam("endYearMonth"):'';
    var professionCategoryInput = getUrlParam("professionCategoryInput") ? decodeURIComponent(getUrlParam("professionCategoryInput")) : '';
    var professionCategory = getUrlParam("professionCategory") ? decodeURIComponent(getUrlParam("professionCategory")) : '';
    layui.use(['jqform', 'table', 'laytpl', 'jquery', 'laydate', 'layer', 'jqbind'], function () {
        var $ = layui.jquery,
            form = layui.jqform,
            laydate = layui.laydate,
            ctx = top.global.ctx,
            tpl = layui.laytpl,
            table = layui.table;

        // 加载表头数据
        if(auditDevision==''){
            showData();
        }

        function showData() {
            $.ajax({
                url: ctx + "/prj/auditInfo/tableTitle/" + 220001
                , dataType: "json"
                , type: "POST"
                , success: function (data) {
                    var tableData = [[], [], [], [], []];
                    var reportId = "";
                    if (data.httpCode == 200) {
                        tableData[0].push({
                            type: 'numbers',
                            title: '序号',
                            rowspan: 5,
                            width: '5%',
                            fixed: 'left'
                        });
                        $.each(data.data, function (index, obj) {
                            if (Number(obj.spanStartRow) == 0) {
                                tableData[0].push({
                                    field: obj.field,
                                    title: obj.title,
                                    style: obj.style,
                                    align: 'center',
                                    width: obj.width,
                                    colspan: Number(obj.colspan),
                                    rowspan: Number(obj.rowspan)
                                })
                            } else if (Number(obj.spanStartRow) == 1) {
                                tableData[1].push({
                                    field: obj.field,
                                    title: obj.title,
                                    style: obj.style,
                                    align: 'center',
                                    width: obj.width,
                                    colspan: Number(obj.colspan),
                                    rowspan: Number(obj.rowspan)
                                })
                            } else if (Number(obj.spanStartRow) == 2) {
                                tableData[2].push({
                                    field: obj.field,
                                    title: obj.title,
                                    style: obj.style,
                                    align: 'center',
                                    width: obj.width,
                                    colspan: Number(obj.colspan),
                                    rowspan: Number(obj.rowspan)
                                })
                            } else if (Number(obj.spanStartRow) == 3) {
                                tableData[3].push({
                                    field: obj.field,
                                    title: obj.title,
                                    style: obj.style,
                                    align: 'center',
                                    width: obj.width,
                                    colspan: Number(obj.colspan),
                                    rowspan: Number(obj.rowspan)
                                })
                            } else if (Number(obj.spanStartRow) == 4) {
                                tableData[4].push({
                                    field: obj.field,
                                    title: obj.title,
                                    style: obj.style,
                                    align: 'center',
                                    width: obj.width,
                                    colspan: Number(obj.colspan),
                                    rowspan: Number(obj.rowspan)
                                })
                            }
                        });
                        showTable(tableData);
                    }
                }
                , error: function (e) {
                    console.info('决算台账数据加载出错:' + e.info);
                }
            });
        }

        // 加载表格数据
        function showTable(tableData) {
            // 获取查询条件
            var data = conditionShow();
            table.render({
                elem: '#infoTable',
                url: ctx + "/prj/finalAuditLedger/tableData",
                where: data,
                page: true,
                even: true,
                cols: tableData,
                done: function (res) {
                    //表头颜色一致
                    $('.layui-table-header .layui-table').removeAttr("lay-even");
                    $('.layui-table-header .layui-table').attr("class", 'layui-table lay-even');
                }
            });
        }

        // 查询条件
        function conditionShow() {
            // 项目名称
            var projectName = document.getElementById("projectName").value;
            // 项目编号
            var projectCode = document.getElementById("projectCode").value;
            var TASK_NUMBER = document.getElementById("TASK_NUMBER").value;
            var TASK_NAME = document.getElementById("TASK_NAME").value;
            // var STAFF_NAME = document.getElementById("STAFF_NAME").value;
            var FINAL_ACCOUNT_CODE = document.getElementById("FINAL_ACCOUNT_CODE").value;
            // 审计方式
            var auditTypeEnumId = document.getElementById("auditTypeEnumId").value;
            var FIELD_CHECK = document.getElementById("FIELD_CHECK").value;
            var SEND_AUDIT_DATE = document.getElementById("SEND_AUDIT_DATE").value;
            var AUDIT_MONTH = document.getElementById("AUDIT_MONTH").value;
            var SEND_REPORT_DATE = document.getElementById("SEND_REPORT_DATE").value;
            var AUDIT_COMP_DATE = document.getElementById("AUDIT_COMP_DATE").value;
            var ADJUST_FLAG = document.getElementById("ADJUST_FLAG").value;
            // 审计分部
            var auditDevisionName = document.getElementById("auditDevisionName").value;
            var auditDevisionArea = document.getElementById("auditDevisionArea").value;
            // 专业类别
            var professionCategory = getCheckedSelect(categoryGroup, "param");
            // 送审部门
            // var deptName = document.getElementById("deptName").value;
            // 送审单位
            // var trailAreaName = document.getElementById("trailAreaName").value;
            var data = {
                reportId: 220001,
                projectName: projectName,// 项目名称
                projectCode: projectCode,// 项目编号
                TASK_NUMBER: TASK_NUMBER,// 任务编号
                TASK_NAME: TASK_NAME,// 任务名称
                // STAFF_NAME: STAFF_NAME,// 任务名称
                FINAL_ACCOUNT_CODE: FINAL_ACCOUNT_CODE,// 任务名称
                auditTypeEnumId: auditTypeEnumId,// 审计方式
                ADJUST_FLAG: ADJUST_FLAG,// 撤销状态
                FIELD_CHECK: FIELD_CHECK,
                SEND_AUDIT_DATE: SEND_AUDIT_DATE,
                AUDIT_MONTH: AUDIT_MONTH,
                SEND_REPORT_DATE: SEND_REPORT_DATE,
                AUDIT_COMP_DATE: AUDIT_COMP_DATE,
                auditDevisionName: auditDevisionName,// 省分审计分部
                auditDevisionArea: auditDevisionArea,//地市审计分部
                professionCategory: professionCategory,
                // deptName: deptName,// 送审部门
                // trailAreaName: trailAreaName// 送审单位
            };
            return data;
        }

        // 查询
        $('#searchBtn').click(function () {
            showData();
        });

        // 加载省分审计分部下拉列表
        $.ajax({
            url: ctx + "/prj/auditInfo/auditDevision"
            , dataType: "json"
            , type: "POST"
            , success: function (data) {
                if (data.length > 1) {
                    var option = new Option();
                    $("#auditDevisionName").append(option);
                    $.each(data, function (index, obj) {
                        var option = new Option(obj.AUDIT_NAME, obj.AUDIT_ID);
                        $("#auditDevisionName").append(option);
                        form.render('select');
                    })

                    if(auditDevision){
                        $("#auditDevisionName").val(auditDevision);
                        auditDevision = ''
                        getAuditName();
                        form.render('select');
                    }
                } else if (data.length == 1) {
                    var option = new Option(data[0].AUDIT_NAME, data[0].AUDIT_ID);
                    $("#auditDevisionName").append(option);
                    form.render('select');
                    getAuditName();
                } else {
                    // 展示当前地市审计分部
                    getOrgGrade();
                }
            }
            , error: function (e) {
                console.info('加载省分审计分部下拉列表出错:' + e.info);
            }
        });

        // 加载地市审计分部下拉列表
        function getAuditName() {
            $("#auditDevisionArea").empty();
            form.render('select');
            var orgId = document.getElementById("auditDevisionName").value;
            $.ajax({
                url: ctx + "/prj/auditInfo/getAuditName/" + orgId
                , dataType: "json"
                , type: "POST"
                , success: function (data) {
                    var option = new Option();
                    $("#auditDevisionArea").append(option);
                    $.each(data, function (index, obj) {
                        var option = new Option(obj.DEVISION_NAME, obj.AUDIT_ID);
                        $("#auditDevisionArea").append(option);
                        form.render('select');
                    })
                    if(auditDevisionArea){
                        $("#auditDevisionArea").val(auditDevisionArea);
                        form.render('select');
                        auditDevisionArea = ''
                        showData()
                    }else{
                        showData()
                    }
                }
                , error: function (e) {
                    console.info('加载地市审计分部下拉列表出错:' + e.info);
                }
            })
        }

        // 省分审计分部下拉框触发
        form.on('select(auditDevisionName)', function (data) {
            $("#AUDIT_ID").empty();
            getAuditName();
        });


        // 审结账期
        laydate.render({
            elem: '#AUDIT_MONTH'
            , type: 'month'
            , range: '~'
            , format: 'yyyyMM'
        });

        laydate.render({
            elem: '#SEND_AUDIT_DATE'
            // , type: 'month'
            , range: '~'
            // , format: 'yyyyMM'
        });

        // 接审时间
        laydate.render({
            elem: '#SEND_REPORT_DATE'
            // , type: 'month'
            , range: '~'
            // , format: 'yyyyMM'
        });

        // 审结时间
        laydate.render({
            elem: '#AUDIT_COMP_DATE'
            // , type: 'month'
            , range: '~'
            // , format: 'yyyyMM'
        });



        $(function () {
            //更多查询
            $('.btnMore').on('click', function () {
                var me = $(this), childDwon = me.children('.slide-down'), childUp = me.children('.slide-up');
                if (childDwon.hasClass('none')) {
                    childDwon.removeClass('none');
                    childUp.addClass('none');
                    me.find('span').text("搜索更多");
                    $('.search-more').stop().hide();
                } else {
                    childDwon.addClass('none');
                    childUp.removeClass('none');
                    me.find('span').text("收起更多");
                    $('.search-more').stop().show();
                }
            });
        });

        if (auditTypeEnumId) {
            // 回显审计方式下拉框
            $("#auditTypeEnumId").val(auditTypeEnumId);
            layui.form.render('select'); // 重新渲染 select
        }

        if (stratYearMonth) {
            // 回显审计方式下拉框
            $("#AUDIT_MONTH").val(stratYearMonth + " ~ " + endYearMonth);
            layui.form.render('select'); // 重新渲染 select
        }

        if (professionCategoryInput) {
            // 回显审计方式下拉框
            var comName = decodeURI(getUrlParam("professionCategoryInput"));
            $("#professionCategoryInput").val(comName);
            // $("#professionCategoryInput").parent().attr("title", comName);
            layui.form.render('select'); // 重新渲染 select
        }
        if (professionCategory) {
            var comName = decodeURI(getUrlParam("professionCategory"));
            $("#tagmodelprofessionCategory").val(comName);
        }

        /**
         * 项目专业类别开始-----------------------------------------------------------------
         * @param e
         */
        var categoryGroup = [];
        // 返回字符串 arrayToStr
        window.printStr = function (arr) {
            var str = [];
            $.each(arr, function (index, item) {
                str.push(item.name);
            })
            return str.join("，")
        };
        window.setDefaultChecked = function (arr) {
            for (var i = 0; i < arr.length; i++) {
                arr[i].checked = false;
            }
        };
        // 项目专业打开与关闭
        window.professionCategoryClick = function (e) {
            e.stopPropagation(); // 事件冒泡
            var type = $(e.target).hasClass("new-confirm") ? false : true;
            if (type) {
                $('#tagmodelprofessionCategory').show();
            } else {
                showSelectCondition();
                $('#tagmodelprofessionCategory').hide();
            }
        };
        $('#professionCategorySelect').on('click', '.checkbox-li', function () {
            var index = $(this).data("index");
            categoryGroup[index].checked = !categoryGroup[index].checked;
            rendercategoryGroup(categoryGroup);
        });
        // 获取项目专业列表
        window.querycategoryGroup = function () {
            $.ajax({
                url: ctx + "/prj/endAccountAuditReport/queryProfession",
                dataType: "JSON",
                type: "POST",
                data: JSON.stringify({}),
                contentType: "application/json;charset=UTF-8",
                success: function (res) {
                    if (res.httpCode == 200) {
                        setDefaultChecked(res.data);
                        var professionArray = professionCategory.split(',');
                        $.each(res.data, function (index, item) {
                            item.className = "monitor_group";
                            // 重置checked状态
                            item.checked = false;

                            // IE兼容方案：遍历professionArray检查匹配项
                            for (var i = 0; i < professionArray.length; i++) {
                                if (item.code == professionArray[i]) {
                                    item.checked = true;
                                    break; // 匹配后跳出当前循环
                                }
                            }
                        });

                        categoryGroup = res.data;
                        console.log('professionCategory', professionArray);
                        console.log('categoryGroup', categoryGroup);
                        rendercategoryGroup(res.data);
                    }
                },
                error: function (e) {
                    console.info('加载项目专业下拉列表出错:' + e.info);
                }
            });
        };
        window.rendercategoryGroup = function (data) {
            lp(tpl, data, $("#profession-category-tpl").html(), $('#professionCategorySelect'));
        };
        window.unCheckList = function (arr) {
            $.each(arr, function (index, item) {
                item.checked = false;
            });
        }
        window.showSelectCondition = function () {
            var tmp1 = getCheckedSelect(categoryGroup);
            var str;
            if (tmp1.length) {
                str = printStr(tmp1);
                $("#professionCategoryInput").val(str);
                $("#professionCategoryInput").parent().attr("title", str);
            } else {
                $("#professionCategoryInput").val("点击选择项目专业");
            }

        };
        // 获取选中的
        function getCheckedSelect (arr, type) {
            var tmp = [];
            var item;
            for (var i = 0; i < arr.length; i++) {
                item = arr[i];
                if (item.checked) {
                    if (type !== "param") {
                        tmp.push({
                            name: item.codeText || item.CODETEXT,
                            code: item.code || item.CODE,
                            class: item.className
                        });
                    } else {
                        tmp.push(item.code || item.CODE);
                    }
                }
            }
            return tmp;
        };
        querycategoryGroup();
        // 项目专业类别结束-----------------------------------------------------------------

    });
</script>

</html>
